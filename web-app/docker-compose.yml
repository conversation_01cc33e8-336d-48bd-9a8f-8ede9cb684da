services:
  postgres:
    restart: always
    image: kartoza/postgis
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: root
      POSTGRES_DB: test_db
    # healthcheck:
    #   test: ['CMD-SHELL', 'pg_isready -U admin -d test_db']
    #   interval: 10s
    #   retries: 5
    #   start_period: 30s
    #   timeout: 10s
    networks:
      - app

  minio:
    image: minio/minio:latest
    ports:
      - '9000:9000' # For connecting to Minio API
      - '9001:9001' # For viewing Minio Web Console
    volumes:
      - mdata:/data
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password
    command: server --console-address ":9001" /data
    networks:
      - app

  createbuckets:
    image: minio/mc
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      sleep 10;
      /usr/bin/mc alias set m1 http://minio:9000 admin password --api S3v4;
      /usr/bin/mc mb -p m1/media;
      /usr/bin/mc mb -p m1/dice;
      /usr/bin/mc mb -p m1/resident-advisor;
      /usr/bin/mc mb -p m1/sanity;
      /usr/bin/mc mb -p m1/songkick;
      exit 0;
      "
    networks:
      - app

  # pgclient:
  #   image: ubuntu:latest
  #   network_mode: host
  #   stdin_open: true
  #   tty: true

networks:
  app:
    driver: bridge

volumes:
  mdata:
  pgdata:
