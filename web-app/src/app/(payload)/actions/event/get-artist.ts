import { NextResponse } from 'next/server'
import type { PayloadRequest } from 'payload'
import { getArtistData } from '@/lib/spotify/handle-artist'


export async function getArtist(req: PayloadRequest) {
    if (!req.user) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const id = req.searchParams.get('id')
    if (!id) {
        return NextResponse.json({ error: '`id` query param is required' }, { status: 400 })
    }

    try {
        const result = await getArtistData(id, req.payload, false)
        return NextResponse.json(result)
    } catch (err: any) {
        console.error(err)
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
}
