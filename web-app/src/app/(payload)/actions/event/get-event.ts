import { NextResponse } from 'next/server'
import type { PayloadRequest } from 'payload'
import { getEventData } from '@/lib/dice/handle-event'

export async function getEvent(req: PayloadRequest) {
  if (!req.user) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
  }
  const id = req.searchParams.get('id')
  if (!id) {
    return NextResponse.json({ error: '`id` query param is required' }, { status: 400 })
  }

  try {
    const result = await getEventData(id, req)
    return NextResponse.json(result)
  } catch (err: any) {
    console.error('getEvent error:', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
