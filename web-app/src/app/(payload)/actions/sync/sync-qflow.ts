import { NextResponse } from 'next/server'
import payload from 'payload'
import {
    loginQFlow,
    fetchAllEvents,
    filterRecent,
    fetchAttendees,
} from '@/lib/qflow/qflow-client'

export async function sync() {
    await loginQFlow(process.env.QFLOW_USER!, process.env.QFLOW_PASS!);

    const events = await fetchAllEvents()
    //   const recent = filterRecent(events, 1)

    for (const evt of events) {
        const attendees = await fetchAttendees(evt.id)
        for (const a of attendees) {
            await payload.update({
                collection: 'tickets',
                where: { code: { equals: a.ticketId } },
                data: {
                    admittedDate: a.checkInTime,
                },
            })
        }
    }

    return NextResponse.json({
        syncedEvents: events.length,
        timestamp: new Date().toISOString(),
    })
}
