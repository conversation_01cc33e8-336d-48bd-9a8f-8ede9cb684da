import { fetchXmlWithRetry } from '@/lib/sync/lib'
import type { PayloadRequest } from 'payload'
import * as cheerio from 'cheerio'
import { promisify } from 'util'
import zlib from 'zlib'
import { NextResponse } from 'next/server'
import UserAgent from 'user-agents'

const gunzip = promisify(zlib.gunzip)
const SONGKICK_SITEMAP_INDEX = 'https://www.songkick.com/sitemap.xml'

async function fetchGzXml(url: string): Promise<string> {
  const res = await fetch(url, { headers: { 'accept-encoding': 'gzip' } })
  if (!res.ok) throw new Error(`Failed ${url}: ${res.status}`)
  const buf = Buffer.from(await res.arrayBuffer())
  return (await gunzip(buf)).toString('utf8')
}

async function fetchHtml(url: string): Promise<string> {
  const res = await fetch(url, { headers: { 'User-Agent': new UserAgent().toString() } })
  if (!res.ok) throw new Error(`Failed ${url}: ${res.status}`)
  return res.text()
}

async function saveHtmlToObjectStorage(html: string) {
  /* TODO */
}

async function getConcertSitemapUrls(indexUrl: string): Promise<string[]> {
  const xml = await fetchXmlWithRetry(indexUrl)
  const $ = cheerio.load(xml, { xmlMode: true })
  return $('sitemap > loc')
    .map((_, el) => $(el).text())
    .get()
    .filter((u) => u.includes('/concerts.'))
}

async function collectConcertUrls(indexUrl: string): Promise<Set<string>> {
  const sitemapUrls = await getConcertSitemapUrls(indexUrl)
  const urls = new Set<string>()
  for (const mapUrl of sitemapUrls) {
    try {
      const xml = await fetchGzXml(mapUrl)
      const $ = cheerio.load(xml, { xmlMode: true })
      $('url > loc').each((_, el) => { urls.add($(el).text()); })
    } catch { }
  }
  return urls
}

export async function syncSongKickEvents(req: PayloadRequest) {
  await req.payload.jobs.queue({
    workflow: 'syncSongkickWorkflow',
    queue: 'default',
    input: undefined
  })
  return NextResponse.json({ message: 'Synced Songkick concerts' }, { status: 200 })
}

export async function crawlSongKickEvents(req: PayloadRequest) {
  await req.payload.jobs.queue({
    workflow: 'crawlSongkickWorkflow',
    queue: 'default',
    input: undefined
  })
  return NextResponse.json({ message: 'Synced Songkick concerts' }, { status: 200 })
}

export async function crawlRAEvents(req: PayloadRequest) {
  
  await req.payload.jobs.queue({
    workflow: 'crawlRAWorkflow',
    queue: 'default',
    input: undefined
  })
  return NextResponse.json({ message: 'crawlRAWorkflow' }, { status: 200 })
}
