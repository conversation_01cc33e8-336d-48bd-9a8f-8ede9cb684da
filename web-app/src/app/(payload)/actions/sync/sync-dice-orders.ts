import { NextResponse } from 'next/server'
import type { PayloadRequest } from 'payload'

export async function SyncDiceOrders(req: PayloadRequest) {
	// if (!req.user) {
	// 	return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
	// }
	await req.payload.jobs.queue({
	    workflow: 'syncDiceOrdersWorkflow', 
	    queue: 'default',
	    input: undefined
	})
	try {
		// const result = await getAllOrders(req)
		return NextResponse.json({ message: "Successfully synced" }, { status: 200 })

	} catch (error) {
		console.error('Sync orders error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}