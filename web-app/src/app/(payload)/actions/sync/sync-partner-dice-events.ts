import { WorkflowOrchestrator } from '@/lib/workflow-orchestrator/service'
import { NextResponse } from 'next/server'
import { PayloadRequest } from 'payload'

export async function crawlDicekWorkflow(req: PayloadRequest) {
    await req.payload.jobs.queue({
        workflow: 'crawlDicekWorkflow',
        queue: 'default',
        input: undefined
    })
    return NextResponse.json({ message: "Successfully crawled DICE events" }, { status: 200 })
}
export async function syncDiceWorkflow(req: PayloadRequest) {
    await req.payload.jobs.queue({
        workflow: 'syncDiceWorkflow',
        queue: 'default',
        input: undefined
    })
    return NextResponse.json({ message: "Successfully synced DICE events" }, { status: 200 })
}
export async function syncDicePartnersEvents(req: PayloadRequest) {
    await req.payload.jobs.queue({
        workflow: 'syncDicePartnerEventsWorkflow',
        queue: 'default',
        input: undefined
    })
    return NextResponse.json({ message: "Successfully synced DICE partner events" }, { status: 200 })
}

export async function startWorkflowOrchestrator(req: PayloadRequest) {
    const orchestrator = new WorkflowOrchestrator(req.payload, { continueOnError: false })

    orchestrator.addStage([
        { slug: 'crawlSanity' },
        { slug: 'crawlRAWorkflow' },
        { slug: 'crawlDicekWorkflow' },
        { slug: 'crawlSongkickWorkflow' },
        { slug: 'syncDicePartnerEventsWorkflow', needs: ['importSanityDataWorkflow'] },
        { slug: 'syncPlanetscaleUsersWorkflow', needs: ['importSanityDataWorkflow'] },
        {
            slug: 'syncPlanetscalePresaleRegistrationsWorkflow',
            needs: ['syncPlanetscaleUsersWorkflow'],
        },
        { slug: 'importSanityDataWorkflow', needs: ['crawlSanity'] },
        { slug: 'syncDiceWorkflow', needs: ['crawlDicekWorkflow', 'importSanityDataWorkflow'] },
        {
            slug: 'syncSongkickWorkflow',
            needs: ['crawlSongkickWorkflow', 'importSanityDataWorkflow'],
        },
        { slug: 'syncDiceOrdersWorkflow', needs: ['syncDicePartnerEventsWorkflow'] },
        { slug: 'syncQFlowWorkflow', needs: ['syncDiceOrdersWorkflow'] },
    ])

    orchestrator
        .run()
        .then((jobIdsMap) => {
            console.log('All stages passed, JOB ID of each workflow:', jobIdsMap)
        })
        .catch((err) => {
            console.error('The Pipeline is not complete, some stage has fallen:', err)
        })
    return NextResponse.json({ message: "Started workflow orchestrator" }, { status: 200 })

}