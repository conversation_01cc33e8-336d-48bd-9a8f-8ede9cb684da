import { NextResponse } from "next/server"
import { PayloadRequest } from "payload"

export async function syncSanity(req: PayloadRequest) {
  await req.payload.jobs.queue({
    workflow: 'importSanityDataWorkflow',
    queue: 'default',
    input: undefined
  })
  return NextResponse.json({ message: 'Synced Sanity datas' }, { status: 200 })
}
export async function crawlSanity(req: PayloadRequest) {
  await req.payload.jobs.queue({
    workflow: 'crawlSanity',
    queue: 'default',
    input: undefined
  })
  return NextResponse.json({ message: 'Synced Sanity datas' }, { status: 200 })
}