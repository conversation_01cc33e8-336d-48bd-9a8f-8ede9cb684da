// app/api/spotify/tracks/route.ts
import { NextRequest, NextResponse } from 'next/server';
import SpotifyService from '@/lib/spotify/service';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const artistId = searchParams.get('id')?.trim() || '';
  const market = searchParams.get('market')?.trim() || 'US';

  if (!artistId) {
    return NextResponse.json(
      { error: 'id (artist) parameter is required' },
      { status: 400 }
    );
  }

  try {
    const tracks = await SpotifyService.getArtistTopTracks(artistId, market);
    return NextResponse.json(tracks);
  } catch (err) {
    console.error('Spotify top tracks error:', err);
    return NextResponse.json(
      { message: 'Internal Spotify error', error: (err as Error).message },
      { status: 500 }
    );
  }
}
