import YouTubeService from '@/lib/youtube/service'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const q = searchParams.get('q')?.trim() || ''
  if (!q) {
    return NextResponse.json({ error: 'q parameter is required' }, { status: 400 })
  }

  try {
    const data = await YouTubeService.getPopularVideosByHandle(q)
    return NextResponse.json(data)
  } catch (error) {
    return NextResponse.json({ message: 'Internal YouTube  error', error }, { status: 500 })
  }
}
