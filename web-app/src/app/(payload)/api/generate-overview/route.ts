import { GeminiService } from '@/lib/gemini/service';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
    try {
        const { prompt } = await req.json();
        const aiRes = await new GeminiService().generate(prompt, true);
        const text = aiRes.candidates?.[0]?.content?.parts?.[0]?.text || '';
        return NextResponse.json({ text });
    } catch (e) {
        console.error('[generate-overview]', e);
        return NextResponse.json({ error: 'Generation failed' }, { status: 500 });
    }
}
