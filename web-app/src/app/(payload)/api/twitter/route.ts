import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get('url');
  if (!url) {
    return NextResponse.json({ error: 'Missing url parameter' }, { status: 400 });
  }

  try {
    const res = await fetch(
      `https://publish.twitter.com/oembed`
      + `?url=${encodeURIComponent(url)}`
      + `&omit_script=true`
      + `&theme=dark`
    );
    if (!res.ok) {
      const text = await res.text();
      return NextResponse.json(
        { error: `Twitter oEmbed returned ${res.status}`, details: text },
        { status: res.status }
      );
    }
    const data = await res.json();
    return NextResponse.json(data);
  } catch {
    return NextResponse.json({ error: 'Failed to fetch oEmbed' }, { status: 500 });
  }
}
