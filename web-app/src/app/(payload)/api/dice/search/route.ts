import DiceService from "@/lib/dice/service";
import { NextRequest, NextResponse } from "next/server";

export const GET = async (req: NextRequest) => {
  try {
	const { searchParams } = new URL(req.url)
	const q = searchParams.get('q')?.trim() || ''
	if (!q) {
		return NextResponse.json({ error: 'q parameter is required' }, { status: 400 })
	}
	const dice = new DiceService(process.env.DICE_PARTNER_API_TOKEN || '', process.env.DICE_PARTNER_API_ENDPOINT || 'https://partners-endpoint.dice.fm/graphql')
	const data = await dice.searchEventsByName(q)
	return NextResponse.json(data)
  } catch (error) {
	return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}