import type { WorkflowConfig } from 'payload'
import mysql, { RowDataPacket } from 'mysql2'
import { access } from '@/lib/planetscale/connection'

export const syncPlanetscalePresaleRegistrationsWorkflow: WorkflowConfig<'syncPlanetscalePresaleRegistrationsWorkflow'> =
  {
    slug: 'syncPlanetscalePresaleRegistrationsWorkflow',
    handler: async ({ tasks }) => {
      const conn = mysql.createConnection(access)
      conn.query<RowDataPacket[]>('SELECT * FROM PresaleRegistration;', async (_err, rows) => {
        for (const [i, row] of rows.entries()) {
          await tasks.importPlanetscalePresaleRegistration(
            `import-planetscale-presale-registration-${i}`,
            { input: { row } },
          )
        }
      })
    },

    label: 'Sync Planetscale presale registrations',
  }
