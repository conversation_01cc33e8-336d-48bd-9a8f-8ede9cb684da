// workflows/syncDiceOrdersWorkflow.ts
import type { WorkflowConfig } from 'payload'
import { Order, PageInfo } from '@/lib/dice/schema-types'
import { CryptoService } from '@/lib/encryption/service'

export const syncDiceOrdersWorkflow: WorkflowConfig<'syncDiceOrdersWorkflow'> = {
  slug: 'syncDiceOrdersWorkflow',
  label: 'Workflow — sync Dice orders',

  handler: async ({ tasks, req }) => {
    const { docs: eventOrganizers } = await req.payload.find({
      collection: 'eventOrganizers',
      where: {
        'diceCredentials.DICE_PARTNER_API_TOKEN': {
          exists: true
        }
      },
      overrideAccess: true
    })
    const cryptoSvc = new CryptoService()

    for (const eventOrganizer of eventOrganizers) {
      const token = cryptoSvc.decrypt(eventOrganizer.diceCredentials!.DICE_PARTNER_API_TOKEN) as string;
      if (!token) {
        console.warn(`No DICE_PARTNER_API_TOKEN for event organizer ${eventOrganizer.id}`)
        continue
      }
      let after: string | undefined = undefined
      const first = 2500
      let page = 0
      do {
        console.log(`Fetching page ${page}, cursor=${after ?? 'first'}`)

        const { orders, pageInfo } = (await tasks.fetchOrders(
          `fetchOrders-${after ?? 'first'}-${page}`,
          { input: { first, after, token } }
        )) as { orders: Order[]; pageInfo: PageInfo }

        if (!orders?.length) break

        for (const orderData of orders) {
          if (!orderData.id) continue
          const existingOrders = await req.payload.find({
            collection: 'orders',
            where: { externalDiceId: { equals: orderData.id } },
            limit: 1,
          })
          if (existingOrders.docs.length) {
            continue
          }

          const { eventDocId } = await tasks.findOrCreateEvent(
            `findOrCreateEvent-${orderData.event?.id ?? 'noEvent'}`,
            { input: { event: orderData.event as any } }
          )

          if (!eventDocId) {
            continue
          }

          const ticketDocIds: number[] = []
          for (const ticket of orderData.tickets ?? []) {
            if (!ticket || !ticket.priceTier?.id) {
              continue
            }

            const ticketTypesFound = await req.payload.find({
              collection: 'ticketTypes',
              where: { 'externalDiceId.priceTierId': { equals: ticket.priceTier.id } },
              limit: 1,
            })
            const ticketTypeDoc = ticketTypesFound.docs[0]
            if (!ticketTypeDoc) {
              continue
            }
            const { ticketDocId } = (await tasks.findOrCreateTicket(
              `findOrCreateTicket-${ticket.id}`,
              {
                input: {
                  ticket: {
                    id: ticket.id,
                    priceTierId: ticket.priceTier.id,
                    code: ticket.code,
                    purchasedAt: orderData.purchasedAt
                  },
                  ticketTypeId: ticketTypeDoc.id,
                },
              }
            )) as { ticketDocId: number }

            ticketDocIds.push(ticketDocId)

            await req.payload.update({
              collection: 'tickets',
              id: ticketDocId,
              data: { total: ticketTypeDoc.total ?? 0 },
            })
          }

          await tasks.createOrder(
            `createOrder-${orderData.id}`,
            {
              input: {
                externalDiceId: orderData.id,
                eventDocId,
                fanEmail: orderData.fan?.email || '',
                ticketDocIds,
              },
            }
          )
        }

        after = pageInfo.hasNextPage ? pageInfo.endCursor! : undefined
        page += 1
      } while (after)
    }
  },
}
