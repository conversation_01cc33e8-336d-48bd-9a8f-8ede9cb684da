import type { WorkflowConfig } from 'payload'

export const syncQFlowWorkflow: WorkflowConfig<'syncQFlowWorkflow'> = {
  slug: 'syncQFlowWorkflow',
  handler: async ({ tasks }) => {
    await tasks.qflowLogin('login', {})        

    const { events } = await tasks.qflowFetchEvents('events', {}) 

    if (events && Array.isArray(events)) {
      for (const [i, ev] of events.entries()) {         
        const event = ev as { id: string }
        await tasks.qflowSyncAttendees(`evt-${i}`, { input: { eventId: event.id } })
      }
    }
  },
  label: 'Sync Q-Flow → Payload',
}
