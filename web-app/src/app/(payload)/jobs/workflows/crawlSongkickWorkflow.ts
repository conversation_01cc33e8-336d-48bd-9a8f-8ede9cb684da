import type { WorkflowConfig } from 'payload'

export const crawlSongkickWorkflow: WorkflowConfig<'crawlSongkickWorkflow'> = {
  slug: 'crawlSongkickWorkflow',
  handler: async ({ tasks }) => {
    const { sitemapUrls } = await tasks.getConcertSitemapUrls('1', {
      input: {
        url: 'https://www.songkick.com/sitemap.xml',
      },
    })

    const { urls } = await tasks.collectConcertUrls('2', {
      input: {
        sitemapUrls,
      },
    })

    for (const url of urls) {
      await tasks.fetchHtmlAndSave(`fetchHtml-save-${url}`, {
        input: {
          url,
          bucket: 'songkick',
        },
      })
    }
  },
  label: 'crawl Songkick → Payload',
}
