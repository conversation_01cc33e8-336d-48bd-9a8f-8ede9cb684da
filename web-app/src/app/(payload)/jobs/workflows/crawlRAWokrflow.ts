import type { WorkflowConfig } from 'payload'

export const crawlRAWorkflow: WorkflowConfig<'crawlRAWorkflow'> = {
    slug: 'crawlRAWorkflow',
    handler: async ({ tasks }) => {
        const { sitemapUrls } = await tasks.getSitemapUrls('1', {
            input: {
                url: 'https://ra.co/sitemap.xml'
            }
        })

        for (const sitemapUrl of sitemapUrls) {
            const { eventUrls } = await tasks.extractEventUrls(`fetchHtml-save-${sitemapUrl}`, {
                input: {
                    sitemapUrl,
                    filterText: `/events/`
                }
            })

            for (const url of eventUrls) {
                await tasks.fetchHtmlAndSave(`fetchHtml-save-${url}`, {
                    input: {
                        url,
                        bucket: 'resident-advisor'
                    }
                })
            }
        }
    },
    label: 'crawl RA.CO → Payload',
}
