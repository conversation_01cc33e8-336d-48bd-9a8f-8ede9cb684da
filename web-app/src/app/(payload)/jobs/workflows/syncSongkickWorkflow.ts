import { getFromObjectStorage } from '@/lib/s3/client'
import { extractLdJson } from '@/lib/songkick/lib'
import { MusicEvent } from '@/lib/songkick/transformers'
import type { WorkflowConfig } from 'payload'

export const syncSongkickWorkflow: WorkflowConfig<'syncSongkickWorkflow'> = {
    slug: 'syncSongkickWorkflow',
    handler: async ({ tasks }) => {
        const bucket = 'songkick'
        const { keys } = await tasks.extractKeysByBucket('extractKeysByBucket', {
            input: { bucket }
        })
        for (const key of keys) {
            const html = await getFromObjectStorage(key, bucket)
            const jsonLdArray = extractLdJson(html, true)
            if (jsonLdArray.length === 0) continue
            const jsonEvent: MusicEvent = jsonLdArray[0]
            await tasks.handleSongkickEvent(`${key}`, {
                input: { jsonEvent: jsonEvent as any }
            })
        }
    },
    label: 'sync Songkick → Payload',
}
