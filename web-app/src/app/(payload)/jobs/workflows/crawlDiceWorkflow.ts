import type { WorkflowConfig } from 'payload'

export const crawlDicekWorkflow: WorkflowConfig<'crawlDicekWorkflow'> = {
    slug: 'crawlDicekWorkflow',
    handler: async ({ tasks }) => {
        const { sitemapUrls } = await tasks.getSitemapUrls('1', {
            input: {
                url: 'https://dice.fm/sitemaps/sitemap.xml'
            }
        })
        const eventUrls = []
        for (const sitemapUrl of sitemapUrls) {
            const { eventUrls: urls } = await tasks.extractEventUrls(`${sitemapUrl}`, {
                input: {
                    sitemapUrl,
                    filterText: `/event/`
                }
            })
            eventUrls.push(...urls)
        }

        for (const url of eventUrls) {
            await tasks.fetchHtmlAndSave(`fetchHtml-save-${url}`, {
                input: {
                    url,
                    bucket: 'dice'
                }
            })
        }
    },
    label: 'Sync DICE events via sitemap',
}
