import type { WorkflowConfig } from 'payload'
import mysql, { RowDataPacket } from 'mysql2'
import { access } from '@/lib/planetscale/connection'

export const syncPlanetscaleUsersWorkflow: WorkflowConfig<'syncPlanetscaleUsersWorkflow'> = {
  slug: 'syncPlanetscaleUsersWorkflow',
  handler: async ({ tasks }) => {
    const conn = mysql.createConnection(access)
    conn.query<RowDataPacket[]>('SELECT * FROM User;', async (_err, rows) => {
      for (const [i, row] of rows.entries()) {
        await tasks.importPlanetscaleUser(`import-planetscale-user-${i}`, { input: { row } })
      }
    })
  },

  label: 'Sync Planetscale users',
}
