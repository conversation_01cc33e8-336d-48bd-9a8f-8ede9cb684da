import { getFromObjectStorage, listAllKeys } from '@/lib/s3/client'
import { extractLdJson } from '@/lib/songkick/lib'
import { MusicEvent } from '@/lib/songkick/transformers'
import type { WorkflowConfig } from 'payload'

export const syncDiceWorkflow: WorkflowConfig<'syncDiceWorkflow'> = {
    slug: 'syncDiceWorkflow',
    handler: async ({ tasks }) => {
        const bucket = 'dice'
        const { keys } = await tasks.extractKeysByBucket('extractKeysByBucket', {
            input: { bucket }
        })
        for (const key of keys) {
            const html = await getFromObjectStorage(key, bucket)
            const { id } = await tasks.extractRetailerId(`${key}`, {
                input: { html }
            })
            await tasks.importDiceEvent(`${id}`, {
                input: { eventIdLive: id },
            })
        }
    },
    label: 'sync Songkick → Payload',
}
