import type { WorkflowConfig } from 'payload'
import DiceService from '@/lib/dice/service'
import { CryptoService } from '@/lib/encryption/service'

export const syncDicePartnerEventsWorkflow: WorkflowConfig<'syncDicePartnerEventsWorkflow'> = {
  slug: 'syncDicePartnerEventsWorkflow',
  handler: async ({ req, tasks }) => {
    const { docs: eventOrganizers } = await req.payload.find({
      collection: 'eventOrganizers',
      where: {
        'diceCredentials.DICE_PARTNER_API_TOKEN': {
          exists: true
        }
      },
      overrideAccess: true
    })
    const cryptoSvc = new CryptoService()

    for (const eventOrganizer of eventOrganizers) {
      const token = cryptoSvc.decrypt(eventOrganizer.diceCredentials!.DICE_PARTNER_API_TOKEN) as string;
      const email = cryptoSvc.decrypt(eventOrganizer.diceCredentials!.login) as string;
      const password = cryptoSvc.decrypt(eventOrganizer.diceCredentials!.password) as string;
      let dicePToken: string = ''
      if (email && password) {
        const authUrl = 'https://p-api.dice.fm/api/session';
        const result = await fetch(authUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password }),
        });

        if (result.ok) {
          const body = (await result.json()) as { access_token: string };
          dicePToken = body.access_token;
        }
      }
      const dice = new DiceService(token)

      const { viewer } = await dice.fetchEventsPage({ first: 10_000 })
      const edges = viewer?.events?.edges ?? []

      for (let i = 0; i < edges.length; i++) {
        const event = edges[i]?.node as any
        if (!event?.eventIdLive) continue

        const { docs } = await req.payload.find({
          collection: 'events',
          where: { externalDiceId: { equals: event.eventIdLive } },
          limit: 1,
          overrideAccess: true,
        })
        if (docs.length === 0) {

          let ticketTypesIds: number[] = []
          if (dicePToken) {
            const { ticketTypes } = await tasks.fetchEventWithTicketTypes(`${event.eventIdLive}-ticket-types`, {
              input: { eventId: event.eventIdLive, token: dicePToken },
            });

            if (!ticketTypes || !Array.isArray(ticketTypes)) continue
            const { ids } = await tasks.upsertTicketTypes(`upsert-ticket-type-${i}`, {
              input: { ticketTypes: ticketTypes as any, },
            })
            ticketTypesIds = ids
          }

          if (ticketTypesIds.length > 0) {
            event.ticketTypes = ticketTypesIds.map(ticketType => ({ ticketType}))
          }
          const { eventDocId } = await tasks.importDiceEvent(`${event.eventIdLive}`, {
            input: { eventIdLive: event.eventIdLive, event },
          })

        }
      }
    }
  },
  label: 'Queue jobs for all new DICE events',
}
