import { RowDataPacket } from 'mysql2'
import { TaskConfig } from 'payload'

export const importPlanetscaleUser: TaskConfig<'importPlanetscaleUser'> = {
  slug: 'importPlanetscaleUser',
  inputSchema: [{ name: 'row', type: 'json', required: true }],
  handler: async ({ req, input }) => {
    const { payload } = req
    const { row } = input
    const existing = (row as RowDataPacket).email
      ? (
          await payload.find({
            collection: 'fanUsers',
            where: {
              email: { equals: (row as RowDataPacket).email },
            },
          })
        ).docs[0]
      : undefined

    if (!existing && (row as RowDataPacket).email)
      await payload.create({
        collection: 'fanUsers',
        data: {
          firstName: (row as RowDataPacket).firstName ?? 'Not set',
          lastName: (row as RowDataPacket).lastName ?? 'Not set',
          email: (row as RowDataPacket).email ?? 'Not set',
          phoneNumber: (row as RowDataPacket).phoneNumber ?? '',
          phoneNumberCountryCode: (row as RowDataPacket).phoneNumberCountryCode ?? '',
          dateOfBirth: new Date((row as RowDataPacket).dateOfBirth).toISOString(),
          ethWallet: (row as RowDataPacket).walletAddress,
          createdAt: (row as RowDataPacket).createdAt,
          updatedAt: (row as RowDataPacket).updatedAt,
        },
      })

    return { output: {} }
  },
  retries: 2,
}
