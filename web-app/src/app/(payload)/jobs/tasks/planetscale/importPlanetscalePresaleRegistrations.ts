import { RowDataPacket } from 'mysql2'
import { TaskConfig } from 'payload'

export const importPlanetscalePresaleRegistration: TaskConfig<'importPlanetscalePresaleRegistration'> =
  {
    slug: 'importPlanetscalePresaleRegistration',
    inputSchema: [{ name: 'row', type: 'json', required: true }],
    handler: async ({ req, input }) => {
      const { payload } = req
      const { row } = input
      const event = (
        await payload.find({
          collection: 'events',
          where: {
            externalSanityId: { equals: (row as RowDataPacket).eventId },
          },
        })
      ).docs[0]

      let fan = (
        await payload.find({
          collection: 'fanUsers',
          where: {
            email: { equals: (row as RowDataPacket).email },
          },
        })
      ).docs[0]

      if (!fan)
        fan = await payload.create({
          collection: 'fanUsers',
          data: {
            firstName: (row as RowDataPacket).firstName ?? 'Not set',
            lastName: (row as RowDataPacket).lastName ?? 'Not set',
            email: (row as RowDataPacket).email ?? 'Not set',
            phoneNumber: (row as RowDataPacket).phoneNumber ?? '',
            phoneNumberCountryCode: (row as RowDataPacket).phoneNumberCountryCode ?? '',
            dateOfBirth: new Date((row as RowDataPacket).dateOfBirth).toISOString(),
          },
        })

      const existing =
        event && fan
          ? (
              await payload.find({
                collection: 'fanNotificationListners',
                where: {
                  and: [{ 'fan.email': { equals: fan.email } }, { event: { equals: event.id } }],
                },
              })
            ).docs[0]
          : undefined

      if (!existing && fan && event) {
        await payload.create({
          collection: 'fanNotificationListners',
          data: {
            type: 'eventPresale',
            event,
            fan,
            createdAt: (row as RowDataPacket).createdAt,
          },
        })
      }

      return { output: {} }
    },
    retries: 2,
  }
