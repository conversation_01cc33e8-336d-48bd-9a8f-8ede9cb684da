import { TicketType } from '@/payload-types';
import type { PayloadRequest, TaskConfig } from 'payload'

async function findTicketType(
    ticketTypeId: string,
    priceTierId: string,
    req: PayloadRequest
) {
    const res = await req.payload.find({
        collection: 'ticketTypes',
        where: {
            'externalDiceId.ticketTypeId': { equals: ticketTypeId },
            'externalDiceId.priceTierId': { equals: priceTierId },
        },
        limit: 1,
    });
    return res.docs[0] ?? null;
}

function mapTicketType(tt: any): TicketType[] {
    return (tt.priceTiers ?? []).map((tier: any) => ({
        externalDiceId: {
            ticketTypeId: tt.id,
            priceTierId: tier.id,
        },
        ticketTypeName: tt.name,
        description: tt.description ?? '',
        allocation: tt.allocation,
        archived: tt.archived,
        label: tier.name,
        faceValue: tier.priceBreakdown.faceValue,
        totalFee: tier.priceBreakdown.fees,
        fee: tier.priceBreakdown.breakdown.map((fee: any) => ({
            applicable: fee.applicable,
            amount: fee.amount,
            computed: fee.computed,
            type: fee.type,
            unit: fee.unit,
        })),
        total: tier.priceBreakdown.total,
        origin: 'dice',
    }));
}

export const upsertTicketTypesTask: TaskConfig<'upsertTicketTypes'> = {
    slug: 'upsertTicketTypes',
    inputSchema: [{
        name: 'ticketTypes', type: 'array', fields: [], required: true
    }
    ],
    outputSchema: [{
        name: 'ids', type: 'number', hasMany:true, required: true
    }],
    retries: 3,
    handler: async ({ input, req }) => {
        const ticketTypes = input.ticketTypes
        const ids: number[] = [];
        for (const ticketType of ticketTypes) {
            const docs = mapTicketType(ticketType);

            for (const doc of docs) {
                const { ticketTypeId, priceTierId } = doc.externalDiceId;

                const existing = await findTicketType(ticketTypeId, priceTierId, req);


                if (!existing) {
                    const result = await req.payload.create({
                        collection: 'ticketTypes',
                        data: doc,
                    });
                    ids.push(result.id);
                } else {
                    const result = await req.payload.update({
                        collection: 'ticketTypes',
                        id: existing.id,
                        data: doc,
                    });
                    ids.push(existing.id);
                }
            }
        }
        return { output: {ids} }
    },
    label: 'Dice → all event IDs',
}
