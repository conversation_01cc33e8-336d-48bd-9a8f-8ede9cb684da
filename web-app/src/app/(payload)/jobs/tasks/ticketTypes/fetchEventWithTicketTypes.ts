import type { TaskConfig } from 'payload'
import DicePService from '@/lib/dice-p/service'

export const fetchEventWithTicketTypesTask: TaskConfig<'fetchEventWithTicketTypes'> = {
  slug: 'fetchEventWithTicketTypes',
  retries: 3,
  inputSchema: [
    { name: 'token', type: 'text', required: true },
    { name: 'eventId', type: 'text', required: true },
  ],
  outputSchema: [
    { name: 'ticketTypes', type: 'json', required: true },
  ],
  handler: async ({ input }) => {
    const token = input.token as string;

    const dice = new DicePService(token);

    const result = await dice.getEventWithTicketTypes({ id: input.eventId });
    const event = result.viewer?.events?.edges?.[0]?.node;
    const ticketTypes = Array.isArray(event?.ticketTypes) ? event.ticketTypes : [];

    return { output: { ticketTypes } };
  },
  label: 'Dice → fetchEventWithTicketTypes (with pre-obtained token)',
}
