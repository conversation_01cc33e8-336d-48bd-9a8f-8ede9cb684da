import { uploadToObjectStorage } from '@/lib/s3/client'
import { getSanytiData, sanityCollections } from '@/lib/sanity/get-raw-data'
import { TaskConfig } from 'payload'

export const saveRawSanityData: TaskConfig<'saveRawSanityData'> = {
  slug: 'saveRawSanityData',
  inputSchema: [{ name: 'bucket', type: 'text', required: true }],
  handler: async ({ input }) => {
    for (const collection of sanityCollections) {
      const key = `sanity-${collection}`
      getSanytiData(collection).then(async (data) => {
        await uploadToObjectStorage(JSON.stringify(data), key, input.bucket, 'application/json')
      })
    }

    return { output: {} }
  },
  retries: 2,
}
