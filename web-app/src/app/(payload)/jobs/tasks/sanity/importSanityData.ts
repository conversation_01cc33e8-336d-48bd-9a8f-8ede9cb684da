import { sanityCollections } from '@/lib/sanity/get-raw-data'
import { resolveRefs } from '@/lib/sanity/seed-data'
import { IDMap, importToPayload } from '@/lib/sanity/transformers'
import { TaskConfig } from 'payload'
import { getFromObjectStorage } from '@/lib/s3/client'

export const importSanityData: TaskConfig<'importSanityData'> = {
  slug: 'importSanityData',
  inputSchema: [{ name: 'bucket', type: 'text', required: true }],
  handler: async ({ req, input }) => {
    const { payload } = req
    const allData: Record<string, any[]> = {}
    const idMap: IDMap = {}

    for (const collection of sanityCollections) {
      const rawSanityData = await getFromObjectStorage(`sanity-${collection}`, input.bucket)
      allData[collection] = JSON.parse(rawSanityData)
    }

    for (const collection of sanityCollections) {
      const items = allData[collection]

      for (const item of items || []) {
        if (!idMap[item._id]) {
          const resolved = await resolveRefs(item, idMap, allData, payload)
          const newId = await importToPayload(collection, resolved, idMap, payload, allData)
          if (newId) idMap[item._id] = newId
        }
      }
    }
    return { output: {} }
  },
  retries: 2,
}
