import type { TaskConfig } from 'payload'
import * as cheerio from 'cheerio'
import UserAgent from 'user-agents';

export const getSitemapUrlsTask: TaskConfig<'getSitemapUrls'> = {
    slug: 'getSitemapUrls',
    retries: 10,
    inputSchema: [{ name: 'url', type: 'text', required: true }],
    outputSchema: [{ name: 'sitemapUrls', type: 'text', hasMany: true, required: true, }],
    handler: async ({ input }) => {
        const userAgent = new UserAgent();
        const hdr = {
            'User-Agent': userAgent.toString(),
            'Accept': 'application/xml,text/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
        } as const
        const res = await fetch(input.url, { headers: hdr })
        if (!res.ok)
            throw new Error(`Sitemap fetch failed: ${res.status} ${res.statusText}`)
        const xml = await res.text()
        const $ = cheerio.load(xml, { xmlMode: true })
        const sitemapUrls = $('sitemap > loc').map((_, el) => $(el).text()).get()
        return { output: { sitemapUrls } }
    },
    label: 'Get sitemap URLs',
}
