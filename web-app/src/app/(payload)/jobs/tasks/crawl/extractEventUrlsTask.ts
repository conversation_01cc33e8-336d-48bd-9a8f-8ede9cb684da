import type { TaskConfig } from 'payload'
import * as cheerio from 'cheerio'
import UserAgent from 'user-agents'

async function extractEventUrlsHandler({ input }: {
    input: { sitemapUrl: string, filterText: string }
  }): Promise<{ output: { eventUrls: string[] } }> {
    const userAgent = new UserAgent();
    const hdr = {
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      'Accept': 'application/xml,text/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
    } as const;
    
    const resXml = await fetch(input.sitemapUrl, { headers: hdr });
    if (!resXml.ok) {
      throw new Error(`Sitemap fetch failed: ${resXml.status} ${resXml.statusText}`);
    }
    const xml = await resXml.text();
    
    const $ = cheerio.load(xml, { xmlMode: true });
    const eventUrls = $('url > loc')
      .map((_, el) => $(el).text())
      .get()
      .filter(u => u.includes(input.filterText));
  
    return { output: { eventUrls } };
  }
  
  export const extractEventUrlsTask: TaskConfig<'extractEventUrls'> = {
    slug: 'extractEventUrls',
    retries: 3,
    inputSchema: [
      { name: 'sitemapUrl', type: 'text', required: true },
      {name: 'filterText', type: 'text', required: true}
    ],
    outputSchema: [
      { name: 'eventUrls', type: 'text', hasMany: true, required: true },
    ],
    handler: extractEventUrlsHandler,
    label: 'Extract /event/ URLs from sitemap list',
  };
  
