import type { TaskConfig } from 'payload'
import { listAllKeys } from '@/lib/s3/client';

export const extractKeysByBucketTask: TaskConfig<'extractKeysByBucket'> = {
    slug: 'extractKeysByBucket',
    retries: 10,
    inputSchema: [{ name: 'bucket', type: 'text', required: true }],
    outputSchema: [{ name: 'keys', type: 'text', hasMany: true, required: true, }],
    handler: async ({ input }) => {
        const keys = await listAllKeys(input.bucket)
        return { output: { keys } }
    },
    label: 'extract Keys By Bucket',
}
