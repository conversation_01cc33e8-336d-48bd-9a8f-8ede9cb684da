import type { TaskConfig } from 'payload'
import * as cheerio from 'cheerio'

export const extractLdJsonTask: TaskConfig<'extractLdJson'> = {
    slug: 'extractLdJson',
    retries: 2,
    inputSchema: [{ name: 'html', type: 'text', required: true }],
    outputSchema: [{ name: 'json', type: 'json', required: true, }],
    handler: async ({ input }) => {
        let json = null
        const $ = cheerio.load(input.html)
        const script = $('script[type="application/ld+json"]').first().html();
        if (!script) {
            throw new Error('No JSON-LD script found in the HTML');
        }
        json = JSON.parse(script.trim());
        return { output: { json } }
    },
    label: 'extract Keys By Bucket',
}
