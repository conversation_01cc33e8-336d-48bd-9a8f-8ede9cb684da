import type { TaskConfig } from 'payload'
import { handleSongkickEvent } from '@/lib/songkick/lib'

export const handleSongkickEventTask: TaskConfig<'handleSongkickEvent'> = {
  slug: 'handleSongkickEvent',
  retries: 3,
  inputSchema: [{ name: 'jsonEvent', type: 'json', required: true }],
  outputSchema: [{ name: 'event', type: 'json', required: true }],
  handler: async ({ input, req }) => {
    const event = await handleSongkickEvent(input.jsonEvent as any, req)
    return { output: { event: event as any } }
  },
  label: '',
}
