import type { TaskConfig } from 'payload'
import * as cheerio from 'cheerio'
import { promisify } from 'util'
import zlib from 'zlib'

const gunzip = promisify(zlib.gunzip)

async function fetchGzXml(url: string): Promise<string> {
  const res = await fetch(url, { headers: { 'accept-encoding': 'gzip' } })
  if (!res.ok) throw new Error(`Failed ${url}: ${res.status}`)
  const buf = Buffer.from(await res.arrayBuffer())
  return (await gunzip(buf)).toString('utf8')
}

export const collectConcertUrlsTask: TaskConfig<'collectConcertUrls'> = {
  slug: 'collectConcertUrls',
  inputSchema: [{ name: 'sitemapUrls', type: 'text', hasMany: true, required: true }],
  outputSchema: [{ name: 'urls', type: 'text', hasMany: true, required: true }],
  handler: async ({ input }) => {
    const urlsSet = new Set<string>()
    for (const mapUrl of input.sitemapUrls) {
      try {
        const xml = await fetchGzXml(mapUrl)
        const $ = cheerio.load(xml, { xmlMode: true })
        $('url > loc').each((_, el) => {
          urlsSet.add($(el).text())
        })
      } catch {}
    }
    const urls = [...urlsSet]
    return { output: { urls } }
  },
  label: '',
}
