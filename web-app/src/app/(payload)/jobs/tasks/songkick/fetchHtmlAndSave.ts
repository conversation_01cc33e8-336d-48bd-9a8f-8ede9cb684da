import type { TaskConfig } from 'payload'
import UserAgent from 'user-agents'
import { uploadToObjectStorage } from '@/lib/s3/client'

export const fetchHtmlAndSaveTask: TaskConfig<'fetchHtmlAndSave'> = {
  slug: 'fetchHtmlAndSave',
  retries: 3,
  inputSchema: [
    { name: 'url', type: 'text', required: true },
    { name: 'bucket', type: 'text', required: true },
  ],
  handler: async ({ input }) => {
    const userAgent = new UserAgent()
    const res = await fetch(input.url,
      {
        headers:
        {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        }
      })

    if (!res.ok) {
      if (res.status === 404) {
        return { output: {} }
      }
      throw new Error(`Failed ${input.url}: ${res.status}`)
    }

    const html = await res.text()
    let key = encodeURIComponent(input.url)
    if (!key.includes('.html')) {
      key += '.html'
    }
    await uploadToObjectStorage(html, key, input.bucket, 'text/html')
    return { output: {} }
  },
  label: '',
}
