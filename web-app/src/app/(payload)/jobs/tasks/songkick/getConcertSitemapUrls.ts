import type { TaskConfig } from 'payload'
import * as cheerio from 'cheerio'
import UserAgent from 'user-agents'

export const getConcertSitemapUrlsTask: TaskConfig<'getConcertSitemapUrls'> = {
    slug: 'getConcertSitemapUrls',
    retries: 3,
    inputSchema: [{ name: 'url', type: 'text', required: true }],
    outputSchema: [{ name: 'sitemapUrls', type: 'text', hasMany: true, required: true }],
    handler: async ({ input }) => {
        const userAgent = new UserAgent()
        const res = await fetch(input.url, { headers: { 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', } })
        const xml = await res.text()
        const $ = cheerio.load(xml, { xmlMode: true })
        const sitemapUrls = $('sitemap > loc').map((_, el) => $(el).text())
            .get()
            .filter((u) => u.includes('/concerts.') || u.includes('/festival'))
        return { output: { sitemapUrls } }
    },
    label: '',
}
