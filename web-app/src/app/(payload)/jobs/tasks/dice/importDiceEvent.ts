import type { TaskConfig } from 'payload'
import { importDiceEvent } from '@/app/(payload)/actions/sync/sync-dice-events'

export const importDiceEventTask: TaskConfig<'importDiceEvent'> = {
  slug: 'importDiceEvent',
  retries: 10,
  inputSchema: [
    { name: 'eventIdLive', type: 'text', required: true },
    { name: 'event', type: 'json', required: false, },

  ],
  outputSchema: [
    { name: 'eventDocId', type: 'number', required: true },
  ],
  handler: async ({ input, req }) => {
    const created = await importDiceEvent(input.eventIdLive, req, input.event as any)
    if (!created) throw new Error(`Event ${input.eventIdLive} not imported`)

    return { output: { eventDocId: created.id } }
  },
  label: 'Import single DICE event',
}
