import type { TaskConfig } from 'payload'
import * as cheerio from 'cheerio'

export const extractRetailerIdTask: TaskConfig<'extractRetailerId'> = {
    slug: 'extractRetailerId',
    inputSchema: [{ name: 'html', type: 'text', required: true }],
    outputSchema: [{ name: 'id', type: 'text', required: true, }],
    handler: async ({ input }) => {
        const $ = cheerio.load(input.html)
        const id = $('meta[property="product:retailer_item_id"]').attr('content')
        if (!id) {
            throw new Error('Retailer ID not found in HTML')
        }
        return { output: { id } }
    },
    label: 'extract Keys By Bucket',
}
