import type { TaskConfig } from 'payload'
import DiceService from '@/lib/dice/service'
import { Ticket } from '@/lib/dice/schema-types';

type Params = { first?: number; after?: string }

export const upsertTicketsTask: TaskConfig<'upsertTickets'> = {
    slug: 'upsertTickets',
    inputSchema: [{ name: 'tickets', type: 'array', fields: [], required: true }],
    outputSchema: [{ name: 'ticketIds', type: 'number', hasMany: true, required: true }],
    handler: async ({ req, input }) => {
        const ticketIds: number[] = [];

        const tickets = input.tickets as Ticket[]
        for (const ticket of tickets ?? []) {
            if (!ticket) continue;
            if (!ticket.ticketType || !ticket.priceTier) continue;

            const ticketTypesFound = await req.payload.find({
                collection: 'ticketTypes',
                where: {
                    'externalDiceId.priceTierId': { equals: ticket.priceTier.id },
                },
            });
            const ticketTypeDoc = ticketTypesFound.docs[0];
            if (!ticketTypeDoc) continue;


            const ticketsFound = await req.payload.find({
                collection: 'tickets',
                where: { externalDiceId: { equals: ticket.id } },
                limit: 1,
            });
            let ticketDoc = ticketsFound.docs[0];
            if (!ticketDoc) {
                ticketDoc = await req.payload.create({
                    collection: 'tickets',
                    data: {
                        externalDiceId: ticket.id || 'No id provided',
                        code: ticket.code ?? '',
                        total: ticketTypeDoc.total ?? 0,
                        ticketType: ticketTypeDoc.id,
                    },
                });
            }
            ticketIds.push(ticketDoc.id)
        }
        return {
            output: {
                ticketIds
            }
        }
    },
    label: 'Dice → upsert Tickets Task',
}
