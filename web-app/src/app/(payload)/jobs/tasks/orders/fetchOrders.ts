import type { TaskConfig } from 'payload'
import DiceService from '@/lib/dice/service'


export const fetchOrdersTask: TaskConfig<'fetchOrders'> = {
  slug: 'fetchOrders',
  retries: 10,
  inputSchema: [
    { name: 'first', type: 'number', required: true },
    { name: 'after', type: 'text', required: false, },
    { name: 'token', type: 'text', required: true },
  ],
  outputSchema: [
    { name: 'orders', type: 'json', required: true },
    { name: 'pageInfo', type: 'json', required: true },
  ],
  handler: async ({ input }) => {
    const firstRaw = input?.first
    const afterRaw = input?.after

    const token = input?.token
    const first = typeof firstRaw === 'number' ? firstRaw : undefined
    const after = typeof afterRaw === 'string' ? afterRaw : undefined

    const dice = new DiceService(token)
    const { orders = [], pageInfo } = await dice.getOrders({ first, after })

    return {
      output: {
        orders,
        pageInfo,
      },
    }
  },
  label: 'Dice → all orders',
}
