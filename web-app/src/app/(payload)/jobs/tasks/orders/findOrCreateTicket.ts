import type { TaskConfig } from 'payload'

export const findOrCreateTicketTask: TaskConfig<'findOrCreateTicket'> = {
  slug: 'findOrCreateTicket',

  inputSchema: [
    { name: 'ticket', type: 'json', required: true },
    { name: 'ticketTypeId', type: 'number', required: true },
  ],
  outputSchema: [{ name: 'ticketDocId', type: 'number', required: true }],

  handler: async ({ input, req }) => {

    const ticket = input.ticket as {
      id: string
      priceTierId: string
      code?: string,
      purchasedAt?: string
    }
    const ticketTypeId = input.ticketTypeId as number

    const ticketsFound = await req.payload.find({
      collection: 'tickets',
      where: { externalDiceId: { equals: ticket.id } },
      limit: 1,
    })

    if (ticketsFound.docs.length) {
      return {
        output: { ticketDocId: ticketsFound.docs[0]!.id },
      }
    }
    const created = await req.payload.create({
      collection: 'tickets',
      data: {
        externalDiceId: ticket.id,
        code: ticket.code || '',
        total: 0,
        ticketType: ticketTypeId,
        createdAt: ticket.purchasedAt,
      },
    })

    return { output: { ticketDocId: created.id } }
  },

  label: 'Task — Find or Create Ticket',
}
