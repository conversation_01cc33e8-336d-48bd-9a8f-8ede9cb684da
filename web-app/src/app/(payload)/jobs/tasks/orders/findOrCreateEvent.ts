import type { TaskConfig } from 'payload'
import { Order, PageInfo, Event } from '@/lib/dice/schema-types'

export const findOrCreateEventTask: TaskConfig<'findOrCreateEvent'> = {
    slug: 'findOrCreateEvent',
    inputSchema: [{ name: 'event', type: 'json', required: true }],
    outputSchema: [{ name: 'eventDocId', type: 'number', required: false }],
    handler: async ({ input, tasks, req }) => {
        const diceEvent = input.event as Event

        const eventsFound = await req.payload.find({
            collection: 'events',
            where: { externalDiceId: { equals: diceEvent.id } },
            limit: 1,
        })

        if (eventsFound.docs.length) {
            return { output: { eventDocId: eventsFound.docs[0]!.id } }
        }
        if (diceEvent.eventIdLive) {
            const { eventDocId } = await tasks.importDiceEvent(
                `create-dice-event-${diceEvent.eventIdLive}`,
                { input: { eventIdLive: diceEvent.eventIdLive } }
            )
            return { output: { eventDocId } }
        }

        return { output: {} }
    },
    label: 'Task — Find or Create Dice Event',
}
