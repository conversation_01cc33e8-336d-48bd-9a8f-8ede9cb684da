// tasks/createOrder.ts
import { indexObject } from '@/lib/sync/indexing'
import type { TaskConfig } from 'payload'

export const createOrderTask: TaskConfig<'createOrder'> = {
  slug: 'createOrder',

  inputSchema: [
    { name: 'externalDiceId', type: 'text', required: true },
    { name: 'eventDocId', type: 'number', required: true },
    { name: 'fanEmail', type: 'text', required: false },
    { name: 'ticketDocIds', type: 'number', hasMany: true, required: true },
  ],
  outputSchema: [],

  handler: async ({ input, req }) => {
    const externalDiceId = input.externalDiceId as string
    const eventDocId = input.eventDocId as number
    const fanEmail = (input.fanEmail as string) || 'No email provided for this order'
    const ticketDocIds = input.ticketDocIds as number[]

    const {docs: [fan]} =await req.payload.find({
      collection: 'fanUsers',
      where: {
        email: { equals: fanEmail },
      },
      limit: 1,
    })

    // Create a new Order document in Payload
    await req.payload.create({
      collection: 'orders',
      data: {
        externalDiceId,
        event: eventDocId,
        fanEmail,
        fan: fan?.id || null,
        tickets: ticketDocIds,
        origin: 'dice',
      },
    })

    // No output needed
    return { output: {} }
  },

  label: 'Task — Create Order in Payload',
}
