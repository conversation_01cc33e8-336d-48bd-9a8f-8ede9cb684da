import type { TaskConfig } from 'payload'
import { fetchAllEvents } from '@/lib/qflow/qflow-client'

export const qflowFetchEventsTask: TaskConfig<'qflowFetchEvents'> = {
  slug: 'qflowFetchEvents',
  outputSchema: [{ name: 'events', type: 'json', required: true }],
  handler: async () => {
    const events = await fetchAllEvents()
    return { output: { events } }
  },
  label: 'Fetch events from Q-Flow',
}
