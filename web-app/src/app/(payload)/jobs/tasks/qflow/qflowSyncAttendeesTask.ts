import type { TaskConfig } from 'payload'
import { fetchAttendees } from '@/lib/qflow/qflow-client'

export const qflowSyncAttendeesTask: TaskConfig<'qflowSyncAttendees'> = {
  slug: 'qflowSyncAttendees',
  inputSchema: [{ name: 'eventId', type: 'text', required: true }],
  handler: async ({ input, req }) => {
    const attendees = await fetchAttendees(input.eventId)

    for (const a of attendees) {
      await req.payload.update({
        collection: 'tickets',
        where: { code: { equals: a.ticketId } },
        data:  { admittedDate: a.checkInTime },
      })
    }
    return { output: {} }
  },
  label: 'Sync attendees for one event',
}
