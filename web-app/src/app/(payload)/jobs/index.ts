import type { JobsConfig } from 'payload'
import { importDiceEventTask } from './tasks/dice/importDiceEvent'
import { autoRun } from './autoRun'
import { syncDicePartnerEventsWorkflow } from './workflows/syncDicePartnerEventsWorkflow'
import { crawlDicekWorkflow } from './workflows/crawlDiceWorkflow'
import { extractEventUrlsTask } from './tasks/crawl/extractEventUrlsTask'
import { getSitemapUrlsTask } from './tasks/crawl/getSitemapUrls'
import { syncDiceOrdersWorkflow } from './workflows/syncDiceOrdersWorkflow'
import { qflowLoginTask } from './tasks/qflow/qflowLoginTask'
import { qflowFetchEventsTask } from './tasks/qflow/qflowFetchEventsTask'
import { qflowSyncAttendeesTask } from './tasks/qflow/qflowSyncAttendeesTask'
import { syncQFlowWorkflow } from './workflows/syncQFlowWorkflow'
import { fetchEventWithTicketTypesTask } from './tasks/ticketTypes/fetchEventWithTicketTypes'
import { upsertTicketTypesTask } from './tasks/ticketTypes/upsertTicketTypes'
import { fetchOrdersTask } from './tasks/orders/fetchOrders'
import { upsertTicketsTask } from './tasks/orders/upsertTickets'
import { crawlSongkickWorkflow } from './workflows/crawlSongkickWorkflow'
import { collectConcertUrlsTask } from './tasks/songkick/collectConcertUrls'
import { getConcertSitemapUrlsTask } from './tasks/songkick/getConcertSitemapUrls'
import { fetchHtmlAndSaveTask } from './tasks/songkick/fetchHtmlAndSave'
import { crawlRAWorkflow } from './workflows/crawlRAWokrflow'
import { crawlSanity } from './workflows/crawlSanityWorkflow'
import { saveRawSanityData } from './tasks/sanity/saveRawSanityData'
import { importSanityData } from './tasks/sanity/importSanityData'
import { handleSongkickEventTask } from './tasks/songkick/handleSongkickEvent'
import { syncSongkickWorkflow } from './workflows/syncSongkickWorkflow'
import { importSanityDataWorkflow } from './workflows/importSanityDataWorkflow'
import { extractKeysByBucketTask } from './tasks/crawl/extractKeysByBucket'
import { extractLdJsonTask } from './tasks/crawl/extractLdJson'
import { syncDiceWorkflow } from './workflows/syncDiceWorkflow'
import { extractRetailerIdTask } from './tasks/dice/extractLdJson'
import { findOrCreateEventTask } from './tasks/orders/findOrCreateEvent'
import { findOrCreateTicketTask } from './tasks/orders/findOrCreateTicket'
import { createOrderTask } from './tasks/orders/createOrder'
import { importPlanetscalePresaleRegistration } from './tasks/planetscale/importPlanetscalePresaleRegistrations'
import { importPlanetscaleUser } from './tasks/planetscale/importPlanetscaleUsers'
import { syncPlanetscaleUsersWorkflow } from './workflows/syncPlanetscaleUsersWorkflow'
import { syncPlanetscalePresaleRegistrationsWorkflow } from './workflows/syncPlanetscalePresaleRegistrationsWorkflow'
import { sendSalesReportTask } from './tasks/reporting/sendSalesReport'

export const jobs: JobsConfig = {
  access: { run: () => true },
  autoRun: async (payload) => {
    const baseAutoRun = Array.isArray(autoRun) ? autoRun : autoRun ? await autoRun(payload) : []
    return [
      ...baseAutoRun,
      {
        cron: '0 9 * * 1',
        queue: 'weekly-reports',
        limit: 100,
      },
      {
        cron: '0 9 * * *',
        queue: 'daily-reports',
        limit: 100,
      },
    ]
  },
  deleteJobOnComplete: false,
  tasks: [
    importDiceEventTask,
    extractEventUrlsTask,
    getSitemapUrlsTask,
    qflowLoginTask,
    qflowFetchEventsTask,
    qflowSyncAttendeesTask,
    fetchEventWithTicketTypesTask,
    upsertTicketTypesTask,
    fetchOrdersTask,
    upsertTicketsTask,
    getConcertSitemapUrlsTask,
    collectConcertUrlsTask,
    fetchHtmlAndSaveTask,
    extractEventUrlsTask,
    saveRawSanityData,
    importSanityData,
    handleSongkickEventTask,
    extractKeysByBucketTask,
    extractLdJsonTask,
    extractRetailerIdTask,
    findOrCreateEventTask,
    findOrCreateTicketTask,
    createOrderTask,
    importPlanetscaleUser,
    importPlanetscalePresaleRegistration,
    sendSalesReportTask,
  ],
  workflows: [
    crawlSanity,
    crawlRAWorkflow,
    crawlSongkickWorkflow,
    crawlDicekWorkflow,
    importSanityDataWorkflow,
    syncDicePartnerEventsWorkflow,
    syncDiceOrdersWorkflow,
    syncDiceWorkflow,
    syncQFlowWorkflow,
    syncSongkickWorkflow,
    syncPlanetscaleUsersWorkflow,
    syncPlanetscalePresaleRegistrationsWorkflow,
  ],
  shouldAutoRun: async () => {
    return process.env.NODE_ENV === 'production' || process.env.ENABLE_JOBS === 'true'
  },
  jobsCollectionOverrides: ({ defaultJobsCollection }) => {
    if (!defaultJobsCollection.admin) {
      defaultJobsCollection.admin = {}
    }
    defaultJobsCollection.admin.hidden = false
    return defaultJobsCollection
  },
}
