import { cn } from '@/utilities/ui'
import { Poppins } from 'next/font/google'
import React from 'react'

import './globals.css'

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-poppins',
})

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html className={cn(poppins.variable)} lang="en">
      <body className={`${poppins.className} relative text-foreground`}>
        {children}
      </body>
    </html>
  )
}
