@import 'tailwindcss';

@config '../../../tailwind.config.mjs';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: auto;
    font-weight: auto;
  }

  :root {
    --background: #000; /* black */
    --foreground: #fff; /* white */
    --error: #f0423a; /* red */
    --gray-darkest: #1c1c1c; /* hsl(0, 0%, 11%)   */
    --gray-darker: #3a3a3a; /* hsl(0, 0%, 23%)   */
    --gray-base: #5a5a5a; /* hsl(0, 0%, 35%)    */
    --gray-soft: #ffffffa8; /* hsla(0, 0%, 100%, 0.66)  */
  }

  .dark {
    --background: #fff; /* white */
    --foreground: #000; /* black */
    --error: #f0423a; /* red */
    --gray-darkest: #1c1c1c; /* hsl(0, 0%, 11%)   */
    --gray-darker: #3a3a3a; /* hsl(0, 0%, 23%)   */
    --gray-base: #5a5a5a; /* hsl(0, 0%, 35%)    */
    --gray-soft: #ffffffa8; /* hsla(0, 0%, 100%, 0.66)  */
  }
}

@media (min-width: theme('screens.sm')) {
  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--white) var(--gray-base);
  }

  [data-ui='NextStudioLayout'] * {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbarSecondary) var(--scrollbarPrimary);
  }

  /* Chrome, Edge, and Safari */
  *::-webkit-scrollbar {
    width: 8px;
  }

  [data-ui='NextStudioLayout'] *::-webkit-scrollbar {
    width: 10px;
  }

  *::-webkit-scrollbar-track {
    background: var(--scrollbarPrimary);
    border-radius: 0px;
  }

  [data-ui='NextStudioLayout'] *::-webkit-scrollbar-track {
    background: var(--white);
    border-radius: 0px;
  }

  *::-webkit-scrollbar-thumb {
    background-color: var(--scrollbarSecondary);
    border-radius: 4px;
    border: 0px solid var(--scrollbarPrimary);
  }

  [data-ui='NextStudioLayout'] *::-webkit-scrollbar-thumb {
    background-color: var(--gray-base);
    border-radius: 4px;
    border: 1px solid var(--white);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground min-h-[100vh] flex flex-col;
  }
}

/* html {
  opacity: 0;
  background-color: var(--background);
}

html[data-theme='dark'],
html[data-theme='light'] {
  opacity: initial;
} */
