import type { Access } from 'payload'
import { isSuperAdmin } from './isSuperAdmin'
import { EventOrganizer } from '@/payload-types'

export const canMutateTenant: Access = ({ req }) => {
  if (!req.user) {
    return false
  }

  if (isSuperAdmin(req.user)) {
    return true
  }

  return {
    id: {
      in:
        req.user?.tenants
          ?.map(({ roles, tenant }) =>
            roles?.includes('tenant-admin')
              ? tenant && (typeof tenant === 'string' ? tenant : (tenant as EventOrganizer).id)
              : null,
          )
          .filter(Boolean) || [],
    },
  }
}
