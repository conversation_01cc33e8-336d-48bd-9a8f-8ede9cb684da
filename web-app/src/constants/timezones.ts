import type { SelectField } from 'payload'
import fallbackTz from 'timezones.json'

export const tzIds: string[] =
  typeof Intl.supportedValuesOf === 'function'
    ? (Intl.supportedValuesOf('timeZone') as string[])
    : Array.isArray(fallbackTz)
      ? fallbackTz.map((z: any) => (typeof z === 'string' ? z : (z.value ?? z.name)))
      : []

export const TIMEZONE_OPTIONS: NonNullable<SelectField['options']> = tzIds.map((tz) => ({
  label: tz,
  value: tz,
}))

export const isValidZone = (val: unknown): true | string =>
  (typeof val === 'string' && tzIds.includes(val)) || !val ? true : 'Must be a valid IANA time‑zone'
