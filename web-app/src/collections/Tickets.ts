import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import type { CollectionConfig } from 'payload'

export const Tickets: CollectionConfig = {
  slug: 'tickets',
  access: {
    create: contributor,
    delete: contributor,
    read: authenticated,
    update: contributor,
  },
  admin: {
    group: 'Fan Data',
  },
  fields: [
    {
      name: 'externalDiceId',
      type: 'text',
      required: true,
      hidden: true,
    },
    {
      name: 'admittedDate',
      type: 'date',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'code',
      type: 'text',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'total',
      type: 'number',
      label: 'Total price with commissions in cents',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'ticketType',
      type: 'relationship',
      relationTo: 'ticketTypes',
      required: true,
      admin: {
        description: 'Which ticket type (VIP, GA, etc.)',
        readOnly: true,
      },
    },
    {
      name: 'order',
      type: 'join',
      collection: 'orders',
      on: 'tickets',
      admin: {
        defaultColumns: ['event', 'quantity', 'total'],
        allowCreate: false,
      },
    },
  ],
}
