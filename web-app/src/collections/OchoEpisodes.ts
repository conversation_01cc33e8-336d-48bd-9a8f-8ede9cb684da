import { anyone } from '@/access/anyone'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { overviewTab } from '@/fields/overviewTab'
import { metaTab } from '@/fields/metaTab'
import { contributor } from '@/access/contributor'

export const OchoEpisodes: CollectionConfig = withSlug({
  slugSource: 'title',
  slug: 'ochoEpisodes',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'title',
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'episodeNumber',
              type: 'number',
              required: true,
            },
            {
              name: 'description',
              type: 'richText',
              required: true,
            },
            {
              name: 'videoUrl',
              type: 'text',
            },
            {
              name: 'podcastUrl',
              type: 'text',
            },
            {
              name: 'guests',
              type: 'relationship',
              relationTo: 'artists',
              hasMany: true,
              required: true,
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
