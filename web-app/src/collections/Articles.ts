import { anyone } from '@/access/anyone'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { overviewTab } from '@/fields/overviewTab'
import { metaTab } from '@/fields/metaTab'
import { contributor } from '@/access/contributor'
import { SyncLock<PERSON>ield } from '@/fields/syncLockField'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'

export const Articles: CollectionConfig = withSlug({
  slugSource: 'title',
  slug: 'articles',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'title',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'type',
              type: 'select',
              options: [
                {
                  label: 'Academy',
                  value: 'Academy',
                },
                {
                  label: 'Magazine',
                  value: 'Magazine',
                },
              ],
              required: true,
            },
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'author',
              type: 'relationship',
              relationTo: 'authors',
              required: true,
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
            {
              name: 'mentions',
              type: 'relationship',
              relationTo: [
                'events',
                'artists',
                'eventBrands',
                'festivals',
                'residencies',
                'venues',
                'authors',
                'ochoEpisodes',
              ],
              hasMany: true,
            },
            {
              name: 'hubs',
              type: 'relationship',
              relationTo: 'hubs',
              hasMany: true,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
