import { anyone } from '@/access/anyone'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { overviewTab } from '@/fields/overviewTab'
import { metaTab } from '@/fields/metaTab'
import { contributor } from '@/access/contributor'

const getFullTitle = (doc: Partial<any> | undefined) => (doc ? `${doc.name} - ${doc.type}` : '')

export const Genres: CollectionConfig = withSlug({
  slugSource: 'name',
  slug: 'genres',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'fullTitle',
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'type',
              type: 'select',
              options: [
                { label: 'Spotify', value: 'Spotify' },
                { label: 'Gray Area', value: 'GrayArea' },
                { label: 'Dice', value: 'Dice' },
                { label: 'Resident Advisor', value: 'ResidentAdvisor' },
                { label: 'Chart Metric', value: 'ChartMetric' },
              ],
            },
            {
              name: 'description',
              type: 'richText',
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
    {
      name: 'fullTitle',
      type: 'text',
      label: 'Full Title',
      admin: {
        hidden: true,
        disableBulkEdit: true,
      },
      hooks: {
        beforeChange: [({ data }) => getFullTitle(data)],
        afterRead: [({ data }) => getFullTitle(data)],
      },
    },
  ],
})
