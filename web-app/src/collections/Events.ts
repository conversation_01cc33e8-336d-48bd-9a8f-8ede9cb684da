import { anyone } from '@/access/anyone'
import type { CollectionConfig } from 'payload'
import { getEvent } from '@/app/(payload)/actions/event/get-event'
import { withSlug } from '@/utilities/_withSlug'
import { metaTab } from '@/fields/metaTab'
import { overviewTab } from '@/fields/overviewTab'
import { socialLinks } from '@/fields/socialLinks'
import {
  crawlDicekWorkflow,
  syncDicePartnersEvents,
  syncDiceWorkflow,
} from '@/app/(payload)/actions/sync/sync-partner-dice-events'
import { sync } from '@/app/(payload)/actions/sync/sync-qflow'
import { isValidZone, TIMEZONE_OPTIONS } from '@/constants/timezones'
import {
  crawlRAEvents,
  crawlSongKickEvents,
  syncSongKickEvents,
} from '@/app/(payload)/actions/sync/sync-songkick-events'
import { PLATFORM_OPTIONS } from '@/fields/platformOptions'
import { genresFromLineup } from '@/hooks/genresFromLineup'
import { crawlSanity, syncSanity } from '@/app/(payload)/actions/sync/sync-sanity'
import { contributor } from '@/access/contributor'
import { marketingTab } from '@/fields/marketingTab'
import { promotionsTab } from '@/fields/promotionsTab'
import { SyncLockField } from '@/fields/syncLockField'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'

export const Events: CollectionConfig = withSlug({
  slugSource: 'name',
  slug: 'events',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  endpoints: [
    {
      path: '/getEvent',
      method: 'get',
      handler: getEvent,
    },
    {
      path: '/sync-partner',
      method: 'get',
      handler: syncDicePartnersEvents,
    },
    {
      path: '/sync-dice',
      method: 'get',
      handler: syncDiceWorkflow,
    },
    {
      path: '/crawl-dice',
      method: 'get',
      handler: crawlDicekWorkflow,
    },
    {
      path: '/crawl-songkick',
      method: 'get',
      handler: crawlSongKickEvents,
    },
    {
      path: '/sync-songkick',
      method: 'get',
      handler: syncSongKickEvents,
    },
    {
      path: '/crawl-ra',
      method: 'get',
      handler: crawlRAEvents,
    },
    {
      path: '/qflow-sync',
      method: 'get',
      handler: sync,
    },
    {
      path: '/sync-Sanity',
      method: 'get',
      handler: syncSanity,
    },
    {
      path: '/crawl-sanity',
      method: 'get',
      handler: crawlSanity,
    },
  ],
  admin: {
    useAsTitle: 'name',
    group: 'Events',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      name: 'search',
      type: 'text',
      virtual: true,
      admin: {
        position: 'sidebar',
        components: {
          Field: '@/components/DiceEventSearch.tsx',
        },
      },
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'eventOrganizer',
              type: 'relationship',
              relationTo: 'eventOrganizers',
              required: false,
            },
            {
              name: 'origin',
              type: 'select',
              options: PLATFORM_OPTIONS,
              hidden: true,
            },
            {
              name: 'externalPlatformSourceUrls',
              type: 'array',
              hidden: true,
              fields: [
                {
                  name: 'platform',
                  type: 'select',
                  options: PLATFORM_OPTIONS,
                  required: true,
                },
                {
                  name: 'sourceUrl',
                  type: 'text',
                  required: true,
                },
              ],
            },
            {
              name: 'externalSanityId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'externalDiceId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'externalResidentAdvisorId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'externalEventBriteId',
              type: 'text',
              hidden: true,
            },
            // need to select wich url to show to users
            {
              name: 'ticketUrls',
              type: 'text',
              hasMany: true,
            },
            {
              name: 'festival',
              type: 'relationship',
              relationTo: 'festivals',
            },
            {
              name: 'eventBrand',
              type: 'relationship',
              relationTo: 'eventBrands',
            },
            {
              name: 'residency',
              type: 'relationship',
              relationTo: 'residencies',
            },
            {
              name: 'Location',
              type: 'group',
              fields: [
                {
                  name: 'locationType',
                  type: 'radio',
                  defaultValue: 'venue',
                  options: [
                    { label: 'Venue', value: 'venue' },
                    { label: 'City hub', value: 'hub' },
                  ],
                  admin: {
                    layout: 'horizontal',
                    description: 'Choose where the event happens',
                  },
                },
                {
                  name: 'venue',
                  type: 'relationship',
                  relationTo: 'venues',
                  admin: {
                    condition: (_, { locationType }) => locationType === 'venue',
                  },
                },
                {
                  name: 'hub',
                  type: 'relationship',
                  relationTo: 'hubs',
                  admin: {
                    condition: (_, { locationType }) => locationType === 'hub',
                  },
                },
              ],
            },
            {
              name: 'timezone',
              label: 'Time zone',
              type: 'select',
              required: false,
              options: TIMEZONE_OPTIONS,
              validate: isValidZone,
              admin: {
                description: 'IANA name, e.g. Europe/Paris or America/Los_Angeles',
              },
            },
            {
              name: 'announcementDate',
              type: 'date',
              admin: {
                date: {
                  pickerAppearance: 'dayAndTime',
                },
              },
              required: false,
            },
            {
              name: 'saleOnDate',
              type: 'date',
              admin: {
                date: {
                  pickerAppearance: 'dayAndTime',
                },
              },
              required: false,
            },
            {
              name: 'saleOffDate',
              type: 'date',
              admin: {
                date: {
                  pickerAppearance: 'dayAndTime',
                },
              },
              required: false,
            },
            {
              name: 'startDate',
              type: 'date',
              admin: {
                date: {
                  pickerAppearance: 'dayAndTime',
                },
              },
              required: true,
            },
            {
              name: 'endDate',
              type: 'date',
              admin: {
                date: {
                  pickerAppearance: 'dayAndTime',
                },
              },
              required: true,
            },
            {
              name: 'lineup',
              type: 'array',
              fields: [
                {
                  name: 'artist',
                  type: 'relationship',
                  relationTo: 'artists',
                  required: true,
                  maxDepth: 2,
                },
                {
                  name: 'tier',
                  type: 'select',
                  options: [
                    {
                      label: 'Headliner',
                      value: 'Headliner',
                    },
                    {
                      label: 'Special Guest',
                      value: 'SpecialGuest',
                    },
                    {
                      label: 'Support I',
                      value: 'SupportI',
                    },
                    {
                      label: 'Support II',
                      value: 'SupportII',
                    },
                    {
                      label: 'Resident',
                      value: 'Resident',
                    },
                    {
                      label: 'Local',
                      value: 'Local',
                    },
                    {
                      label: 'Jr Local',
                      value: 'JrLocal',
                    },
                  ],
                  required: false,
                },
                {
                  name: 'startTime',
                  type: 'date',
                  admin: {
                    date: {
                      pickerAppearance: 'timeOnly',
                      displayFormat: 'h:mm:ss a',
                    },
                  },
                },
              ],
              hooks: {
                afterChange: [genresFromLineup],
              },
            },
            {
              name: 'eventGenres',
              type: 'group',
              fields: [
                {
                  name: 'fromLineup',
                  type: 'relationship',
                  relationTo: 'genres',
                  hasMany: true,
                  admin: {
                    readOnly: true,
                  },
                  label: 'This field will be calculated after event lineup changes',
                },
                {
                  name: 'manual',
                  type: 'relationship',
                  relationTo: 'genres',
                  hasMany: true,
                },
              ],
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image for the event grid display',
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
            {
              name: 'afterparties',
              label: 'After‑parties',
              type: 'relationship',
              relationTo: 'events',
              hasMany: true,
              admin: {
                description: 'Select events that act as after‑parties for this event',
              },
            },
            {
              name: 'faqs',
              label: 'FAQ',
              type: 'array',
              minRows: 0,
              admin: {
                description: 'Frequently‑asked questions for this event',
              },
              fields: [
                {
                  name: 'question',
                  label: 'Question',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'answer',
                  label: 'Answer',
                  type: 'richText',
                  required: true,
                },
              ],
            },

            {
              name: 'minAge',
              label: 'Minimum Age',
              type: 'number',
              min: 0,
              admin: {
                description: 'Minimum attendee age (in years). Leave blank if no restriction.',
              },
            },
            {
              name: 'ticketTypes',
              type: 'array',
              fields: [
                {
                  name: 'ticketType',
                  type: 'relationship',
                  relationTo: 'ticketTypes',
                  hasMany: false,
                  required: true,
                },
              ],
            },
          ],
        },
        overviewTab,
        marketingTab,
        promotionsTab,
        metaTab,
      ],
    },
  ],
})
