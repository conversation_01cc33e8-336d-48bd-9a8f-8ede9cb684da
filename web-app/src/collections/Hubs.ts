import { anyone } from '@/access/anyone'
import { contributor } from '@/access/contributor'
import { metaTab } from '@/fields/metaTab'
import { overviewTab } from '@/fields/overviewTab'
import { Hub } from '@/payload-types'
import type { CollectionConfig, FieldHook } from 'payload'

const allChildHubVenues: FieldHook<Hub> = async ({ data, req }) => {
  const childHubVenues = await req.payload.find({
    collection: 'hubs',
    where: {
      parent: { equals: data?.id },
    },
    select: {
      venues: true,
    },
  })

  const venues = childHubVenues.docs.map((doc) => doc.venues?.docs?.map((venue) => venue)).flat()
  return venues
}

export const Hubs: CollectionConfig = {
  slug: 'hubs',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'formatted',
              type: 'text',
              required: true,
            },
            {
              name: 'type',
              type: 'select',
              options: [
                { label: 'Country', value: 'country' },
                { label: 'Region', value: 'region' },
                { label: 'City', value: 'city' },
              ],
              defaultValue: 'city',
              admin: {
                description: 'Level of this hub in the hierarchy',
              },
            },
            {
              name: 'parent',
              type: 'relationship',
              relationTo: 'hubs',
              admin: {
                description: 'Select a parent hub (e.g. region for a city, country for a region)',
              },
            },
            {
              name: 'childHubs',
              type: 'join',
              collection: 'hubs',
              on: 'parent',
              admin: {
                condition: (_, siblings) => siblings.type !== 'city',
              },
            },
            {
              name: 'venues',
              type: 'join',
              collection: 'venues',
              on: 'hub',
            },
            {
              name: 'childHubVenues',
              type: 'relationship',
              relationTo: 'venues',
              hasMany: true,
              virtual: true,
              admin: {
                readOnly: true,
              },
              hooks: {
                afterRead: [allChildHubVenues],
              },
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
}
