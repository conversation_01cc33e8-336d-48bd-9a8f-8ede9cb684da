import { anyone } from '@/access/anyone'
import { socialLinks } from '@/fields/socialLinks'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { metaTab } from '@/fields/metaTab'
import { overviewTab } from '@/fields/overviewTab'
import { PLATFORM_OPTIONS } from '@/fields/platformOptions'
import { contributor } from '@/access/contributor'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import { SyncLockField } from '@/fields/syncLockField'

export const Venues: CollectionConfig = withSlug({
  slugSource: 'name',
  slug: 'venues',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'address',
              type: 'text',
              required: true,
            },
            {
              name: 'city',
              type: 'text',
              required: true,
            },
            {
              name: 'country',
              type: 'text',
              required: true,
            },
            {
              name: 'coordinates',
              type: 'point',
            },
            {
              name: 'timezone',
              type: 'text',
              required: true,
            },
            {
              name: 'hub',
              type: 'relationship',
              relationTo: 'hubs',
            },
            {
              name: 'capacities',
              type: 'array',
              fields: [
                {
                  name: 'title',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'capacity',
                  type: 'number',
                  min: 0,
                  required: true,
                },
              ],
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
            {
              name: 'mediaAssets',
              type: 'upload',
              relationTo: 'media',
              hasMany: true,
            },
            {
              name: 'accessibility',
              type: 'text',
              hasMany: true,
            },
            {
              name: 'internalContacts',
              type: 'array',
              fields: [
                {
                  name: 'type',
                  type: 'select',
                  options: [
                    { label: 'Management', value: 'Management' },
                    { label: 'Marketing', value: 'Marketing' },
                    { label: 'Advancing', value: 'Advancing' },
                    { label: 'Production', value: 'Production' },
                    { label: 'Ticketing', value: 'Ticketing' },
                    { label: 'Finance', value: 'Finance' },
                  ],
                },
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'phoneNumber',
                  type: 'text',
                },
                {
                  name: 'emailAddress',
                  type: 'text',
                },
              ],
            },
            {
              name: 'houseRules',
              type: 'relationship',
              relationTo: 'documents',
            },
            {
              name: 'productionTechSpecs',
              type: 'relationship',
              relationTo: 'documents',
              hasMany: true,
            },
            {
              name: 'invoicingInfo',
              type: 'richText',
            },
            {
              name: 'closestAirport',
              type: 'text',
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
            {
              name: 'origin',
              type: 'select',
              options: PLATFORM_OPTIONS,
              hidden: true,
            },
            {
              name: 'externalSanityId',
              type: 'text',
              hidden: true,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
