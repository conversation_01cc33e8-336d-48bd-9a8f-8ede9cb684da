import { anyone } from '@/access/anyone'
import { socialLinks } from '@/fields/socialLinks'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { overviewTab } from '@/fields/overviewTab'
import { metaTab } from '@/fields/metaTab'
import { PLATFORM_OPTIONS } from '@/fields/platformOptions'
import { contributor } from '@/access/contributor'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import { SyncLockField } from '@/fields/syncLockField'

export const Festivals: CollectionConfig = withSlug({
  slugSource: 'name',
  slug: 'festivals',
  labels: {
    singular: 'Festival Edition',
    plural: 'Festival Editions',
  },
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
    group: 'Events',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'festivalBrand',
              type: 'relationship',
              relationTo: 'festivalProfiles',
            },
            {
              name: 'festivalEditionEvents',
              type: 'join',
              collection: 'events',
              on: 'festival',
              hasMany: true,
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
            {
              name: 'externalSanityId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'origin',
              type: 'select',
              options: PLATFORM_OPTIONS,
              hidden: true,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
