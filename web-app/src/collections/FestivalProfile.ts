import { anyone } from '@/access/anyone'
import { socialLinks } from '@/fields/socialLinks'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { overviewTab } from '@/fields/overviewTab'
import { metaTab } from '@/fields/metaTab'
import { contributor } from '@/access/contributor'
import { PLATFORM_OPTIONS } from '@/fields/platformOptions'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import { SyncLockField } from '@/fields/syncLockField'

export const FestivalProfiles: CollectionConfig = withSlug({
  slugSource: 'name',
  slug: 'festivalProfiles',
  labels: {
    singular: 'Festival Brand',
    plural: 'Festival Brands',
  },
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'festivalEditions',
              type: 'join',
              collection: 'festivals',
              on: 'festivalBrand',
              hasMany: true,
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
            {
              name: 'externalSanityId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'origin',
              type: 'select',
              options: PLATFORM_OPTIONS,
              hidden: true,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
