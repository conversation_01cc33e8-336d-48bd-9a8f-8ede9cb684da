import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import { CollectionConfig, FieldHook } from 'payload'

const appendOnlyArray: FieldHook = ({ value, originalDoc }) => {
  const previous = originalDoc?.statusHistory || []
  const incoming = value || []
  if (incoming.length < previous.length) {
    throw new Error('Removing items from history is not allowed.')
  }

  return [...previous, ...incoming.slice(previous.length)]
}

export const ArtistDeals: CollectionConfig = {
  slug: 'artistDeals',
  access: {
    create: contributor,
    delete: contributor,
    read: authenticated,
    update: contributor,
  },
  fields: [
    {
      name: 'artist',
      type: 'relationship',
      relationTo: 'artists',
      required: true,
    },
    {
      name: 'event',
      type: 'relationship',
      relationTo: 'events',
      required: true,
    },
    {
      name: 'statusHistory',
      type: 'array',
      fields: [
        {
          name: 'status',
          type: 'select',
          options: [
            {
              label: 'Offer sent',
              value: 'offerSent',
            },
            {
              label: 'Counter offer by artist',
              value: 'counterOffer',
            },
            {
              label: 'Declined',
              value: 'declined',
            },
            {
              label: 'Accepted',
              value: 'accepted',
            },
            {
              label: 'Canceled',
              value: 'canceled',
            },
          ],
        },
        {
          name: 'description',
          type: 'text',
        },
      ],
      hooks: {
        beforeChange: [appendOnlyArray],
      },
    },
    {
      name: 'documents',
      type: 'upload',
      relationTo: 'documents',
      label: 'Any related PDF files',
      hasMany: true,
    },
    {
      name: 'expenses',
      type: 'number',
      required: true,
      defaultValue: 0,
    },
  ],
}
