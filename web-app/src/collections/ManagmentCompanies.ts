import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import { CollectionConfig } from 'payload'

export const ManagmentCompanies: CollectionConfig = {
  slug: 'managmentCompanies',
  access: {
    create: contributor,
    delete: contributor,
    read: authenticated,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'address',
      type: 'text',
      required: true,
    },
    {
      name: 'generalContacInfo',
      type: 'group',
      fields: [
        {
          name: 'email',
          type: 'text',
          required: true,
        },
        {
          name: 'phoneNumber',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'managers',
      type: 'join',
      collection: 'managers',
      on: 'managmentCompany',
      hasMany: true,
    },
    // {
    //   name: 'artists',
    //   type: 'join',
    //   collection: 'artists',
    //   on: 'managmentCompanies',
    //   hasMany: true,
    // },
  ],
}
