import type { CollectionConfig } from 'payload'
import path from 'path'
import { fileURLToPath } from 'url'

import { authenticated } from '../access/authenticated'
import { contributor } from '@/access/contributor'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export const Documents: CollectionConfig = {
  slug: 'documents',
  access: {
	create: contributor,
	delete: contributor,
	read: authenticated,
	update: contributor,
  },
  fields: [],
  upload: {
	staticDir: path.resolve(dirname, '../../documents'),
	mimeTypes: ["application/pdf"],
  },
}
