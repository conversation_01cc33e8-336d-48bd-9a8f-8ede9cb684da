import { anyone } from '@/access/anyone'
import { socialLinks } from '@/fields/socialLinks'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { metaTab } from '@/fields/metaTab'
import { overviewTab } from '@/fields/overviewTab'
import { contributor } from '@/access/contributor'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import { SyncLockField } from '@/fields/syncLockField'

export const Residencies: CollectionConfig = withSlug({
  slugSource: 'name',
  slug: 'residencies',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
    group: 'Events',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'eventBrand',
              type: 'relationship',
              relationTo: 'eventBrands',
            },
            {
              name: 'events',
              type: 'join',
              collection: 'events',
              on: 'residency',
              hasMany: true,
            },
            {
              name: 'Location',
              type: 'group',
              fields: [
                {
                  name: 'venue',
                  type: 'relationship',
                  relationTo: 'venues',
                  admin: {
                    condition: (_, siblingData) => !siblingData.geoLocation.locationName,
                  },
                },
                {
                  name: 'geoLocation',
                  type: 'group',
                  fields: [
                    {
                      name: 'locationName',
                      type: 'text',
                    },
                    {
                      name: 'link',
                      type: 'text',
                      admin: {
                        condition: (_, siblingData) => !!siblingData.locationName,
                      },
                    },
                  ],
                  admin: {
                    condition: (_, siblingData) => !siblingData.venue,
                  },
                },
              ],
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
