import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import { CollectionConfig } from 'payload'

export const Agencies: CollectionConfig = {
  slug: 'agencies',
  access: {
    create: contributor,
    delete: contributor,
    read: authenticated,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'address',
      type: 'text',
      required: true,
    },
    {
      name: 'generalContacInfo',
      type: 'group',
      fields: [
        {
          name: 'email',
          type: 'text',
          required: true,
        },
        {
          name: 'phoneNumber',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'agents',
      type: 'join',
      collection: 'agents',
      on: 'agency',
      hasMany: true,
    },
    // {
    //   name: 'artists',
    //   type: 'join',
    //   collection: 'artists',
    //   on: 'agencies',
    //   hasMany: true,
    // },
  ],
}
