import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import { CollectionConfig } from 'payload'

export const FanNotificationListners: CollectionConfig = {
  slug: 'fanNotificationListners',
  access: {
    create: contributor,
    delete: contributor,
    read: authenticated,
    update: contributor,
  },
  fields: [
    {
      name: 'type',
      type: 'select',
      options: [
        {
          label: 'Event Presale',
          value: 'eventPresale',
        },
      ],
      defaultValue: 'eventPresale',
      required: true,
    },
    {
      name: 'event',
      type: 'relationship',
      relationTo: 'events',
      required: true,
    },
    {
      name: 'fan',
      type: 'relationship',
      relationTo: 'fanUsers',
      required: true,
    },
  ],
}
