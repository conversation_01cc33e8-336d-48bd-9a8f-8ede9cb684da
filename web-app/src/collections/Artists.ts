import { anyone } from '@/access/anyone'
import { socialLinks } from '@/fields/socialLinks'
import { withSlug } from '@/utilities/_withSlug'
import { metaTab } from '@/fields/metaTab'
import { overviewTab } from '@/fields/overviewTab'
import { getArtist } from '@/app/(payload)/actions/event/get-artist'
import { contributor } from '@/access/contributor'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import { SyncLockField } from '@/fields/syncLockField'

export const Artists = withSlug({
  slugSource: 'name',
  slug: 'artists',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  endpoints: [
    {
      path: '/getArtist',
      method: 'get',
      handler: getArtist,
    },
  ],
  admin: {
    useAsTitle: 'name',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      name: 'search',
      type: 'text',
      virtual: true,
      admin: {
        position: 'sidebar',
        components: {
          Field: '@/components/SpotifyArtistSelect.tsx',
        },
      },
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'spotifyId',
              type: 'text',
              hidden: true,
              required: true,
            },
            {
              name: 'chartMetricExternalId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'externalSanityId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'country',
              type: 'relationship',
              relationTo: 'countries',
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
            {
              name: 'genres',
              type: 'relationship',
              relationTo: 'genres',
              hasMany: true,
            },
            {
              name: 'events',
              type: 'join',
              collection: 'events',
              on: 'lineup.artist',
            },
            {
              name: 'residencies',
              type: 'relationship',
              relationTo: 'residencies',
              hasMany: true,
            },
            {
              name: 'representation',
              type: 'array',
              label: 'Representation by region',
              fields: [
                {
                  name: 'territory',
                  label: 'Region or Country',
                  type: 'relationship',
                  relationTo: 'countries',
                  required: true,
                },
                {
                  name: 'coverage',
                  type: 'select',
                  options: [
                    { label: 'Global', value: 'global' },
                    { label: 'Continent', value: 'continent' },
                    { label: 'Country', value: 'country' },
                  ],
                  defaultValue: 'global',
                },
                {
                  name: 'agency',
                  type: 'relationship',
                  relationTo: 'agencies',
                  required: true,
                },
                {
                  name: 'agent',
                  type: 'relationship',
                  relationTo: 'agents',
                  filterOptions: ({ siblingData }) => {
                    const { agency } = siblingData as { agency?: string }
                    if (!agency) return true
                    return {
                      agency: { equals: agency },
                    }
                  },
                },
                {
                  name: 'managementCompany',
                  type: 'relationship',
                  relationTo: 'managmentCompanies',
                },
                {
                  name: 'manager',
                  type: 'relationship',
                  relationTo: 'managers',
                  filterOptions: ({ siblingData }) => {
                    const { managementCompany } = siblingData as { managementCompany?: string }
                    if (!managementCompany) return true
                    return {
                      managmentCompany: { equals: managementCompany },
                    }
                  },
                },
              ],
            },
            {
              name: 'managmentCompanies',
              type: 'relationship',
              relationTo: 'managmentCompanies',
              hasMany: true,
            },
            {
              name: 'managers',
              type: 'relationship',
              relationTo: 'managers',
              hasMany: true,
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
