import { anyone } from '@/access/anyone'
import { contributor } from '@/access/contributor'
import { SyncDiceOrders } from '@/app/(payload)/actions/sync/sync-dice-orders'
import { PLATFORM_OPTIONS } from '@/fields/platformOptions'
import { Order } from '@/payload-types'
import type { CollectionAfterChangeHook, CollectionConfig } from 'payload'

const hook: CollectionAfterChangeHook<Order> = async ({ req, doc }) => {
  const fan = (
    await req.payload.find({
      collection: 'fanUsers',
      where: {
        email: { equals: doc.fanEmail },
      },
      req,
    })
  ).docs[0]

  if (fan && fan.orders && !fan.orders.includes(doc) && !fan.orders.includes(doc.id))
    await req.payload.update({
      collection: 'fanUsers',
      id: fan.id,
      data: {
        orders: [...fan.orders.map((o) => (typeof o === 'object' ? o.id : o)), doc.id],
      },
      req,
    })
}

export const Orders: CollectionConfig = {
  slug: 'orders',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  endpoints: [
    {
      path: '/sync',
      method: 'get',
      handler: SyncDiceOrders,
    },
  ],
  admin: {
    group: 'Fan Data',
  },
  fields: [
    {
      name: 'externalDiceId',
      type: 'text',
      required: true,
      hidden: true,
    },
    {
      name: 'origin',
      type: 'select',
      options: PLATFORM_OPTIONS,
      hidden: true,
    },
    {
      name: 'externalResidentAdvisorId',
      type: 'text',
      hidden: true,
    },
    {
      name: 'externalEventBriteId',
      type: 'text',
      hidden: true,
    },
    {
      name: 'event',
      type: 'relationship',
      relationTo: 'events',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'fanEmail',
      type: 'text',
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'fan',
      type: 'relationship',
      relationTo: 'fanUsers',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'tickets',
      type: 'relationship',
      relationTo: 'tickets',
      hasMany: true,
      admin: {
        readOnly: true,
      },
    },
    // need separate collections for this?
    // {
    // 	name: "returns",
    // 	type: "relationship",
    // 	relationTo: "tickets",
    // 	hasMany: true,
    // 	required: true,
    // },
    // {
    // 	name: "adjustments",
    // 	type: "relationship",
    // 	// relationTo: "tickets",
    // 	hasMany: true,
    // 	required: true,
    // },
  ],
  hooks: {
    afterChange: [hook],
  },
}
