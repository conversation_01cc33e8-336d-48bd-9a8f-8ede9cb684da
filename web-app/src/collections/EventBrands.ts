import { anyone } from '@/access/anyone'
import { socialLinks } from '@/fields/socialLinks'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { metaTab } from '@/fields/metaTab'
import { overviewTab } from '@/fields/overviewTab'
import { contributor } from '@/access/contributor'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import { SyncLockField } from '@/fields/syncLockField'

export const EventBrands: CollectionConfig = withSlug({
  slugSource: 'name',
  slug: 'eventBrands',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'country',
              type: 'relationship',
              relationTo: 'countries',
            },
            {
              name: 'residencies',
              type: 'join',
              collection: 'residencies',
              on: 'eventBrand',
            },
            {
              name: 'events',
              type: 'join',
              collection: 'events',
              on: 'eventBrand',
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
