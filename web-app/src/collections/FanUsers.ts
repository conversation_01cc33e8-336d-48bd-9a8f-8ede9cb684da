import { anyone } from '@/access/anyone'
import { contributor } from '@/access/contributor'
import type { CollectionConfig } from 'payload'

export const FanUsers: CollectionConfig = {
  slug: 'fanUsers',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'firstName',
    group: 'Fan Data',
  },
  fields: [
    {
      name: 'firstName',
      type: 'text',
      required: true,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
    },
    {
      name: 'email',
      type: 'text',
      required: true,
    },
    {
      name: 'phoneNumber',
      type: 'text',
    },
    {
      name: 'phoneNumberCountryCode',
      type: 'text',
    },
    {
      name: 'dateOfBirth',
      type: 'date',
      required: true,
    },
    {
      name: 'ethWallet',
      type: 'text',
    },
    {
      name: 'followingsGroup',
      type: 'group',
      fields: [
        {
          name: 'eventBrandsFollowings',
          type: 'relationship',
          relationTo: 'eventBrands',
          hasMany: true,
        },
        {
          name: 'festivalsFollowings',
          type: 'relationship',
          relationTo: 'genres',
          hasMany: true,
        },
        {
          name: 'artistsFollowings',
          type: 'relationship',
          relationTo: 'artists',
          hasMany: true,
        },
        {
          name: 'authorsFollowings',
          type: 'relationship',
          relationTo: 'authors',
          hasMany: true,
        },
        {
          name: 'genresFollowings',
          type: 'relationship',
          relationTo: 'genres',
          hasMany: true,
        },
      ],
    },
    {
      name: 'orders',
      type: 'relationship',
      relationTo: 'orders',
      hasMany: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'diceId',
      type: 'text',
      hidden: true,
    },
  ],
}
