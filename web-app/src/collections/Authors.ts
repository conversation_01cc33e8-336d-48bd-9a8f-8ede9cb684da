import { anyone } from '@/access/anyone'
import { socialLinks } from '@/fields/socialLinks'
import type { CollectionConfig } from 'payload'
import { withSlug } from '@/utilities/_withSlug'
import { overviewTab } from '@/fields/overviewTab'
import { metaTab } from '@/fields/metaTab'
import { contributor } from '@/access/contributor'

export const Authors: <AUTHORS>
  slugSource: 'name',
  slug: 'authors',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'country',
              type: 'relationship',
              relationTo: 'countries',
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
