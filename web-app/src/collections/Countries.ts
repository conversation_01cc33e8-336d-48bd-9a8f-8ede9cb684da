import { anyone } from '@/access/anyone';
import { contributor } from '@/access/contributor';
import type { CollectionConfig } from 'payload';

export const Countries: CollectionConfig = {
	slug: "countries",
	access: {
		create: contributor,
		delete: contributor,
		read: anyone,
		update: contributor,
	},
	admin: {
		useAsTitle: 'name',
	},
	fields: [
		{
			name: "name",
			type: "text",
			required: true,
		},
		{
			name: "code",
			type: "text",
			required: true,
		},
		{
			name: "flag",
			type: "upload",
			relationTo: "media",
			required: true,
		},
	],
}