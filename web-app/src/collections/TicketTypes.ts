import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import { syncDiceTicketTypes } from '@/app/(payload)/actions/sync/sync-dice-ticketTypes'
import { PLATFORM_OPTIONS } from '@/fields/platformOptions'
import { SyncLock<PERSON>ield } from '@/fields/syncLockField'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import type { CollectionConfig } from 'payload'

export const TicketTypes: CollectionConfig = {
  slug: 'ticketTypes',
  access: {
    create: contributor,
    read: authenticated,
    update: contributor,
    delete: contributor,
  },
  admin: {
    group: 'Events',
    useAsTitle: 'label',
  },
  endpoints: [
    {
      path: '/sync',
      method: 'get',
      handler: syncDiceTicketTypes,
    },
  ],
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      name: 'origin',
      type: 'select',
      options: PLATFORM_OPTIONS,
      hidden: true,
    },
    {
      name: 'externalDiceId',
      type: 'group',
      unique: true,
      fields: [
        {
          name: 'ticketTypeId',
          type: 'text',
          required: true,
          admin: { description: 'ID of the parent ticket type' },
        },
        {
          name: 'priceTierId',
          type: 'text',
          required: true,
          admin: { description: 'ID of this specific price tier' },
        },
      ],
      hidden: true,
    },
    {
      name: 'externalResidentAdvisorId',
      type: 'text',
      hidden: true,
    },
    {
      name: 'externalEventBriteId',
      type: 'text',
      hidden: true,
    },
    {
      name: 'ticketTypeName',
      type: 'text',
      required: true,
      admin: { description: 'Name of the ticket type' },
    },
    {
      name: 'label',
      type: 'text',
      required: true,
      admin: { description: 'Label of this price tier (e.g. “Last Chance”)' },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: { description: 'Parent ticket type description' },
    },
    {
      name: 'archived',
      type: 'checkbox',
      defaultValue: false,
      admin: { description: 'Is the parent ticket type archived?' },
    },
    {
      name: 'internalPresentationOnly',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'allocation',
      type: 'number',
      required: true,
      admin: { description: 'Total tickets allocated for parent type' },
    },

    {
      label: 'Face Value',
      name: 'faceValue',
      type: 'number',
      required: true,
      admin: { description: 'Face value of this tier' },
    },
    {
      name: 'fee',
      type: 'array',
      fields: [
        {
          type: 'checkbox',
          name: 'applicable',
          defaultValue: true,
          admin: { description: 'Is this fee applicable?' },
        },
        {
          type: 'number',
          name: 'amount',
          required: true,
          admin: { description: 'Amount of the fee' },
        },
        {
          type: 'number',
          name: 'computed',
          required: true,
        },
        {
          type: 'text',
          name: 'type',
          admin: { description: 'Type of the fee' },
          required: true,
        },
        {
          type: 'text',
          name: 'unit',
          required: true,
        },
      ],
    },
    {
      name: 'totalFee',
      type: 'number',
      admin: { description: 'Platform fee portion' },
    },
    {
      name: 'total',
      type: 'number',
      admin: { description: 'Amount paid by fan (price + fee)' },
    },
    {
      name: 'tickets',
      type: 'join',
      collection: 'tickets',
      on: 'ticketType',
      hasMany: true,
      admin: {
        description: 'All tickets that use this ticket type',
      },
    },
  ],
}
