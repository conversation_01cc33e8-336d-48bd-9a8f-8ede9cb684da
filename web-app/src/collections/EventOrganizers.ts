import { canMutateTenant } from '@/access/byTenant'
import { contributor } from '@/access/contributor'
import { updateAndDeleteAccess } from '@/access/updateAndDelete'
import { metaTab } from '@/fields/metaTab'
import { overviewTab } from '@/fields/overviewTab'
import { SyncLockField } from '@/fields/syncLockField'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import { CryptoService } from '@/lib/encryption/service'
import { withSlug } from '@/utilities/_withSlug'
import { FieldHook } from 'payload'

const cryptoSvc = new CryptoService()

const beforeChange = (args: any) => {
  const { value } = args
  if (value && typeof value === 'string') {
    return cryptoSvc.encrypt(value)
  }
  return value
}

const beforeChangeHook: FieldHook[] = [beforeChange]

const admin = {
  components: {
    Field: '@/components/EncryptedSecretField.tsx',
  },
}
const hooks = {
  beforeChange: beforeChangeHook,
}
export const EventOrganizers = withSlug({
  slugSource: 'name',
  slug: 'eventOrganizers',
  access: {
    create: contributor, // TODO: should be accessible for superadmin and jobs
    delete: updateAndDeleteAccess,
    read: canMutateTenant,
    update: updateAndDeleteAccess,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'presentedBy', 'createdAt'],
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'presentedBy',
              type: 'text',
            },
            {
              name: 'diceCredentials',
              type: 'group',
              admin: {
                disableListColumn: true,
              },
              fields: [
                { name: 'login', type: 'text', admin, hooks },
                { name: 'password', type: 'text', admin, hooks },
                {
                  name: 'DICE_PARTNER_API_TOKEN',
                  label: 'Dice Partner API Token',
                  type: 'text',
                  admin,
                  hooks,
                },
              ],
            },
            {
              name: 'residentAdvisorCredentials',
              type: 'group',
              admin: {
                disableListColumn: true,
              },
              fields: [
                { name: 'login', type: 'text', admin, hooks },
                { name: 'password', type: 'text', admin, hooks },
              ],
            },
            {
              name: 'qflowCredentials',
              type: 'group',
              admin: {
                disableListColumn: true,
              },
              fields: [
                { name: 'login', type: 'text', admin, hooks },
                { name: 'password', type: 'text', admin, hooks },
              ],
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
