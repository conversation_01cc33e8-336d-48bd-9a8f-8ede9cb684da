import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import { CollectionConfig } from 'payload'

export const Agents: CollectionConfig = {
  slug: 'agents',
  access: {
    create: contributor,
    delete: contributor,
    read: authenticated,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'generalContacInfo',
      type: 'group',
      fields: [
        {
          name: 'email',
          type: 'text',
          required: true,
        },
        {
          name: 'phoneNumber',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'agency',
      type: 'relationship',
      relationTo: 'agencies',
      required: true,
    },
    {
      name: 'automatedReportingEnabled',
      type: 'checkbox',
      defaultValue: false,
    },
  ],
}
