  query getEventWithTicketTypes($id: String!) {
    viewer {
      events(first: 1, where: { eventIdLive: { eq: $id } }) {
        edges {
                node {
                    ticketType
                    ticketTypes {
                        allocation
                        archived
                        description
                        id
                        name
                        priceTiers {
                            id
                            name
                            priceBreakdown {
                                faceValue
                                fees
                                total
                                    breakdown {
                                    active
                                    amount
                                    applicable
                                    computed
                                    type
                                    unit
                                }
                            }
                        }
                    }
                }
        }
      }
      id
    }
  }