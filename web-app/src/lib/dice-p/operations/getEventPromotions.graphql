query getEventPromotions($id: ID!) {
  viewer {
    events(first: 1, where: { id: { eq: $id } }) {
      edges {
        node {
          eventPromotions {
            accessType
            code
            endDate
            fanFacingName
            id
            isEnded
            maxRedemptions
            name
            promotionType
            startDate
            codeLocks(first: 10000) {
              edges {
                node {
                  code
                  createdUses
                  disabled
                  id
                  link
                  status
                  unlimited
                  usageStatus
                  uses
                  validFrom
                  validTo
                }
              }
            }
          }
        }
      }
    }
  }
}
