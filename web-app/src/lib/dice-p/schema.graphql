schema {
  query: RootQueryType
  mutation: RootMutationType
}

type Account implements Node {
  accountUsers(after: String, before: String, first: Int, last: Int, permissionProfileIds: [ID!], term: String): UserAccountObjectConnection
  addressCountry: String!
  allowSkipReview: Boolean!
  allowedAdhocFees(eventId: ID): [Fee]
  allowedForFiscalDataChanges: Boolean!
  automaticRollingPaymentsEnabled: Boolean
  availableEventsForPayout: [Event]
  billingAccountDueDate: Time
  countryCode: String
  disabledReason: EnumAccountDisabledReason
  displayName: String
  eventsApiToken: EventsApiToken
  extrasEnabled: Boolean
  facebookPage: FacebookPage
  financialDataProvided: Boolean!
  forbidSelfPayouts: Boolean
  hasEuEvents: Boolean
  hasUsEvents: Boolean
  holdPayouts: Boolean

  """The ID of an object"""
  id: ID!
  integrationTokens: [IntegrationToken]
  isDisabled: Boolean
  licenseNumber: String
  mailchimp: Mailchimp
  merchEnabled: Boolean
  name: String!
  stripeAccountId: String
  stripeAccountState: StripeAccountIntegrityState!
  stripeAccountType: String
  stripeLoginUrl: String
  stripeOauthUrl(app: String): String!
  taxFormProvided: Boolean!
  typeOfOrganizer: EnumTypeOfOrganizer
  vatNumber: String
  vatNumberProvided: Boolean!
}

type AccountAvailability {
  account: Account!
  disabledReason: EnumAccountDisabledReason!
  isDisabled: Boolean!
  membershipType: AccountMembershipType!
  permissionProfile: PermissionProfile!
}

type AccountBalance {
  accountId: String!
  available: Map!
  pending: Map!
}

enum AccountMembershipType {
  COLLABORATORS
  MEMBER
}

type AccountUser {
  account: Promoter
  current: Boolean!
  permissionProfile: PermissionProfile
}

type AccountUserInvitation implements Node {
  email: String!

  """The ID of an object"""
  id: ID!
  invitedBy: User
  permissionProfile: PermissionProfile
  status: EnumAccountUserInvitaionStatus
  user: User
}

type Action {
  category: String
  name: String
}

input ActionInput {
  name: String
}

type ActivitiesConnection {
  edges: [ActivitiesEdge]
  pageInfo: PageInfo!
}

type ActivitiesEdge {
  cursor: String
  node: Activity
}

type Activity implements Node {
  date: Time

  """The ID of an object"""
  id: ID!
  itemId: Int!
  itemType: ActivityItemType!
  legacy: Boolean
  onBehalfOfUser: PamUser
  payload: Map
  type: ActivityType!
  user: PamUser
}

enum ActivityItemType {
  Artist
  Event
  Label
  PermissionProfile
  Promoter
  TicketType
  Venue
  VenueArea
}

enum ActivityType {
  change
  comment
  creation
  event_changed_notification
  markback
  synchronisation
  tier_closure
}

input ActivityWhereInput {
  date: OperatorsDateInput
  itemId: ID
  itemType: OperatorsString
  legacy: OperatorsBooleanEqInput
  onlyImpersonated: Boolean
  type: OperatorsString
}

type AdditionalArtist {
  description: String
  hierarchicalTags: [HierarchicalTag]
  id: ID!
  name: String
}

input AdditionalArtistInput {
  description: String
  hierarchicalTagIds: [ID]
  id: ID
  name: String
}

input AdditionalCurrencyInput {
  currency: EventCostCurrency
  token: String
}

type AdjustmentsStats {
  completed: Int!
  failed: Int!
  notProcessed: Int!
}

"""admission config"""
type AdmissionConfig implements Node {
  description: String
  enabled: Boolean
  fromShift: Int!

  """The ID of an object"""
  id: ID!
  name: String!
  note: String
  recurringLabels: [String]
  ticketTypes: [TicketType]
  toShift: Int!
  uniqueness: ConfigUniqueness!
}

"""
admission log: use to sync scanned tickets with server and other scanners
"""
input AdmissionLogInput {
  configId: ID
  scannedAt: Time!
  scanningUser: String!
  source: String!
  status: AdmissionLogStatus!
  ticketId: Int!
  ticketTypeId: Int!
}

"""the different admission_log statuses"""
enum AdmissionLogStatus {
  """In"""
  CHECKED_IN

  """
  prevent this ticket to be scanned (ex: the ticket is revoked
        after the devices downloaded the list of admission tickets)
  """
  DENIED

  """
  In but invalid ticket (ex: no ticket, not in the list,
          scanning issue, wrong time, wrong entrance...)
  """
  FORCED

  """ In (searched manually from guestlist)"""
  MANUAL_CHECKED_IN

  """Not scanned yet"""
  PENDING
}

"""admission_ticket: represents a ticket entrance"""
type AdmissionTicket implements Node {
  code: String!
  fanEmail: String
  fanFirstName: String
  fanId: String!
  fanLastName: String
  fanPhoneNumber: String

  """The ID of an object"""
  id: ID!
  source: String!
  status: AdmissionLogStatus!
  ticketId: Int!
  ticketTypeId: Int!
}

type AdmissionTicketsConnection {
  edges: [AdmissionTicketsEdge]
  pageInfo: PageInfo!
}

type AdmissionTicketsEdge {
  cursor: String
  node: AdmissionTicket
}

type AgeBreakdownItem {
  count: Int!
  from: Int!
  to: Int!
}

type AggregatedSalesReport {
  eventsCount: Int!
  onSaleEventsCount: Int!
  soldTickets: Int!
  totalAllocation: Int!
}

type AllowedActions {
  addExtras: Boolean @deprecated(reason: "Please use add_products instead")
  addProducts: Boolean
  changeAllocation: Boolean
  changeDates: Boolean
  closeTier: Boolean
  createSocialLinks: Boolean
  downloadC1Form: Boolean
  downloadChargebacksReport: Boolean
  downloadDoorlist: Boolean
  downloadDoorlistExtras: Boolean
  downloadDoorlistMerch: Boolean
  downloadSalesReport: Boolean
  exportReturnRequests: Boolean
  inviteExternalGuest: Boolean
  manageBoxOffice: Boolean
  manageCodeLocks: Boolean
  manageDataCollection: Boolean
  manageDiscounts: Boolean
  manageExtrasAllocation: Boolean @deprecated(reason: "Please use manage_products_allocation instead")
  manageFacebook: Boolean
  manageLinks: Boolean
  manageProductsAllocation: Boolean
  managePromotions: Boolean
  managePromotionsMaxRedemptions: Boolean
  manageTicketPools: Boolean
  manageTickets: Boolean
  markBackEvent: Boolean
  minorUpdate: Boolean
  performPayout: Boolean
  readAdvancedStats: Boolean
  readAllCustomerData: Boolean
  readAnalytics: Boolean
  readDoorSalesActivities: Boolean
  readDoorlist: Boolean
  readDoorlistExtras: Boolean
  readDoorlistMerch: Boolean
  readExtras: Boolean
  readMarketingOptIns: Boolean
  readMerch: Boolean
  readSocialLinks: Boolean
  toggleFanSeatSelection: Boolean
}

type AllowedLifecycleUpdateBase {
  canUpdate: Boolean
}

type AllowedLifecycleUpdates {
  ageLimit: AllowedLifecycleUpdateBase
  announceDate: AllowedLifecycleUpdateBase
  artistIds: AllowedLifecycleUpdateBase
  attractiveCompatibilityAe: AllowedLifecycleUpdateBase
  billingPromoter: AllowedLifecycleUpdateBase
  date: AllowedLifecycleUpdateBase
  description: AllowedLifecycleUpdateBase
  diceStreamDuration: AllowedLifecycleUpdateBase
  diceStreamDvrEnabled: AllowedLifecycleUpdateBase
  diceStreamRewatchEnabledUntil: AllowedLifecycleUpdateBase
  endDate: AllowedLifecycleUpdateBase
  eventImages: AllowedLifecycleUpdateBase
  eventRules: AllowedLifecycleUpdateBase
  extraNotes: AllowedLifecycleUpdateBase
  lineUp: AllowedLifecycleUpdateBase
  links: AllowedLifecycleUpdateBase
  manualValidationEnabled: AllowedLifecycleUpdateBase
  maxTicketsLimit: AllowedLifecycleUpdateBase
  media: AllowedLifecycleUpdateBase
  name: AllowedLifecycleUpdateBase
  offSaleDate: AllowedLifecycleUpdateBase
  onSaleDate: AllowedLifecycleUpdateBase
  paidWaitingList: AllowedLifecycleUpdateBase
  presentedBy: AllowedLifecycleUpdateBase
  products: AllowedLifecycleUpdatesProducts
  recurringEvents: AllowedLifecycleUpdateBase
  requiresBoxOfficeTicketNomination: AllowedLifecycleUpdateBase
  requiresTicketNomination: AllowedLifecycleUpdateBase
  restrictCountries: AllowedLifecycleUpdateBase
  restrictCountriesKind: AllowedLifecycleUpdateBase
  sendReceiptViaSms: AllowedLifecycleUpdateBase
  streamEmbedCode: AllowedLifecycleUpdateBase
  streamLink: AllowedLifecycleUpdateBase
  ticketPools: AllowedLifecycleUpdatesTicketPools
  ticketTypes: AllowedLifecycleUpdatesTicketTypes
  venues: AllowedLifecycleUpdateBase
}

type AllowedLifecycleUpdatesProducts {
  canAdd: Boolean
  canChangeAllocation: Boolean
  canChangeOrder: Boolean
  canDelete: Boolean
  canUpdate: Boolean
  canUpdateImages: [ID]
  canUpdatePrice: [ID]
}

type AllowedLifecycleUpdatesTicketPools {
  canAdd: Boolean
  canChangeAllocation: Boolean
  canUpdate: Boolean
  removablePools: [ID]
}

type AllowedLifecycleUpdatesTicketTypes {
  canAdd: Boolean
  canChangeAllocation: Boolean
  canChangeDoorSalesEnabled: Boolean
  canChangeExternalSkus: Boolean
  canChangeIcon: Boolean
  canChangeIncrements: Boolean
  canChangeLimit: Boolean
  canChangeOffSaleDate: Boolean
  canChangeOnSaleDate: Boolean
  canChangeOrder: Boolean
  canChangeSeatmap: Boolean
  canChangeTierNames: Boolean
  canDelete: Boolean
  canHide: Boolean
  canUpdate: Boolean
  updateablePriceTiers: [ID]
  updateablePriceTtys: [ID]
}

type AppleMusicSearchResponse {
  resultCount: Int!
  results: [AppleMusicTrack]!
}

type AppleMusicTrack {
  image: String
  name: String
  openUrl: String
  previewUrl: String
}

type AppleMusicViewer {
  search(q: String!): AppleMusicSearchResponse
}

input ArchiveFanConnectInput {
  clientMutationId: String!
  id: ID!
}

type ArchiveFanConnectPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

type Artist implements Name & Node {
  activities(after: String, before: String, first: Int, last: Int): ActivitiesConnection @deprecated(reason: "To be removed, please use viewer.activities instead")
  artistEvents(scopes: EventScopesInput): [ArtistEvent]
  backendArtistIds: [String]
  createdAt: Time
  description: String
  destinationAccount: DestinationAccount
  disambiguation: String
  events(after: String, before: String, first: Int, last: Int): EventConnection
  hierarchicalTags: [HierarchicalTag]

  """The ID of an object"""
  id: ID!
  inventory: Inventory
  links: [Map]!
  media: [MediaItem]!
  musicbrainzId: String
  name: String
  performerType: PerformerType!
  permName: String
  profileImageAttachment: Attachment
  profileImageCropRegion: CropRegion
  tags: [Tag]
  updatedAt: Time
}

type ArtistConnection {
  count: Int!
  edges: [ArtistEdge]
  pageInfo: PageInfo!
}

type ArtistEdge {
  cursor: String
  node: Artist
}

"""Short version of an event to show it on artist merch field"""
type ArtistEvent implements Node {
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressState: String
  announceDate: Time
  costCurrency: EventCostCurrency
  countryCode: String
  date: Time
  endDate: Time
  eventId: ID
  eventIdLive: String
  eventImages: [EventImage]
  fullAddress: String

  """The ID of an object"""
  id: ID!
  latitude: Float
  longitude: Float
  name: String
  offSaleDate: Time
  onSaleDate: Time
  organicSocialLink: String
  postOfficeBoxNumber: String
  postalCode: String
  streetAddress: String
  timezoneName: String
  venueName: String
}

input ArtistWhereInput {
  _or: [ArtistWhereInput]
  musicbrainzId: Uuid
  name: OperatorsString
  spotifyArtistId: String
}

input AssignProductsToEventsInput {
  clientMutationId: String!
  inventoryId: ID
  productToEvents: [ProductToEventsInput]
}

type AssignProductsToEventsPayload {
  clientMutationId: String!
  inventory: Inventory
}

union AssociatedEntities = Promoter | Venue

input AttachTicketPoolInput {
  id: ID
  maxAllocation: Int
  name: String
}

type Attachment implements Node {
  cdnUrl: String!

  """The ID of an object"""
  id: ID!
}

type AttractiveFields {
  """field dedicated to siae_genre_type: 01"""
  author: String
  compatibilityAe: CompatibilityAe

  """field dedicated to siae_genre_type: 01"""
  distributor: String
  entertainmentPercent: Int
  forceSubscription: Boolean
  forceSubscriptionLimit: Int
  integrationDisabled: Boolean!
  linkedEvents: [Event]

  """field dedicated to siae_genre_type: 01"""
  nationality: String

  """field dedicated to siae_genre_type: 45"""
  performer: String

  """field dedicated to siae_genre_type: 01"""
  producer: String
  seatingAreaConfigs: [SeatingAreaConfig]!
  siaeGenreType: String
  streamingTicketsIntegrationDisabled: Boolean!
  subscriptionCode: String
  subscriptionEventsLimit: Int
  taxFree: Boolean
}

input AttractiveFieldsInput {
  """field dedicated to siae_genre_type: 01"""
  author: String
  compatibilityAe: CompatibilityAe

  """field dedicated to siae_genre_type: 01"""
  distributor: String
  entertainmentPercent: Int
  forceSubscription: Boolean
  forceSubscriptionLimit: Int
  id: ID
  integrationDisabled: Boolean
  linkedEventIds: [ID]

  """field dedicated to siae_genre_type: 01"""
  nationality: String

  """field dedicated to siae_genre_type: 45"""
  performer: String

  """field dedicated to siae_genre_type: 01"""
  producer: String
  seatingAreaConfigs: [SeatingAreaConfigInput]
  siaeGenreType: String
  streamingTicketsIntegrationDisabled: Boolean
  subscriptionCode: String
  subscriptionEventsLimit: Int
  taxFree: Boolean
}

type AttractiveTicket {
  activationCard: String
  agency: String
  agencyId: Int
  barcode: String
  barcodeAlt: String
  block: String
  blockId: Int
  businessName: String
  city: String
  emissionDate: Time
  emissionId: Int
  eventDate: Time
  eventDateId: Int
  eventId: Int
  eventTitle: String
  eventsEntered: Int
  eventsQuantity: Int
  figurativeAmount: Float
  figurativeVat: Float
  firstName: String
  fiscalDate: Time
  fiscalSeal: String
  genre: String
  genreDescription: String
  isAbbonamento: Boolean
  isVoided: Boolean
  lastName: String
  location: String
  movementValue: Float
  note: String
  noteState: String
  ntcEntranceDate: Time
  orderCode: String
  orderReference: String
  originalEmissionId: Int
  originalFirstName: String
  originalLastName: String
  personalId: Int
  phone: String
  piHolder: String
  prepaidVat: String
  prepaidVatDescription: String
  preprint: String
  presaleFee: Float
  presaleVat: Float
  price: Float
  priceType: String
  priceVat: Float
  printDate: Time
  progressiveNumber: String
  progressiveSector: Int
  promoterFiscalCode: String
  promoterName: String
  reason: String
  reissueEmissionId: Int
  row: String
  seat: String
  seatingArea: String
  seatingAreaId: Int
  state: String
  subscriptionCode: String
  subscriptionFiscalCode: String
  subscriptionProgressiveNumber: String
  subscriptionText: String
  supportCode: String
  supportType: String
  systemNumber: String
  terminalId: Int
  ticketType: String
  timestamp: String
  turn: String
  validity: Time
  venue: String
  venueCode: String
  voidedTicketEmissionId: Int
  voidingCard: String
  voidingDate: Time
  voidingFiscalSeal: String
  voidingProgressiveNumber: String
  voidingReason: String
  voidingTransactionEmissionId: Int
  voidingType: String
}

type AttractiveTicketConnection {
  edges: [AttractiveTicketEdge]
  pageInfo: PageInfo!
}

type AttractiveTicketEdge {
  cursor: String
  node: AttractiveTicket
}

type AutoRescheduledEventRefunds {
  active: Boolean
  cutoffDays: Int
}

input AutoRescheduledEventRefundsInput {
  active: Boolean
  cutoffDays: Int
}

type AutomaticRollingPaymentsConfiguration {
  active: Boolean
  holdbackForDisputes: Float
  holdbackInterval: Int
  payoutPercent: Float
  reportOnly: Boolean
  reportRecipients: [String]
}

input AutomaticRollingPaymentsConfigurationInput {
  active: Boolean
  holdbackForDisputes: Float
  holdbackInterval: Int
  payoutPercent: Float
  reportOnly: Boolean
  reportRecipients: [String]
}

type BackendEvent {
  address: String
  artistIds: [String]
  backgroundImage: String
  barCodeType: String
  cityIds: [String]
  codeLockedImage: String
  colour: Map
  cost: BackendEventCost
  date: String
  dateEnd: Time
  deleted: Boolean
  description: String
  faqs: [BackendFaq]
  flags: [String]
  hidden: Boolean
  id: String
  images: [String]
  lineup: Map
  location: BackendEventLocation
  locked: Boolean
  maxTicketsLimit: Int
  media: Map
  name: String
  payments: Map
  permName: String
  price: Map
  relatedEventIds: Map
  returns: Map
  saleStartDate: Time
  status: String
  tags: [String]
  ticketImage: String
  ticketTypes: [BackendTicketType]
  totalNumTickets: Int
  transfer: BackendEventTransfer
  userIds: [String]
  venue: String
}

type BackendEventCost {
  amount: Int!
  currency: String!
  feeAmount: Int
}

type BackendEventLocation {
  accuracy: Float!
  lat: Float!
  lng: Float!
  place: String!
}

type BackendEventTransfer {
  deadline: Time
  mode: Int!
}

type BackendFaq {
  body: String
  id: ID
  order: Int
  title: String
}

type BackendTicketType {
  id: ID
  name: String
  tiers: [Map]
}

type BalanceBatch {
  balanceChange: Int!
  completedAt: Time
  currency: String!
  destinationAccount: String!
  eventId: Int!
  intId: Int!
  region: String!
  transfer: StripeTransfer
  transferReversals: [StripeTransferReversal]!
}

enum BasePriceMode {
  FACE_VALUE
  FACE_VALUE_PLUS_VENUE_LEVY
}

"""admission base report"""
type BaseReport {
  notScanned: Int!
  scanned: Int!
  total: Int!
}

type BillingDestination {
  billingAccount: Promoter
}

input BoxOfficePaymentInput {
  amount: Int
  clientMutationId: String!
  id: ID!
  terminalSerialId: String
  ticketIds: [Int]
}

type BoxOfficePaymentPayload {
  clientMutationId: String!
  secret: String!
  taxAmount: Int!
}

input BulkUpdateFanSurveyQuestionsInput {
  clientMutationId: String!
  eventId: ID!
  fanQuestionsInput: [FanQuestionInput]
}

type BulkUpdateFanSurveyQuestionsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Event

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

"""Custom event bundles"""
type Bundle implements Node {
  canManage: Boolean
  city: City
  events: [Event]
  expiryDate: Date
  fromDate: Date
  hidden: Boolean!

  """The ID of an object"""
  id: ID!
  maxPrice: Int
  minPrice: Int
  name: String!
  owner: Promoter
  permName: String
  profileDetails: ProfileDetails
  promoterCurator: Promoter
  promoters: [Promoter]
  socialLinks: [SocialLink]
  toDate: Date
  venueCurator: Venue
}

type BundleConnection {
  count: Int!
  edges: [BundleEdge]
  pageInfo: PageInfo!
}

type BundleEdge {
  cursor: String
  node: Bundle
}

enum BundleOrder {
  EXPIRY_DATE_ASC
  EXPIRY_DATE_DESC
}

input BundleWhereInput {
  _or: [BundleWhereInput]
  active: OperatorsBooleanEqInput
  id: OperatorsIdEqInput
  name: OperatorsString
}

input CancelReportInput {
  clientMutationId: String!
  id: ID!
}

type CancelReportPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ReportOrSchedule

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

type Category implements Node {
  categories: [Category]!
  coverBackgroundColor: String
  coverImageUrl: String
  hidden: Boolean!

  """The ID of an object"""
  id: ID!
  name: String!
  parentCategory: Category
  rootType: ProductRootType
  type: CategoryType
}

type CategoryBreakdown {
  category: Category
  productBreakdown: [ProductStats]
}

enum CategoryType {
  ACCESS_PASS
  AFTER_PARTY_PASS
  ARTIST_MEET_AND_GREET
  ARTIST_MERCH
  CAMPING
  COACH_BUS
  DRINKS_ALCOHOLIC
  DRINKS_NON_ALCOHOLIC
  EARLY_ENTRY_PASS
  EXPERIENCE
  FOOD
  FOOD_AND_DRINK
  JUMPER
  MERCH
  OTHER
  PARKING
  QUEUE_JUMP
  TRAVEL_AND_ACCOMMODATION
  T_SHIRT
  VIP_UPGRADE
}

input ChangeAllocationInput {
  allocation: Int!
  clientMutationId: String!
  id: ID!
}

type ChangeAllocationPayload {
  clientMutationId: String!
  object: Event
}

input ChangeDatesInput {
  announceDate: Time!
  clientMutationId: String!
  id: ID!
  onSaleDate: Time!
}

type ChangeDatesPayload {
  clientMutationId: String!
  event: Event
}

enum ChannelType {
  APP_SALE
  BOX_OFFICE_SALE
  HOLD
  NON_DICE
  OTHER
}

type Characteristic implements Node {
  """The ID of an object"""
  id: ID!
  level: CharacteristicLevel
  name: String
}

type CharacteristicConnection {
  count: Int!
  edges: [CharacteristicEdge]
  pageInfo: PageInfo!
}

type CharacteristicEdge {
  cursor: String
  node: Characteristic
}

enum CharacteristicLevel {
  event
  venue
}

input CharacteristicWhereInput {
  level: CharacteristicLevel
  name: OperatorsString
}

type Charge {
  paymentMethodType: String
  stripeLink: String
}

type ChartManagerCredentials {
  chartId: String!
  publicKey: String!
  secretKey: String!
}

type Checklist {
  items: [ChecklistItem]!
  name: String!
  uuid: String!
}

input ChecklistInput {
  items: [ChecklistItemInput]!
  name: String!
  uuid: String!
}

type ChecklistItem {
  completed: Boolean!
  name: String
  uuid: String!
}

input ChecklistItemInput {
  completed: Boolean!
  name: String!
  uuid: String!
}

interface ChecklistsNode {
  checklists: [Checklist]!
  id: ID!
}

input CitiesWhereInput {
  name: String
}

type City {
  code: String!
  countryId: String!
  countryName: String!
  countryPermName: String!
  id: String!
  location: CityLocation
  name: String!
  permName: String!
}

type CityLocation {
  accuracy: Int!
  lat: Float!
  lng: Float!
  place: String!
}

input CloneEventInput {
  clientMutationId: String!
  frequency: RepeatFrequency!
  id: ID!
  occurrences: Int
  repeatEnds: RepeatEnds!
  repeatOn: RepeatOn
  until: Time
}

type CloneEventPayload {
  clientMutationId: String!
  events: [Event]
}

input CloneEventPromotionInput {
  clientMutationId: String!
  id: ID!
}

type CloneEventPromotionPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventPromotion

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

type CodeLock implements Node {
  code: String!
  createdUses: Int
  disabled: Boolean

  """The ID of an object"""
  id: ID!
  link: String
  status: CodeLockStatus!
  ticketTypes: [TicketType]
  unlimited: Boolean
  usageStatus: CodeLockUsageStatus!
  uses: Int
  validFrom: Time
  validTo: Time
}

type CodeLockConnection {
  count: Int!
  edges: [CodeLockEdge]
  pageInfo: PageInfo!
}

type CodeLockEdge {
  cursor: String
  node: CodeLock
}

input CodeLockFilter {
  code: String
  status: CodeLockUsageStatus
}

enum CodeLockStatus {
  AVAILABLE
  DELETED
  UNLIMITED_USE
  USED
}

enum CodeLockUsageStatus {
  DISABLED
  NOT_USED
  USED
}

type CodeUsage {
  code: String!
  name: String!
  ticketType: TicketType
  ticketsSold: Int!
  usedOn: Time!
}

type CodeUsageConnection {
  count: Int!
  edges: [CodeUsageEdge]
  pageInfo: PageInfo!
}

type CodeUsageEdge {
  cursor: String
  node: CodeUsage
}

enum CodesOperationType {
  CREATE_CODES
  DELETE_CODES
  DISABLE_CODES
  ENABLE_CODES
  UPLOAD_CODES
}

input CodesOperationsInput {
  allCodes: Boolean
  amount: Int
  codes: [String]
  filename: String
  force: Boolean
  operationType: CodesOperationType!
}

input ColourInput {
  alpha: Int
  blue: Int
  green: Int
  red: Int
}

enum CompatibilityAe {
  _0
  _1000
  _5000
}

type Config {
  s3Policy: String!
  s3Signature: String!
}

"""admission config options for the uniqueness definition"""
enum ConfigUniqueness {
  INFINITY
  UNIQUE
  UNIQUE_PER_CONFIG
}

type ConnectedAccount {
  accountCountry: String
  accountCurrency: EventCostCurrency
  accountNumber: String
  accountRouting: String
  address: String
  businessName: String
  businessTaxId: String
  city: String
  dob: String
  firstName: String
  id: String
  lastName: String
  legalEntityType: LegalEntityType
  personalAddressAddress: String
  personalAddressCity: String
  personalAddressPostalCode: String
  personalState: String
  postalCode: String
  ssnLast4: String
  state: String
}

input ContactAccountUser {
  accountId: ID!
  permissionProfileId: ID!
}

input ContactsInput {
  additionalAccountsAccess: [ContactAccountUser]
  detailedRemittanceRecipient: Boolean
  doorlistRecipient: Boolean
  email: String
  firstName: String
  hasQflowAccount: Boolean
  id: ID
  lastName: String
  loginEnabled: Boolean
  mioRedesignV2: Boolean
  name: String
  pamUser: Boolean
  permissionProfileId: ID
  phone: String
  position: String
  preferredLanguage: Language
  promoterIds: [ID]
  remittanceRecipient: Boolean
  venueIds: [ID]
}

interface Contracts {
  contracts: [FeesConfiguration]
}

input CoolingOffPeriodInput {
  active: Boolean
  hours: Int
}

input CopySeatingChartInput {
  clientMutationId: String!
  name: String
  seatingChartId: ID!
}

type CopySeatingChartPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: SeatingChart

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

enum CountryCode {
  AD
  AE
  AF
  AG
  AI
  AL
  AM
  AN
  AO
  AQ
  AR
  AS
  AT
  AU
  AW
  AX
  AZ
  BA
  BB
  BD
  BE
  BF
  BG
  BH
  BI
  BJ
  BL
  BM
  BN
  BO
  BQ
  BR
  BS
  BT
  BV
  BW
  BY
  BZ
  CA
  CC
  CD
  CF
  CG
  CH
  CI
  CK
  CL
  CM
  CN
  CO
  CR
  CU
  CV
  CW
  CX
  CY
  CZ
  DE
  DJ
  DK
  DM
  DO
  DZ
  EC
  EE
  EG
  EH
  ER
  ES
  ET
  FI
  FJ
  FK
  FM
  FO
  FR
  GA
  GB
  GD
  GE
  GF
  GG
  GH
  GI
  GL
  GM
  GN
  GP
  GQ
  GR
  GS
  GT
  GU
  GW
  GY
  HK
  HM
  HN
  HR
  HT
  HU
  ID
  IE
  IL
  IM
  IN
  IO
  IQ
  IR
  IS
  IT
  JE
  JM
  JO
  JP
  KE
  KG
  KH
  KI
  KM
  KN
  KP
  KR
  KW
  KY
  KZ
  LA
  LB
  LC
  LI
  LK
  LR
  LS
  LT
  LU
  LV
  LY
  MA
  MC
  MD
  ME
  MF
  MG
  MH
  MK
  ML
  MM
  MN
  MO
  MP
  MQ
  MR
  MS
  MT
  MU
  MV
  MW
  MX
  MY
  MZ
  NA
  NC
  NE
  NF
  NG
  NI
  NL
  NO
  NP
  NR
  NU
  NZ
  OM
  PA
  PE
  PF
  PG
  PH
  PK
  PL
  PM
  PN
  PR
  PS
  PT
  PW
  PY
  QA
  RE
  RO
  RS
  RU
  RW
  SA
  SB
  SC
  SD
  SE
  SG
  SH
  SI
  SJ
  SK
  SL
  SM
  SN
  SO
  SR
  SS
  ST
  SV
  SX
  SY
  SZ
  TC
  TD
  TF
  TG
  TH
  TJ
  TK
  TL
  TM
  TN
  TO
  TR
  TT
  TV
  TW
  TZ
  UA
  UG
  UM
  US
  UY
  UZ
  VA
  VC
  VE
  VG
  VI
  VN
  VU
  WF
  WS
  YE
  YT
  ZA
  ZM
  ZW
}

input CreateAdmissionConfigInput {
  clientMutationId: String!
  description: String
  enabled: Boolean
  fromShift: Int!
  name: String!
  note: String
  promoterId: ID!
  recurringLabels: [String]
  ticketTypeIds: [ID]!
  toShift: Int!
  uniqueness: ConfigUniqueness!
}

type CreateAdmissionConfigPayload {
  admissionConfig: AdmissionConfig
  clientMutationId: String!
}

input CreateArtistInput {
  backendArtistIds: [String]
  clientMutationId: String!
  description: String
  disambiguation: String
  hierarchicalTagIds: [ID]
  links: [LinkInput]
  media: [MediaItemInputObject]
  musicbrainzId: String
  name: String
  performerType: PerformerType
  profileImageAttachmentId: ID
  profileImageCropRegion: CropRegionInput
  tagIds: [ID]
}

type CreateArtistPayload {
  artist: Artist
  clientMutationId: String!
}

input CreateAttachmentInput {
  clientMutationId: String!
  format: String!
}

type CreateAttachmentPayload {
  attachment: Attachment
  clientMutationId: String!
  presignedUploadUrl: String!
}

input CreateBundleInput {
  cityId: String
  clientMutationId: String!
  curatedByPromoter: CuratedByPromoterInput
  curatedByVenue: CuratedByVenueInput
  eventIds: [ID]
  expiryDate: Date
  fromDate: Date
  hidden: Boolean!
  maxPrice: Int
  minPrice: Int
  name: String!
  profileDetails: ProfileDetailsInput
  promotersBundles: [PromotersBundles]
  toDate: Date
}

type CreateBundlePayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Bundle

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateCommentActivityInput {
  clientMutationId: String!
  content: String!
  itemId: ID!
}

type CreateCommentActivityPayload {
  activity: Activity
  clientMutationId: String!
}

input CreateDraftEventInput {
  """The name of the object type currently being queried."""
  
  additionalArtists: [AdditionalArtistInput]
  additionalInfos: [EventAdditionalInfoInput]
  addressCapacity: Int
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressSiaeCode: String
  addressState: String
  ageLimit: String
  announceDate: Time
  artistIds: [ID]
  artists: [EventArtistInput]
  attractiveFields: AttractiveFieldsInput
  barcodeType: String
  basePriceFees: [String]
  billingNotes: String
  billingPromoterId: ID
  bundleIds: [String]
  characteristicIds: [ID]
  charityEvent: Boolean
  charityId: String
  checklists: [ChecklistInput]
  cityIds: [String]
  clientMutationId: String!
  closeEventDate: Time
  colour: ColourInput
  completedSteps: Int
  costAmount: Int
  costCurrency: String
  countryCode: String
  date: Time
  description: String
  diceStatusNotes: String
  diceStreamDuration: Int
  diceStreamDvrEnabled: Boolean
  diceStreamRewatchEnabledUntil: Time
  diceTv: Boolean
  diceTvPlatform: TvPlatform
  disableUsTax: Boolean
  doorlistAdditionalRecipients: [String]
  doorlistRecipientIds: [ID]
  endDate: Time
  eventIdLive: String
  eventImages: [EventImageInput]
  eventLoadPredictions: [EventLoadPredictionInput]
  eventPromoters: [EventPromoter]
  eventRules: EventRulesInput
  eventSeatingChartId: ID
  eventSharingObjects: [EventSharingObjectInput]
  eventType: EventType
  eventVenues: [EventVenues]
  extraNotes: String
  fanFacingPromoterIds: [ID]
  fanSupportNotes: FanSupportNotesInput
  faqs: [FaqInput]
  featuredAreas: [FeaturedAreaInput]
  fees: [FeeInput]
  feesBehaviour: FeesBehaviour
  flags: EventFlagsInput
  freeEvent: Boolean
  fullAddress: String
  hierarchicalTagIds: [ID]
  isTest: Boolean
  isTicketAvailableAtDoor: Boolean
  labelIds: [ID]
  latitude: Float
  licenseNumber: String
  lineup: [LineupInput]
  links: [LinkInput]
  lockVersion: Int
  longitude: Float
  manualValidationEnabled: Boolean
  marketeerIds: [ID]
  maxTicketsLimit: Int
  media: [MediaItemInputObject]
  musicbrainzArtists: [MusicbrainzArtists]
  name: String
  notes: String
  offSaleDate: Time
  onSaleDate: Time
  onSaleNotification: Boolean
  onSaleNotificationAt: Time
  onSaleNotificationSmsContent: String
  overriddenPromoterName: String
  overrideFees: Boolean
  permName: String
  platformAccountCode: PlatformAccountCode
  postFanPriceFees: [String]
  postOfficeBoxNumber: String
  postalCode: String
  presentedBy: String
  printedTicketFormat: PrintedTicketFormat
  products: [ProductInput]
  promoterIds: [ID]
  promoterStatusNotes: String
  pwlWindow: Int
  readAccessEmails: [String]
  recurrentEventSchedule: RecurrentEventsScheduleInput
  relatedEventIds: [ID]
  requiresBoxOfficeTicketNomination: Boolean
  requiresTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  salesforceContractId: ID
  scheduleStatus: ScheduleStatus
  seatingChannels: [SeatingChannelInput]
  sendReceiptViaSms: Boolean
  showArtistDescription: ShowArtistDescription
  socialDistancingRulesetKey: String
  stages: [String]
  streetAddress: String
  stripeAccountId: String
  tagIds: [ID]
  taxSettings: TaxSettingsInput
  thirdPartySettingsId: ID
  ticketPools: [AttachTicketPoolInput]
  ticketType: String
  ticketTypes: [TicketTypesInput]
  timezoneName: String
  totalTickets: Int
  venue: String
  venueConfigurationId: ID
  venueIds: [ID]
  venueName: String
  venueSchedules: [VenueScheduleInput]
  venueSpaceId: ID
  waitingListExchangeWindows: [WaitingListExchangeWindowInput]
}

type CreateDraftEventPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Event

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateEventChangedNotificationInput {
  changes: [String]
  clientMutationId: String!
  eventId: ID!
  message: String
  sendMeACopy: Boolean
}

type CreateEventChangedNotificationPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateEventInput {
  """The name of the object type currently being queried."""
  
  additionalArtists: [AdditionalArtistInput]
  additionalInfos: [EventAdditionalInfoInput]
  addressCapacity: Int
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressSiaeCode: String
  addressState: String
  ageLimit: String
  announceDate: Time
  artistIds: [ID]
  artists: [EventArtistInput]
  attractiveFields: AttractiveFieldsInput
  barcodeType: String
  basePriceFees: [String]
  billingNotes: String
  billingPromoterId: ID
  bundleIds: [String]
  characteristicIds: [ID]
  charityEvent: Boolean
  charityId: String
  checklists: [ChecklistInput]
  cityIds: [String]
  clientMutationId: String!
  closeEventDate: Time
  colour: ColourInput
  completedSteps: Int
  costAmount: Int
  costCurrency: String
  countryCode: String
  date: Time
  description: String
  diceStatusNotes: String
  diceStreamDuration: Int
  diceStreamDvrEnabled: Boolean
  diceStreamRewatchEnabledUntil: Time
  diceTv: Boolean
  diceTvPlatform: TvPlatform
  disableUsTax: Boolean
  doorlistAdditionalRecipients: [String]
  doorlistRecipientIds: [ID]
  endDate: Time
  eventIdLive: String
  eventImages: [EventImageInput]
  eventLoadPredictions: [EventLoadPredictionInput]
  eventPromoters: [EventPromoter]
  eventRules: EventRulesInput
  eventSeatingChartId: ID
  eventSharingObjects: [EventSharingObjectInput]
  eventType: EventType
  eventVenues: [EventVenues]
  extraNotes: String
  fanFacingPromoterIds: [ID]
  fanSupportNotes: FanSupportNotesInput
  faqs: [FaqInput]
  featuredAreas: [FeaturedAreaInput]
  fees: [FeeInput]
  feesBehaviour: FeesBehaviour
  flags: EventFlagsInput
  freeEvent: Boolean
  fullAddress: String
  hierarchicalTagIds: [ID]
  isTest: Boolean
  isTicketAvailableAtDoor: Boolean
  labelIds: [ID]
  latitude: Float
  licenseNumber: String
  lineup: [LineupInput]
  links: [LinkInput]
  lockVersion: Int
  longitude: Float
  manualValidationEnabled: Boolean
  marketeerIds: [ID]
  maxTicketsLimit: Int
  media: [MediaItemInputObject]
  musicbrainzArtists: [MusicbrainzArtists]
  name: String
  notes: String
  offSaleDate: Time
  onSaleDate: Time
  onSaleNotification: Boolean
  onSaleNotificationAt: Time
  onSaleNotificationSmsContent: String
  overriddenPromoterName: String
  overrideFees: Boolean
  permName: String
  platformAccountCode: PlatformAccountCode
  postFanPriceFees: [String]
  postOfficeBoxNumber: String
  postalCode: String
  presentedBy: String
  printedTicketFormat: PrintedTicketFormat
  products: [ProductInput]
  promoterIds: [ID]
  promoterStatusNotes: String
  pwlWindow: Int
  readAccessEmails: [String]
  recurrentEventSchedule: RecurrentEventsScheduleInput
  relatedEventIds: [ID]
  requiresBoxOfficeTicketNomination: Boolean
  requiresTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  salesforceContractId: ID
  scheduleStatus: ScheduleStatus
  seatingChannels: [SeatingChannelInput]
  sendReceiptViaSms: Boolean
  showArtistDescription: ShowArtistDescription
  socialDistancingRulesetKey: String
  stages: [String]
  streetAddress: String
  stripeAccountId: String
  tagIds: [ID]
  taxSettings: TaxSettingsInput
  thirdPartySettingsId: ID
  ticketPools: [AttachTicketPoolInput]
  ticketType: String
  ticketTypes: [TicketTypesInput]
  timezoneName: String
  totalTickets: Int
  venue: String
  venueConfigurationId: ID
  venueIds: [ID]
  venueName: String
  venueSchedules: [VenueScheduleInput]
  venueSpaceId: ID
  waitingListExchangeWindows: [WaitingListExchangeWindowInput]
}

type CreateEventPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Event

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateEventPromotionCodesInput {
  amount: Int!
  clientMutationId: String!
  id: ID!
}

type CreateEventPromotionCodesPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventPromotion

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateEventPromotionInput {
  accessType: PromotionAccessType!
  clientMutationId: String!
  code: String
  codesOperations: [CodesOperationsInput]
  discount: Int
  endDate: Time
  eventId: ID!
  fanFacingName: String
  maxRedemptions: Int
  name: String!
  promotionType: PromotionType!
  seatsIoChannel: String
  startDate: Time
  ticketTypeIds: [ID]
  unlockAfterExpired: Boolean
}

type CreateEventPromotionPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventPromotion

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateEventSeatingChannelInput {
  clientMutationId: String!
  color: String!
  eventId: ID!
  eventPromotionId: ID
  name: String!
  objects: [String]
}

type CreateEventSeatingChannelPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: SeatsIoChannel

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateEventSeatingChartInput {
  clientMutationId: String!
  socialDistancingRulesetKey: String
  venueChartId: ID!
}

type CreateEventSeatingChartPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventSeatingChart

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateFacebookEventInput {
  clientMutationId: String!
  eventId: ID!
  pageId: String!
  userAccessToken: String!
}

type CreateFacebookEventPayload {
  clientMutationId: String!
  facebookEventId: ID
}

input CreateFanConnectInput {
  clientMutationId: String!
  eventId: ID!
  message: String!
  onBehalfOfUserId: ID
  scheduledAt: Time
  sendMeACopy: Boolean
  ticketTypeIds: [ID]!
  title: String!
}

type CreateFanConnectPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateFeeConfigurationInput {
  allowedAdhocFeeTypes: [FeeInput]
  basePriceFees: [String] = []
  basePriceMode: BasePriceMode
  boxOfficeFee: Int
  clientMutationId: String!
  effectiveDate: Time!
  endDate: Time
  feesApplicationRules: [FeesApplicationRuleInput]
  itemId: ID!
  postFanPriceFees: [String] = []
  taxExempt: Boolean!
  taxId: String
}

type CreateFeeConfigurationPayload {
  clientMutationId: String!
  feesConfiguration: FeesConfiguration
}

input CreateHierarchicalTagInput {
  clientMutationId: String!
  deprecated: Boolean
  name: String!
  parentId: ID!
}

type CreateHierarchicalTagPayload {
  clientMutationId: String!
  hierarchicalTag: HierarchicalTag
}

input CreateIntegrationTokenInput {
  clientMutationId: String!
}

type CreateIntegrationTokenPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateLabelInput {
  checklists: [ChecklistInput]
  clientMutationId: String!
  description: String
  name: String!
}

type CreateLabelPayload {
  clientMutationId: String!
  label: Label
}

input CreateLinkoutInput {
  artistId: ID
  cityIds: [String]
  clientMutationId: String!
  currency: String
  date: Time!
  destinationEventId: ID
  endDate: Time!
  externalUrl: String
  imageAttachmentId: ID
  imageCropRegion: CropRegionInput
  linkoutType: LinkoutType!
  name: String!
  offSaleDate: Time
  onSaleDate: Time
  price: Int
  promoterId: ID
  tagIds: [ID]
  timezone: String!
  venueId: ID
}

type CreateLinkoutPayload {
  clientMutationId: String!
  linkout: Linkout
}

input CreateManualPayoutInput {
  amount: Int!
  clientMutationId: String!
  date: Time!
  details: String
  eventId: ID!
  isAdvance: Boolean!
  means: PayoutMeans!
  reason: PayoutReason!
}

type CreateManualPayoutPayload {
  clientMutationId: String!
  event: Event
}

input CreateMarketeerInput {
  appOptInEnabled: Boolean
  clientMutationId: String!
  contacts: [ContactsInput]
  fbAccessToken: String
  fbPixelId: String
  gaTrackingId: String
  googleAdsConversionId: String
  googleAdsPurchaseConversionLabel: String
  name: String!
  privacyPolicyLink: String
  tiktokPixelId: String
  twitterCheckoutInitiatedPixelId: String
  twitterPixelId: String
  twitterPurchasePixelId: String
  webOptInEnabled: Boolean
}

type CreateMarketeerPayload {
  clientMutationId: String!
  marketeer: Marketeer
}

input CreateNotificationBatchInput {
  clientMutationId: String!
  eventId: ID!
  message: String!
  notifyOn: Time!
  reason: NotificationReason!
}

type CreateNotificationBatchPayload {
  clientMutationId: String!
  notificationBatch: NotificationBatch
}

input CreateOrUpdateVenueAreaInput {
  code: String
  id: ID
  name: String
}

input CreateOrUpdateVenueConfigurationInput {
  attractiveRoomSiaeCode: String
  capacity: Int
  id: ID
  name: String
  seatingAreaConfigs: [SeatingAreaConfigInput]
}

input CreatePayoutInput {
  amount: Int!
  clientMutationId: String!
  currency: String!
  eventId: ID!
  sendRemittanceEmail: Boolean!
}

type CreatePayoutPayload {
  clientMutationId: String!
  event: Event
}

input CreatePermissionProfileInput {
  accountId: ID
  caption: String
  clientMutationId: String!
  subjects: [SubjectInput]
}

type CreatePermissionProfilePayload {
  clientMutationId: String!
  permissionProfile: PermissionProfile
}

input CreateProductInput {
  allTicketTypes: Boolean
  allocation: Int
  archived: Boolean
  categoryId: ID
  clientMutationId: String!
  customCover: Boolean
  date: Time
  description: String
  endDate: Time
  excludedFromCrmAutomation: Boolean
  faceValue: Int
  fees: [FeeInput]
  fulfilledBy: String
  hasSeparateAccessBarcodes: Boolean
  hasVariants: Boolean
  locationNote: String
  name: String
  offSaleDate: Time
  onSaleDate: Time
  optionType: ProductOptionType
  productImages: [ProductImageInput]
  productType: ProductType
  purchaseConfirmationMessage: String
  sellingPoints: [SellingPointInput]
  sku: String
  variants: [VariantInput]
  venueId: ID
}

type CreateProductPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Product

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateProductsListInput {
  clientMutationId: String!
  products: [NewProductInput]
}

type CreateProductsListPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ProductsList

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreatePromoterInput {
  accountIban: String
  accountManagerId: ID
  accountManagerIds: [ID]
  accountName: String
  accountNumber: String
  accountSortCode: String
  accountType: String
  accountVatNumber: String
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressState: String
  allowSkipReview: Boolean
  apiToken: String
  apiTokenExpiryDate: Time
  associatedMarketeerIds: [ID]
  autoRescheduledEventRefunds: AutoRescheduledEventRefundsInput
  automaticRollingPaymentsConfiguration: AutomaticRollingPaymentsConfigurationInput
  bankAddress: String
  bankName: String
  billingNotes: String
  charityTaxFree: Boolean
  clientMutationId: String!
  clientSuccessManagerIds: [ID]
  contacts: [ContactsInput]
  coolingOffPeriod: Boolean
  coolingOffPeriodHours: Int
  countryCode: String
  curatedBundle: CuratedBundleInput
  dicePartner: Boolean
  disableUsTax: Boolean
  disabledReason: EnumAccountDisabledReason
  displayName: String
  eventDefaults: EventDefaultsInput
  extrasEnabled: Boolean
  fanSupportNotes: FanSupportNotesInput
  fees: [FeeInput]
  forbidSelfPayouts: Boolean
  fullAddress: String
  holdPayouts: Boolean
  isDisabled: Boolean
  isTest: Boolean
  labelIds: [ID]
  latitude: Float
  legalEntity: String
  licenseNumber: String
  links: [LinkInput]
  longitude: Float
  merchEnabled: Boolean
  name: String
  notes: String
  permissionProfileOverrides: [PermissionProfileOverrideInput]
  postOfficeBoxNumber: String
  postalCode: String
  profileActive: Boolean
  profileDetails: ProfileDetailsInput
  promoterTaxSettings: PromoterTaxSettingsInput
  qflowEnabled: Boolean
  resoldEnabled: Boolean
  routingNumber: String
  salesforcePromoterFields: SalesforcePromoterFieldsInput
  sendReceiptViaSms: Boolean
  showPriceBreakdown: Boolean
  statusNotes: String
  streetAddress: String
  stripeAccountId: String
  stripeDocumentId: String
  stripeFallbackAccountId: String
  stripeFallbackPlatformCode: PlatformAccountCode
  stripeLocationId: String
  stripeVerified: Boolean
  swiftCode: String
  tagIds: [ID]
  taxCode: String
  ticketAgreementComplete: Boolean
  timezoneName: String
  typeOfOrganizer: EnumTypeOfOrganizer
}

type CreatePromoterPayload {
  clientMutationId: String!
  promoter: Promoter
}

input CreateReportScheduleInput {
  clientMutationId: String!
  emailList: [EmailAddress]!
  endAt: Time
  eventId: ID
  locale: String
  name: String!
  options: ScheduledReportOptionsInput
  recurrence: RecurrenceInput
  reportType: ReportType!
  startAt: Time!

  """
  Time zone name for localized start_at time.  Defaults to user's saved timezone, or UTC, if not provided.
  """
  timezoneName: String
}

type CreateReportSchedulePayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ReportSchedule

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateRestrictedIntegrationTokenInput {
  clientMutationId: String!
  eventId: ID
  fullAccess: Boolean
  venueId: ID
}

type CreateRestrictedIntegrationTokenPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateScheduledReportInput {
  clientMutationId: String!
  emailList: [EmailAddress]!
  eventId: ID
  locale: String
  name: String!
  options: ScheduledReportOptionsInput
  reportType: ReportType!
  scheduledAt: Time!
}

type CreateScheduledReportPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ScheduledReport

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateSeatingChartInput {
  clientMutationId: String!
  name: String!
  venueId: ID!
  venueType: SeatsIoChartType
}

type CreateSeatingChartPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: SeatingChart

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateSocialLinkInput {
  campaign: String
  channel: String
  clientMutationId: String!
  customUrl: String
  diceLink: Boolean
  eventId: ID!
  postType: SocialLinkPostType
  sourceId: ID
  ticketTypeIds: [ID]
}

type CreateSocialLinkPayload {
  clientMutationId: String!
  event: Event
}

input CreateTagInput {
  clientMutationId: String!
  deprecated: Boolean
  name: String!
}

type CreateTagPayload {
  clientMutationId: String!
  tag: Tag
}

input CreateTempAttachmentInput {
  clientMutationId: String!
  format: String!
}

type CreateTempAttachmentPayload {
  attachment: TempAttachment
  clientMutationId: String!
  presignedUploadUrl: String!
}

input CreateThirdPartySettingsInput {
  appIcon: String!
  appLink: String!
  appName: String!
  clientMutationId: String!
  idVerification: Boolean!
  promoterDisplayName: String
  promoterId: ID
  provideSecureUserAuth: Boolean!
}

type CreateThirdPartySettingsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ThirdPartySettings

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateTicketPoolInput {
  archived: Boolean
  clientMutationId: String!
  holds: [HoldInput]
  maxAllocation: Int
  name: String
  ticketTypes: [TicketTypesInput]
  unlimitedAllocation: Boolean
}

type CreateTicketPoolPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: TicketPool

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input CreateUserInput {
  clientMutationId: String!
  email: String
  firstName: String
  lastName: String
  mioRedesignV2: Boolean
  mongoUserId: String
  name: String
  notificationPermissions: NotificationPermissionsInput
  permissionProfileId: ID
  phone: String
  position: String
  preferredLanguage: Language
}

type CreateUserPayload {
  clientMutationId: String!
  user: User
}

input CreateVenueInput {
  accessControl: String
  accessibilityLink: String
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressState: String
  ageLimit: String
  allocatedSeating: String
  areas: [CreateOrUpdateVenueAreaInput]
  associatedPromoterIds: [ID]
  attractiveEnabled: Boolean
  attractiveRoomSiaeCode: String
  attributes: [String]
  barcodeType: String
  capacity: Int
  characteristicIds: [ID]
  cityId: String
  clientMutationId: String!
  configurations: [CreateOrUpdateVenueConfigurationInput]
  contactEmail: String
  contactPhone: String
  contacts: [ContactsInput]
  countryCode: String
  curatedBundle: CuratedBundleInput
  dicePartner: Boolean
  externalLinks: ExternalLinksInput
  facebookPageId: String
  faqs: [FaqInput]
  fees: [FeeInput]
  flags: VenueFlagsInput
  fullAddress: String
  hideCapacity: Boolean
  isSecret: Boolean
  isTest: Boolean
  labelIds: [ID]
  latitude: Float
  links: [LinkInput]
  logoAttachmentId: ID
  logoCropRegion: CropRegionInput
  longitude: Float
  mobileTicketsEnabled: Boolean
  multizone: Boolean
  name: String
  notes: String
  postOfficeBoxNumber: String
  postalCode: String
  profileActive: Boolean
  profileDetails: ProfileDetailsInput
  promoterAllocation: Int
  qflowEnabled: Boolean
  scannerModel: String
  scannerType: String
  streetAddress: String
  ticketType: String
  ticketValidation: String
  ticketingPartner: String
  tier: VenueTier
  timezoneName: String
  type: VenueType
  venueAllocation: Int
  venueImages: [VenueImageInput]
  venueOwnerIds: [ID]
  venueSpaces: [VenueSpaceInput]
}

type CreateVenuePayload {
  clientMutationId: String!
  venue: Venue
}

type CreditBalance {
  balance: Int
  currency: String
}

type CropRegion {
  height: Int
  width: Int
  x: Int
  y: Int
}

input CropRegionInput {
  height: Int
  width: Int
  x: Int
  y: Int
}

type CumulativeExtrasPurchaseItem {
  purchasedAt: Time!
  totalPurchases: Int!
}

type CumulativeProductsPurchaseItem {
  purchasedAt: Time!
  totalPurchases: Int!
}

scalar CumulativePurchasesAsJson

input CuratedBundleInput {
  bundleId: ID
}

input CuratedByPromoterInput {
  promoterId: ID
}

input CuratedByVenueInput {
  venueId: ID
}

type CurrencyBalance {
  availableBalance: Int!
  availableForPayout: Int!
  currency: EventCostCurrency!
  pendingBalance: Int!
}

"""ISO 8601 Date - YYYY-MM-DD"""
scalar Date

input DateRangeInput {
  endDate: Time
  startDate: Time
}

type DayOfMonth {
  dayOfWeek: Int!
  nthOfMonth: Int!
}

input DayOfMonthInput {
  dayOfWeek: Int!
  nthOfMonth: Int!
}

input DeadlineInput {
  active: Boolean!
  deadline: Time
}

input DeleteAdmissionConfigInput {
  clientMutationId: String!
  id: ID!
}

type DeleteAdmissionConfigPayload {
  admissionConfig: AdmissionConfig
  clientMutationId: String!
}

input DeleteEventPromotionCodesInput {
  allCodes: Boolean
  clientMutationId: String!
  codes: [String]
  filter: CodeLockFilter
  force: Boolean
  id: ID!
}

type DeleteEventPromotionCodesPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventPromotion

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input DeleteEventSeatingChannelInput {
  clientMutationId: String!
  key: String!
}

type DeleteEventSeatingChannelPayload {
  clientMutationId: String!
  successful: Boolean!
}

input DeleteFacebookEventInput {
  clientMutationId: String!
  eventId: ID!
}

type DeleteFacebookEventPayload {
  clientMutationId: String!
  success: Boolean
}

input DeleteIntegrationTokenInput {
  clientMutationId: String!
  token: String!
}

type DeleteIntegrationTokenPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input DeleteMailchimpSettingsInput {
  clientMutationId: String!
}

type DeleteMailchimpSettingsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input DeleteManualPayoutInput {
  clientMutationId: String!
  payoutId: ID!
}

type DeleteManualPayoutPayload {
  clientMutationId: String!
  event: Event
}

input DeleteNodeInput {
  clientMutationId: String!
  id: ID!
}

type DeleteNodePayload {
  clientMutationId: String!
  id: ID!
}

input DeleteReportScheduleInput {
  clientMutationId: String!
  id: ID!
}

type DeleteReportSchedulePayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ReportSchedule

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input DeleteScheduledReportInput {
  clientMutationId: String!
  id: ID!
}

type DeleteScheduledReportPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ScheduledReport

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input DeleteTicketPoolInput {
  clientMutationId: String!
  id: ID!
}

type DeleteTicketPoolPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: TicketPool

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

type DestinationAccount implements Node {
  accountId: String
  balances: [CurrencyBalance]!

  """The ID of an object"""
  id: ID!
  region: String
  stripeOauthUrl: String
}

enum DiceRefundReason {
  ACCESSIBILITY_REQUEST
  ACCIDENTAL_PURCHASE
  APP_TECH_ISSUE
  DISPUTED_TRANSACTION
  EVENT_BUILD_ERROR_DICE
  EVENT_BUILD_ERROR_PARTNER
  EVENT_COMPLAINT
  EVENT_POSTPONED_RESCHEDULED
  FRAUD_PREVENTION
  OTHER
  PROMOTER_REQUEST
  SUPPORT_DISCRETION
  SUSPECTED_RESELLER
  SWAP_REQUEST
  VENUE_COMPLAINT
}

input DisableFanSurveyInput {
  clientMutationId: String!
  eventId: ID!
}

type DisableFanSurveyPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Event

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input DisableSeatingChartInput {
  clientMutationId: String!
  seatingChartId: ID!
}

type DisableSeatingChartPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: SeatingChart

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

enum DistanceUnits {
  kilometers
  miles
}

type DoorSalesActivity implements Node {
  allowedActions: DoorSalesAllowedActions
  author: User
  charge: DoorSalesCharge!

  """The ID of an object"""
  id: ID!
  insertedAt: Time!
  items: [DoorSalesActivityItem]!
  warning: DoorSalesWarning
}

type DoorSalesActivityConnection {
  edges: [DoorSalesActivityEdge]
  pageInfo: PageInfo!
}

type DoorSalesActivityEdge {
  cursor: String
  node: DoorSalesActivity
}

type DoorSalesActivityItem {
  code: String
  price: Int
  salesTax: Int
  seatName: String
  state: DoorSalesTicketState
  ticketId: ID!
  updatedAt: Time!
}

"""Actions the user can make to the object"""
type DoorSalesAllowedActions {
  """Edit the activity (adjust payment type)"""
  edit: Boolean
}

type DoorSalesCharge implements Node {
  cardLastDigits: String!
  event: Event!

  """The ID of an object"""
  id: ID!
  paymentType: DoorSalesPaymentType!
  printableReceipt: String
  ticketType: TicketType!
}

input DoorSalesChargeInput {
  """For box office sales with p400 terminal"""
  chargeId: String
  clientMutationId: String!

  """For box office sales with p400 terminal"""
  paymentIntentId: String
  paymentType: DoorSalesPaymentType!

  """FAN # to assign tickets to"""
  phoneNumber: String
  ticketHolderNames: [HolderName]
  ticketIds: [ID]
}

type DoorSalesChargePayload {
  clientMutationId: String!
  event: Event!
  newActivityEdge: DoorSalesActivityEdge!
  printableTickets: [DoorSalesPrintableTicket]
}

input DoorSalesCreateReaderInput {
  clientMutationId: String!
  eventId: ID!
  label: String
  registrationCode: String!
}

type DoorSalesCreateReaderPayload {
  clientMutationId: String!
  reader: StripeReader
}

input DoorSalesEditInput {
  activityId: ID!
  clientMutationId: String!
  paymentType: DoorSalesPaymentType!
}

type DoorSalesEditPayload {
  activity: DoorSalesActivity!
  clientMutationId: String!
}

type DoorSalesExport {
  downloadUrl: String
  endDate: Time
  s3Path: String
  startDate: Time
}

type DoorSalesExportConnection {
  edges: [DoorSalesExportEdge]
  pageInfo: PageInfo!
}

type DoorSalesExportEdge {
  cursor: String
  node: DoorSalesExport
}

enum DoorSalesPaymentType {
  CARD
  CASH
  MIXED
  NO_PAYMENT
}

type DoorSalesPrintableTicket {
  activationCard: String @deprecated(reason: "To be removed, use legalDetails instead")
  charge: DoorSalesCharge!
  code: String
  emissionDate: String @deprecated(reason: "To be removed, use legalDetails instead")
  fiscalSeal: String @deprecated(reason: "To be removed, use legalDetails instead")
  holder: TicketHolder
  id: String!
  legalDetails: PrintableTicketLegalDetails
  price: Int!
  printableReceipt: String
  progressiveNumber: String @deprecated(reason: "To be removed, use legalDetails instead")
  seatLabels: SeatLabel
  seatName: String @deprecated(reason: "Use seatLabels instead")
}

input DoorSalesRefundInput {
  clientMutationId: String!
  ticketIds: [ID]
}

type DoorSalesRefundPayload {
  activity: DoorSalesActivity!
  clientMutationId: String!
}

input DoorSalesReprintInput {
  activityId: ID!
  clientMutationId: String!
}

type DoorSalesReprintPayload {
  clientMutationId: String!
  printableTickets: [DoorSalesPrintableTicket]
}

type DoorSalesReservation {
  expiresAt: Time!
  tickets: [DoorSalesReservedTicket]
}

input DoorSalesReserveInput {
  amount: Int!
  channelIds: [String]
  clientMutationId: String!
  ticketTypeId: ID!
}

type DoorSalesReservePayload {
  clientMutationId: String!
  reservation: DoorSalesReservation!
}

type DoorSalesReservedTicket {
  id: ID!
  price: Int!
}

type DoorSalesRevenue {
  byPaymentType: [DoorSalesRevenueItem]!
  total: Int!
}

type DoorSalesRevenueItem {
  paymentType: DoorSalesPaymentType!
  value: Int!
}

input DoorSalesSendReceiptInput {
  activityId: ID!
  clientMutationId: String!
  phoneNumber: PhoneNumber!
}

type DoorSalesSendReceiptPayload {
  clientMutationId: String!
  receiptUrl: String
}

type DoorSalesStats {
  posRevenue: Int!
  posSold: Int!
  soldOrReserved: Int!
}

enum DoorSalesTicketState {
  ACTIVE
  REFUNDED
}

input DoorSalesUnreserveInput {
  clientMutationId: String!
  ticketIds: [ID]
}

type DoorSalesUnreservePayload {
  clientMutationId: String!
  ok: Boolean
}

input DoorSalesValidateFanInput {
  amount: Int!
  clientMutationId: String!
  phoneNumber: String!
  ticketTypeId: ID!
}

type DoorSalesValidateFanPayload {
  clientMutationId: String!
  smsCode: String
}

type DoorSalesWarning {
  message: String
  ticketCodes: [String]
}

enum DoorlistDirection {
  ASC
  DESC
}

type DoorlistEntry {
  address: FanAddress
  chargeKinds: [String]
  checkId: String
  codes: [String]
  fan: Fan
  holders: [TicketHolder]
  itemType: DoorlistEntryItemType!
  legalDetails: [PrintableTicketLegalDetails]
  lineItemIds: [Int]
  numberOfTickets: Int!
  prices: [Int]
  product: Product
  purchaseId: Int!
  purchasedAt: Time!
  seatLabels: [SeatLabel]
  seatNames: [String] @deprecated(reason: "Use seatLabels instead")
  ticketIds: [Int]
  ticketType: TicketType!
  variant: Variant
}

type DoorlistEntryConnection {
  edges: [DoorlistEntryEdge]
  pageInfo: PageInfo!
}

type DoorlistEntryEdge {
  cursor: String
  node: DoorlistEntry
}

enum DoorlistEntryItemType {
  EXTRAS
  MERCH
  TICKETS
}

enum DoorlistType {
  ALL
  EXTRAS
  MERCH
  TICKETS
}

input DropHoldPayoutsInput {
  clientMutationId: String!
  id: ID!
}

type DropHoldPayoutsPayload {
  clientMutationId: String!
  event: Event
}

input DuplicateEventInput {
  clientMutationId: String!
  id: ID!
}

type DuplicateEventPayload {
  clientMutationId: String!
  event: Event
  sourceEvent: Event
}

type Email {
  email: String!
  status: EmailStatus!
  updatedAt: Time!
  uuid: String!
}

"""
Email Address : represent a addr-spec in `local-part@domain` format.
      It can parse mailbox format `display name <address>` but it only returns the address part.
"""
scalar EmailAddress

enum EmailStatus {
  FAILED
  READY
  SENT
  WAITING
}

input EnableFanSurveyInput {
  clientMutationId: String!
  eventId: ID!
}

type EnableFanSurveyPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Event

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input EnabledPwlInput {
  active: Boolean
  deadline: Time
  increaseAmount: Int
}

input EndEventPromotionInput {
  clientMutationId: String!
  id: ID!
}

type EndEventPromotionPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventPromotion

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

enum EnumAccountDisabledReason {
  INACTIVITY
  INACTIVITY_AD_LEAD
  TERMINATION
}

enum EnumAccountUserInvitaionStatus {
  ACCEPTED
  DECLINED
  PENDING
}

enum EnumFeeTarget {
  extras
  merch
  tickets
}

enum EnumTypeOfOrganizer {
  AGENT
  ARTIST
  ARTIST_MANAGEMENT
  BRAND
  LABEL
  PROMOTER
  VENUE
}

"""Gig event"""
type Event implements ChecklistsNode & Fees & Name & Node & TimeStamps & VenueAddress {
  activities(after: String, before: String, first: Int, last: Int, where: ActivityWhereInput): ActivitiesConnection @deprecated(reason: "To be removed, please use viewer.activities instead")
  additionalArtists: [AdditionalArtist]
  additionalInfos: [EventAdditionalInfo]
  addressCapacity: Int
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressSiaeCode: String
  addressState: String
  adjustmentsStats: AdjustmentsStats
  admissionConfigs: [AdmissionConfig]
  ageLimit: String
  allocation: Int!
  allowedActions: AllowedActions
  allowedLifecycleUpdates: AllowedLifecycleUpdates
  announceDate: Time
  artists: [Artist] @deprecated(reason: "Legacy")
  attractiveErrors: [String]
  attractiveFields: AttractiveFields
  attractiveStatus: IntegrationStatus
  automatedUserComms: Boolean
  autopaymentFailed: Boolean
  balance: EventBalance
  barcodeType: String
  basePriceFees: [String]
  billingNotes: String
  billingPromoter: Promoter
  boxOfficeLocationId: String
  boxOfficeTerminalSupported: Boolean
  boxOfficeTerminalToken: String
  bundles: [Bundle]
  cancelledAt: Time
  characteristics: [Characteristic]
  charityEvent: Boolean
  charityId: String
  checklists: [Checklist]!
  cities: [City]
  closeEventDate: Time
  codeLocks(after: String, before: String, code: String, first: Int, last: Int, status: CodeLockStatus, ticketTypesIds: [ID], validFrom: Time, validTo: Time): CodeLockConnection
  colour: Map
  completedSteps: Int
  contacts: Map
  costAmount: Int
  costCurrency: EventCostCurrency
  countryCode: String
  createdAt: Time
  createdBy: ThinUserProfile
  cumulativeExtrasPurchases: [CumulativeExtrasPurchaseItem] @deprecated(reason: "Please use cumulative_products_purchases instead")
  cumulativeProductsPurchases(rootType: ProductRootType): [CumulativeProductsPurchaseItem]
  cumulativePurchasesJson: CumulativePurchasesAsJson
  date: Time
  deeplinkUrl: String
  defaultEventTimings: [EventTiming]
  description: String
  diceStatusNotes: String
  diceStreamDuration: Int
  diceStreamDvrEnabled: Boolean
  diceStreamRewatchEnabledUntil: Time
  diceTv: Boolean @deprecated(reason: "unuseds")
  diceTvPlatform: TvPlatform
  disableUsTax: Boolean
  doorSalesActivities(after: String, before: String, first: Int, last: Int, onlyMy: Boolean, search: String): DoorSalesActivityConnection
  doorSalesRevenue: DoorSalesRevenue
  doorlist(after: String, before: String, doorlistDirection: DoorlistDirection = DESC, doorlistType: DoorlistType = ALL, first: Int, last: Int, searchTerm: String): DoorlistEntryConnection
  doorlistAdditionalRecipients: [String]
  doorlistJson: Map
  doorlistRecipients: [User]
  doorlistSendAt: Time
  doorlistSendStatus: String
  endDate: Time
  eventArtists: [EventArtist]
  eventCompareLive: EventCompare
  eventComparePreview: EventCompare
  eventIdLive: String
  eventIdPreview: String
  eventImages: [EventImage]
  eventLive: BackendEvent
  eventLoadPredictions: [EventLoadPrediction]
  eventPreview: BackendEvent
  eventPromotions: [EventPromotion]
  eventReview: EventReview
  eventRules: EventRules
  eventSeatingChart: EventSeatingChart
  eventSerialised: Map
  eventSharingObjects: [EventSharingObject]
  eventType: EventType
  extraNotes: String
  facebookEventId: String
  fanFacingPromoters: [Promoter]
  fanQuestions: [FanQuestion]
  fanSupportNotes: FanSupportNotes
  fansStats: FansStats
  faqs: [Faq]
  featuredAreas: [FeaturedArea]
  fees: [Fee]
  feesBehaviour: FeesBehaviour
  flags: EventFlags
  freeEvent: Boolean
  fullAddress: String
  guestList: GuestList
  hierarchicalTags: [HierarchicalTag]
  holdPayouts: Boolean

  """The ID of an object"""
  id: ID!
  isAttractive: Boolean!
  isTest: Boolean
  isTicketAvailableAtDoor: Boolean
  labels: [Label]
  latitude: Float
  licenseNumber: String
  lineup: [Map]
  linkStats: [LinkStats]
  links: [Link]!
  locationStats: LocationStats!
  lockVersion: Int!
  longitude: Float
  manualValidationEnabled: Boolean
  marketeers: [Marketeer]
  marketingLinks: [SocialLink]
  maxTicketsLimit: Int
  media: [MediaItem]
  name: String
  notes: String
  notificationBatches: [NotificationBatch]
  notifyCancelOn: Time
  offSaleDate: Time
  offSaleSentAt: Time
  offSaleSentStatus: String
  onSaleDate: Time
  onSaleNotification: Boolean
  onSaleNotificationAt: Time
  onSaleNotificationSmsContent: String @deprecated(reason: "unused")
  onSaleNotificationStatus: Boolean
  onSaleSmsReminders: Int!
  organicSocialLink: String
  overriddenPromoterName: String
  overrideFees: Boolean @deprecated(reason: "Legacy")
  payout: EventPayout
  permName: String
  platformAccountCode: PlatformAccountCode
  postFanPriceFees: [String]
  postOfficeBoxNumber: String
  postalCode: String
  presentedBy: String
  previewToken: String
  price: Price @deprecated(reason: "Not used")
  prices: PriceRange
  primaryVenue: Venue
  printedTicketFormat: PrintedTicketFormat
  products: [Product]
  productsSales: ProductsSales
  promoterStatusNotes: String
  promoters: [Promoter]
  pwlWindow: Int
  qflowId: String
  qflowLastIntegratedAt: Time
  readAccessEmails: [String]
  recurrentEventSchedule: RecurrentEventsScheduleConfig
  recurrentEventsGroup: [Event]
  relatedEvents: [Event]
  repsRequired: String
  requiresBoxOfficeTicketNomination: Boolean
  requiresTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  sales: Sales
  salesforceContract: SalesforceContract
  savedCount: Int!
  scheduleStatus: ScheduleStatus
  scheduleStatusUpdatedAt: Time
  seatingChannels: [SeatsIoChannelWithObjects]
  selfPayoutBlockers: Map
  sendReceiptViaSms: Boolean
  showArtistDescription: ShowArtistDescription
  socialLinks: [SocialLink]
  soldToday: Int!
  specialConsiderations: String
  stages: [String]
  state: EventState
  statusAsOfNow: String
  streamDetails: StreamDetails
  streamStats(streamType: StreamType!): StreamStats
  streetAddress: String
  stripeAccountId: String
  submittedAt: Time
  tags: [Tag]
  taxRates: [TaxRate]
  taxSettings: TaxSettings
  thirdPartySettings: ThirdPartySettings
  ticketNominationFields: [String]
  ticketPools: [TicketPool]
  ticketType: String
  ticketTypes(doorSalesOnly: Boolean, includeArchived: Boolean): [TicketType]
  timezoneName: String
  tokens: TokensList
  totalTickets: Int
  updatedAt: Time
  venue: String
  venueConfiguration: VenueConfiguration
  venueName: String
  venueSchedules: [VenueSchedule]
  venueSpace: VenueSpace
  venues: [Venue]
  waitingList: [WaitingListEntry]
  waitingListExchangeWindows: [WaitingListExchangeWindow]
}

type EventAdditionalInfo {
  content: String
  ctaLabel: String
  ctaLink: String
  id: ID!
  includeOnPurchaseEmail: Boolean
  includeOnReminderEmail: Boolean
}

input EventAdditionalInfoInput {
  content: String
  ctaLabel: String
  ctaLink: String
  id: ID
  includeOnPurchaseEmail: Boolean
  includeOnReminderEmail: Boolean
}

type EventArtist {
  artist: Artist
  description: String
  headliner: Boolean
  orderNumber: Int
}

input EventArtistInput {
  description: String
  headliner: Boolean
  id: ID
  musicbrainzArtistId: ID
  name: String
}

type EventBalance {
  account: AccountBalance
  amountPromoterOwed: Int!
  amountPromoterTotal: Int!
  availability: EventBalanceAvailability!
  credits: [EventCredit]
  feesSum: [FeesSum]!
  isProcessing: Boolean!
  payouts: [Payout]
  payoutsTotal: Int!
  productBalance: ProductBalance
  splitSum: [PriceSplit]
  tiers: [EventBalanceTier]
  totalSold: Int
  totalSum: Int
  totalsByMethod: [MethodPair]
}

enum EventBalanceAvailability {
  AVAILABLE
  INSUFFICIENT
  PENDING
}

type EventBalanceTier {
  count: Int
  faceValue: Int
  paymentMethod: PaymentMethod
}

input EventChangedNotificationChangeset {
  date: Time
  eventVenues: [EventVenues]
  flags: EventFlagsInput
  fullAddress: String
  lineup: [LineupInput]
  scheduleStatus: ScheduleStatus
}

type EventCompare {
  backendEvent: BackendEvent
  backendEventId: String
  status: EventCompareStatus
  values: [EventCompateResultValue]
}

enum EventCompareStatus {
  approx
  broken
  eq
  notEq
  notFound
  unset
}

type EventCompateResultValue {
  key: String!
  source: Map
  status: EventCompareStatus!
  target: Map
}

type EventConnection {
  count: Int!
  edges: [EventEdge]
  pageInfo: PageInfo!
}

enum EventCostCurrency {
  AED
  AFN
  ALL
  AMD
  AOA
  ARS
  AUD
  AWG
  AZN
  BAM
  BBD
  BDT
  BGN
  BHD
  BIF
  BMD
  BND
  BOB
  BRL
  BWP
  BYR
  BZD
  CAD
  CDF
  CHF
  CLP
  CNY
  COP
  CRC
  CVE
  CZK
  DJF
  DKK
  DOP
  DZD
  EGP
  ERN
  ETB
  EUR
  GBP
  GEL
  GHS
  GNF
  GTQ
  GYD
  HKD
  HNL
  HRK
  HUF
  IDR
  ILS
  INR
  IQD
  IRR
  ISK
  JMD
  JOD
  JPY
  KES
  KHR
  KMF
  KRW
  KWD
  KZT
  LBP
  LKR
  LRD
  LTL
  LVL
  LYD
  MAD
  MDL
  MGA
  MKD
  MMK
  MOP
  MUR
  MXN
  MYR
  MZN
  NAD
  NGN
  NIO
  NOK
  NPR
  NZD
  OMR
  PAB
  PEN
  PHP
  PKR
  PLN
  PYG
  QAR
  RON
  RSD
  RUB
  RWF
  SAR
  SDG
  SEK
  SGD
  SOS
  STD
  SYP
  THB
  TND
  TOP
  TRY
  TTD
  TWD
  TZS
  UAH
  UGX
  USD
  UYU
  UZS
  VEF
  VND
  XAF
  XOF
  YER
  ZAR
  ZMK
}

type EventCredit {
  promotionId: Int
  promotionName: String
  spent: Int!
  sponsorName: String
}

type EventDefaults {
  addArtistsToEvent: Boolean
  disableAttractiveIntegration: Boolean
  disableDayOfEventComms: Boolean
  hideFromDiscovery: Boolean
  manualValidationEnabled: Boolean
  printedTicketFormat: PrintedTicketFormat
  requiresBoxOfficeTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  taxFree: Boolean
  ticketTransfer: Boolean
  waitingList: Boolean
}

input EventDefaultsInput {
  """The name of the object type currently being queried."""
  
  addArtistsToEvent: Boolean
  disableAttractiveIntegration: Boolean
  disableDayOfEventComms: Boolean
  hideFromDiscovery: Boolean
  manualValidationEnabled: Boolean
  printedTicketFormat: PrintedTicketFormat
  requiresBoxOfficeTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  taxFree: Boolean
  ticketTransfer: Boolean
  waitingList: Boolean
}

type EventEdge {
  cursor: String
  node: Event
}

type EventFlags {
  alcoholFree: Map
  autoRescheduledEventRefundsDeadline: Map
  branded: Map
  claimTickets: Map
  codeLocked: Map
  competition: Map
  coolingOffPeriod: Map
  disableDayOfEventComms: Map
  enabledPwl: Map
  fanPickSeat: Map
  fanSurvey: Map
  featured: Map
  generateNewCodeOnTransfer: Map
  grouped: Map
  guestlist: Map
  hidden: Map
  hideFromDiscovery: Map
  mbwayEnabled: Map
  night: Map
  paperTicket: Map
  seated: Map
  shoppingCart: Map
  ticketTransfer: Map
  ticketTypes: Map
  unicorn: Map
  waitingList: Map
}

input EventFlagsInput {
  alcoholFree: FlagValue
  autoRescheduledEventRefundsDeadline: DeadlineInput
  branded: FlagValue
  claimTickets: FlagValue
  codeLocked: FlagValue
  competition: FlagValue
  coolingOffPeriod: CoolingOffPeriodInput
  disableDayOfEventComms: FlagValue
  enabledPwl: EnabledPwlInput
  fanPickSeat: FanPickSeatInput
  fanSurvey: FlagValue
  featured: FlagValue
  generateNewCodeOnTransfer: FlagValue
  grouped: FlagValue
  guestlist: FlagValue
  hidden: FlagValue
  hideFromDiscovery: FlagValue
  mbwayEnabled: FlagValue
  night: FlagValue
  paperTicket: FlagValue
  returns: FlagValue
  seated: FlagValue
  shoppingCart: FlagValue
  ticketTransfer: FlagValue
  ticketTypes: FlagValue
  unicorn: FlagValue
  waitingList: FlagValue
}

type EventImage implements Node {
  attachment: Attachment
  cdnUrl: String!
  cropRegion: CropRegion

  """The ID of an object"""
  id: ID!
  type: String
}

input EventImageInput {
  attachmentId: ID
  cropRegion: CropRegionInput
  id: ID
  type: String
}

enum EventLifeCycleState {
  DRAFT
  LIVE
  PAST
  PAST_EXCLUDING_ARCHIVED
}

type EventLoadPrediction implements Node {
  expectedRequestsPerMinute: Int
  expectedStartTime: Time

  """The ID of an object"""
  id: ID!
}

input EventLoadPredictionInput {
  expectedRequestsPerMinute: Int!
  expectedStartTime: Time!
  id: ID
}

type EventPayout {
  availability: EventBalanceAvailability!
  totalPaid: Int
  totalPending: Int
}

input EventPromoter {
  billingPromoter: Boolean
  promoterId: ID
}

type EventPromotion implements Node {
  accessType: PromotionAccessType!
  code: String
  codeLocks(after: String, before: String, filter: CodeLockFilter, first: Int, last: Int): CodeLockConnection
  discount: Int
  endDate: Time
  event: Event
  exportTokens: EventPromotionExportTokens!
  fanFacingName: String

  """The ID of an object"""
  id: ID!
  isEnded: Boolean!
  maxRedemptions: Int
  name: String!
  promotionType: PromotionType!
  seatingChannel: SeatingChannel
  startDate: Time
  ticketTypes: [TicketType]
  timesRedeemed: Int!
  timesUsed: Int!
  timesUsedUnique: Int!
  unlockAfterExpired: Boolean!
  usages(after: String, before: String, first: Int, last: Int): CodeUsageConnection
}

type EventPromotionExportTokens {
  codesToken: String!
  usagesToken: String!
}

type EventPromotionTicketPrice {
  discountedFaceValue: Int!
  discountedPrice: Int!
  discountedPriceWithPwl: Int
  discountedPriceWithoutPwl: Int
  faceValue: Int!
  fees: Int!
  price: Int!
  priceTier: PriceTier
  priceWithPwl: Int
  priceWithoutPwl: Int
}

type EventPromotionTicketTypePrices {
  prices: [EventPromotionTicketPrice]!
  ticketType: TicketType!
}

type EventReview implements Node {
  assignee: User
  event: Event

  """The ID of an object"""
  id: ID!
  priority: Boolean
  status: EventReviewStatus
}

type EventReviewConnection {
  edges: [EventReviewEdge]
  pageInfo: PageInfo!
}

type EventReviewEdge {
  cursor: String
  node: EventReview
}

enum EventReviewStatus {
  ESCALATED
  ON_HOLD
}

input EventReviewWhereInput {
  _or: [EventReviewWhereInput]
  assigneeId: OperatorsIdEqInput
  event: EventWhereInput
  id: OperatorsIdEqInput
  status: OperatorsEnumEventReviewStatus
}

type EventRules {
  covidPcr: Boolean
  covidPcrValidHours: Int
  covidPolicyUrl: String
  covidRecovery: Boolean
  covidVaccination: Boolean
  maskRequired: Boolean
  proofOfBeingHealthy: Boolean
  socialDistancing: Boolean
}

input EventRulesInput {
  covidPcr: Boolean
  covidPcrValidHours: Int
  covidPolicyUrl: String
  covidRecovery: Boolean
  covidVaccination: Boolean
  maskRequired: Boolean
  proofOfBeingHealthy: Boolean
  socialDistancing: Boolean
}

type EventSchedule {
  announceDate: Time
  date: Time
  endDate: Time
  offSaleDate: Time
  onSaleDate: Time
}

input EventScopesInput {
  eventState: [EventStates]
  eventType: [EventTypes]
  lifeCycleState: [EventLifeCycleState]
  streamingUrlState: [StreamingUrlState]
}

type EventSeatingChart implements Node {
  chartManagerCredentials: ChartManagerCredentials!
  chartThumbnail: String
  event: Event

  """The ID of an object"""
  id: ID!
  seatingChannels: [SeatingChannel]
  seatsIoChart: SeatsIoChart
  seatsIoChartReport: Map
  seatsIoEvent: SeatsIoEvent
  seatsIoEventId: String!
  seatsIoEventReport(group: SeatsIoEventReportBy!): Map
  venueChart: SeatingChart!
}

type EventSharingObject {
  email: String!
  id: ID
  permissionProfile: PermissionProfile!
}

input EventSharingObjectInput {
  email: String!
  id: ID
  permissionProfileId: ID!
}

enum EventSpecialFilters {
  onSaleToday
  runningLow
  soldOut
  toBePaid
  today
}

enum EventState {
  APPROVED
  ARCHIVED
  CANCELLED
  DECLINED
  DRAFT
  REVIEW
  SUBMITTED
}

enum EventStates {
  ANNOUNCED
  ARCHIVED
  NOT_COMPLETE
  ON_SALE
  READY_TO_SUBMIT
  RUNNING_LOW
  SOLD_OUT
  TO_BE_APPROVED
}

type EventTiming {
  offset: Int
  sourceField: EventTimingField
  targetField: EventTimingField
  type: EventTimingType
}

enum EventTimingField {
  ANNOUNCE_DATE
  DATE
  END_DATE
  LINE_UP_DOORS_OPEN
  OFF_SALE_DATE
  ON_SALE_DATE
  ON_SALE_REMINDER_TIME
  RETURN_DEADLINE
  TICKET_TYPE_ANNOUNCE_DATE
  TICKET_TYPE_OFF_SALE_DATE
  TICKET_TYPE_ON_SALE_DATE
  TRANSFER_DEADLINE
}

input EventTimingInput {
  offset: Int
  sourceField: EventTimingField
  targetField: EventTimingField
}

enum EventTimingType {
  DEFAULT
  PROMOTER
}

enum EventType {
  HYBRID
  LIVE
  STREAM
}

enum EventTypes {
  BOX_OFFICE
  RECURRING_EVENT
  REGULAR_EVENT
  STREAM_EVENT
}

input EventVenues {
  primary: Boolean
  venueId: ID
}

input EventWhereInput {
  _or: [EventWhereInput]
  accountId: OperatorsIdEqInput
  accountManagerId: OperatorsIdEqInput
  addressCountry: OperatorsString
  addressRegion: OperatorsString
  addressState: OperatorsString
  announceDate: OperatorsDateInput
  artistIds: OperatorsIdEqInput
  artistName: OperatorsString
  billingPromoter: OperatorsJsonInput
  billingPromoterName: OperatorsString
  bundleIds: OperatorsIdEqInput
  cityIds: OperatorsListOfStringInput
  closeEventDate: OperatorsDateInput
  countryCode: OperatorsEnumCountryCode
  createdAt: OperatorsDateInput
  date: OperatorsDateInput
  description: OperatorsString
  endDate: OperatorsDateInput
  endSaleDate: OperatorsDateInput
  eventIdLive: OperatorsString
  eventIdPreview: OperatorsString
  eventStatus: OperatorsString
  eventType: OperatorsString
  flagNames: OperatorsListOfStringInput
  free: OperatorsBooleanEqInput
  fullAddress: OperatorsString
  hierarchicalTagNames: OperatorsListOfStringInput
  id: OperatorsIdEqInput
  isAbbonamento: OperatorsBooleanEqInput
  labelNames: OperatorsListOfStringInput
  name: OperatorsString
  offSaleDate: OperatorsDateInput
  onSaleDate: OperatorsDateInput
  permName: OperatorsString
  priority: OperatorsBooleanEqInput
  promoterName: OperatorsString
  promoterPayoutsEnabled: OperatorsBooleanEqInput
  promoterTier: OperatorsEnumPromoterTier
  promoterTypeOfOrganizer: OperatorsEnumTypeOfOrganizer
  recurring: OperatorsBooleanEqInput
  scheduleStatus: OperatorsString
  special: EventSpecialFilters
  stages: OperatorsListOfStringInput
  state: OperatorsString
  stripeAccountId: OperatorsString
  stripeVerified: OperatorsBooleanEqInput
  submittedAt: OperatorsDateInput
  tagNames: OperatorsListOfStringInput
  venue: OperatorsString
  venueIds: OperatorsIdEqInput
  venueSeatingChartIds: OperatorsIdEqInput
  venueTier: OperatorsString
}

type EventsApiToken {
  partnerId: String
  token: String
}

enum EventsConnectionOrder {
  announceDateASC
  announceDateDESC
  cancelledAtASC
  cancelledAtDESC
  createdAtASC
  createdAtDESC
  dateASC
  dateDESC
  endDateASC
  endDateDESC
  nameASC
  nameDESC
  offSaleDateASC
  offSaleDateDESC
  onSaleDateASC
  onSaleDateDESC
  submittedAtASC
  submittedAtDESC
  ticketsSoldASC
  ticketsSoldDESC
  totalFaceValueASC
  totalFaceValueDESC
  venueASC
  venueDESC
}

type ExternalEntity {
  isEntertainment: Boolean!
  name: String
  value: String
}

type ExternalLinks {
  facebook: String
  instagram: String
  tiktok: String
  twitter: String
  website: String
}

input ExternalLinksInput {
  facebook: String
  instagram: String
  tiktok: String
  twitter: String
  website: String
}

type ExtrasRevenueReportItem {
  faceValue: Int!
  rebate: Int!
  revenue: Int!
  soldExtras: Int!
  time: Time!
}

interface FaceValueInterface {
  faceValue: Int!
}

type FacebookPage {
  autoCreateEvents: Boolean!
  name: String!
  pageId: ID!
  publishToLive: Boolean!
}

input FacebookPageInput {
  autoCreateEvents: Boolean!
  name: String!
  pageId: ID!
  publishToLive: Boolean!
}

type Fan implements Node {
  blocked: Boolean
  creditBalances: [CreditBalance]
  dob: Date
  email: String
  fanActivities(after: String, before: String, first: Int, last: Int, where: FanActivityWhereInput): FanActivityConnection
  firstName: String

  """The ID of an object"""
  id: ID!
  lastName: String
  pastEvents: [FanEvent]
  phone: String
  upcomingEvents: [FanEvent]
}

type FanAccountActivityPayload {
  changes: [Map]
}

type FanActivity implements Node {
  date: Time
  id: ID!
  payload: FanActivityPayload
  type: FanActivityType!
}

type FanActivityConnection {
  edges: [FanActivityEdge]
  pageInfo: PageInfo!
}

type FanActivityEdge {
  cursor: String
  node: FanActivity
}

type FanActivityLineItem {
  lineItemId: Int
  product: Product
}

type FanActivityNotificationPayload {
  event: Event
}

enum FanActivityOperationLogAction {
  ASSIGN_TICKETS
  CANCEL_RETURN_REQUEST
  REQUEST_RETURN
  REVOKE_SEATS
  USER_MERGE
  WL_BUMP
}

type FanActivityOperationLogPayload {
  action: FanActivityOperationLogAction
  event: Event
  mergeFanId: String
  operatorId: String
  seatName: String
  ticketType: TicketType
}

union FanActivityPayload = FanAccountActivityPayload | FanActivityNotificationPayload | FanActivityOperationLogPayload | FanActivityProductPayload | FanActivityTicketPayload

enum FanActivityProductAction {
  PRICE_CORRECTION
  PURCHASE
  REFUND
}

enum FanActivityProductActionType {
  ACCESSIBILITY_REQUEST
  ACCIDENTAL_PURCHASE
  APP_TECH_ISSUE
  AUTOREFUND
  DISPUTED_TRANSACTION
  EVENT_BUILD_ERROR_DICE
  EVENT_BUILD_ERROR_PARTNER
  EVENT_CANCELLED
  EVENT_COMPLAINT
  EVENT_POSTPONED_RESCHEDULED
  FRAUD_PREVENTION
  GOOD_WILL
  NO_VALID_ID
  OTHER
  PROMOTER_REQUEST
  REJECTED_AT_THE_DOOR
  SUPPORT_DISCRETION
  SUSPECTED_RESELLER
  SWAP_REQUEST
  TICKET_RESOLD
  VENUE_COMPLAINT
}

type FanActivityProductPayload {
  action: FanActivityProductAction
  actionType: FanActivityProductActionType
  actioner: User
  event: Event
  lineItems: [FanActivityLineItem]
  purchaseId: Int
  stripeLinks: [String]
}

enum FanActivityTicketAction {
  PRICE_CORRECTION
  PURCHASE
  REFUND
  REMOVE_FROM_WL
  RESOLD_FROM_WL
  RESTORED_FROM_WL
  RETURN_TO_WL
  SWAP
  TRANSFER
}

type FanActivityTicketPayload {
  action: FanActivityTicketAction!
  actioner: User
  charge: Charge
  event: Event
  lineItemIds: [Int]
  newEvent: Event
  newTicketIds: [Int]
  newTicketType: TicketType
  product: Product
  recipient: Fan
  ticketIds: [Int]
  ticketType: TicketType
}

enum FanActivityType {
  ACCOUNT_ACTIVITY
  COMMS
  NOTES
  NOTIFICATION
  OPERATION_LOG
  PRODUCT
  REFUND
  TICKET
  WAITING_LIST
}

input FanActivityWhereInput {
  date: OperatorsDateInput
  type: OperatorsString
}

type FanAddress {
  countryCode: String
  county: String
  line1: String
  line2: String
  postCode: String
  town: String
}

type FanConnect implements Node {
  event: Event

  """The ID of an object"""
  id: ID!
  insertedAt: Time
  message: String
  onBehalfOfUser: User
  scheduledAt: Time
  sendMeACopy: Boolean
  status: FanConnectStatus
  ticketTypes: [TicketType]
  title: String
  updatedAt: Time
  user: User
}

type FanConnectConnection {
  edges: [FanConnectEdge]
  pageInfo: PageInfo!
}

type FanConnectEdge {
  cursor: String
  node: FanConnect
}

enum FanConnectOrder {
  INSERTED_AT_ASC
  INSERTED_AT_DESC
  SCHEDULED_AT_ASC
  SCHEDULED_AT_DESC
}

enum FanConnectStatus {
  ARCHIVED
  ERROR
  PENDING
  PROCESSED
  PROCESSING
  SCHEDULED
}

input FanConnectWhereInput {
  eventId: OperatorsIdEqInput
  insertedAt: OperatorsDateInput
  onBehalfOfUserId: OperatorsIdEqInput
  promoterId: OperatorsIdEqInput
  scheduledAt: OperatorsDateInput
  status: OperatorsFanConnectStatusInput
}

type FanConnection {
  edges: [FanEdge]
  pageInfo: PageInfo!
}

type FanEdge {
  cursor: String
  node: Fan
}

type FanEvent {
  event: Event
  extraCount: Int
  ticketCount: Int
}

"""filter fans"""
input FanFilterInput {
  email: String
  name: String
  phone: PhoneNumber
}

input FanPickSeatInput {
  active: Boolean
  hours: Int
}

type FanQuestion {
  enabled: Boolean
  id: ID!
  required: Boolean
  ticketTypes: [TicketType]
  title: String!
  type: String!
}

input FanQuestionInput {
  enabled: Boolean
  id: ID!
  required: Boolean
  ticketTypeIds: [ID]!
}

type FanSupportNotes {
  body: String
}

input FanSupportNotesInput {
  body: String
}

input FanWhereInput {
  id: OperatorsIdEqInput
}

type FansStats {
  ageBreakdown: [AgeBreakdownItem]
  averageAge: Int
  female: Float
  male: Float
  other: Float
}

type Faq implements Node {
  body: String

  """The ID of an object"""
  id: ID!
  order: Int!
  title: String
}

input FaqInput {
  body: String!
  id: ID
  order: Int!
  title: String!
}

type FeatureFlags {
  """Enable access to artist inventory"""
  artistInventory: Boolean

  """Allows user to enable Unicorn Purchase Flow or Cart UI"""
  devSettings: Boolean

  """Disable Amex in EU depending on Experiements variable"""
  disablingAmex: Boolean

  """Enable access to the new event overview page"""
  eventOverviewV2: Boolean

  """Enable access to Events Collection page"""
  eventsCollection: Boolean

  """Allows user to work with extras"""
  extras: Boolean

  """Allows user to see UI for creating extras separate barcodes"""
  extrasSeparateAccessBarcodes: Boolean @deprecated(reason: "To be removed: all users who can see extras should see the separate code UI")

  """Enable access to holds"""
  holds: Boolean

  """
  Allows DICE staff to define promoter level defaults for event level settings
  """
  promoterDefaultEventSettings: Boolean

  """Allows to define which seat categories are on hold"""
  seatsHold: Boolean

  """Allows to test new ideas on Social links"""
  socialLinkExperiment: Boolean

  """Dictates whether or not ticket pools should be used for new events"""
  ticketPools: Boolean

  """Allows user to enable Box Office on a Unicorn event"""
  unicornBoxOffice: Boolean

  """Allows user to enable Promotions/Codelocks for Unicorn events"""
  unicornPromotions: Boolean
}

type FeaturedArea implements Node {
  countryCode: String
  description: String
  endDate: Time!
  event: Event

  """The ID of an object"""
  id: ID!
  locationLat: Float
  locationLng: Float

  """Radius expressed in user-facing 'units' - km or miles"""
  locationRadius: Float
  locationString: String
  locationUnits: DistanceUnits
  mode: TargetingMode!
  startDate: Time!
  weight: Int!
}

type FeaturedAreaEstimate {
  featuredArea: FeaturedArea

  """Weekly Average Users that will see this promo in a given position"""
  wauByLayer: [Int]
}

input FeaturedAreaInput {
  countryCode: String
  description: String
  endDate: Time!
  id: ID
  locationLat: Float
  locationLng: Float
  locationRadius: Float
  locationString: String
  locationUnits: DistanceUnits
  mode: TargetingMode!
  startDate: Time!
  weight: Int!
}

type Fee {
  active: Boolean @deprecated(reason: "No longer active")
  amount: Float
  applicable: Boolean
  split: [FeeSplit]
  type: FeeType!
  unit: FeeUnit!
}

enum FeeDestination {
  billingPromoter
  keep
}

input FeeInput {
  active: Boolean
  amount: Float!
  split: [FeeSplitInput]!
  type: FeeType!
  unit: FeeUnit!
}

type FeeOutput {
  active: Boolean @deprecated(reason: "No longer active")
  amount: Float
  applicable: Boolean
  computed: Int!
  split: [FeeSplitOutput]!
  type: FeeType!
  unit: FeeUnit!
}

type FeeRange {
  fees: [Fee]
  fromBasePrice: Int!
  id: ID!
}

input FeeRangeInput {
  fees: [FeeInput]
  fromBasePrice: Int
}

type FeeSplit {
  amount: Float
  destination: FeeDestination
  unit: FeeUnit!
}

input FeeSplitInput {
  amount: Float!
  destination: FeeDestination!
  unit: FeeUnit!
}

type FeeSplitOutput {
  amount: Float
  computed: Int!
  destination: FeeDestination
  unit: FeeUnit!
}

enum FeeType {
  additionalPromoterFee
  booking
  boxOfficeFee
  charityDonation
  deposit
  extraCharge
  facilityFee
  paidWaitingList
  postal
  presale
  processing
  salesTax
  tierDiff
  vendor
  venueFee
  venueLevy
}

enum FeeUnit {
  fixed
  percentage
}

interface Fees {
  fees: [Fee]
}

type FeesApplicationRule {
  feeRanges: [FeeRange]
  feeTarget: EnumFeeTarget
  feeTargetCategories: [Category]
  feeTargetParentCategory: Category
  id: ID!
}

input FeesApplicationRuleInput {
  feeRanges: [FeeRangeInput]
  feeTarget: EnumFeeTarget
  feeTargetCategoryIds: [ID]
}

enum FeesBehaviour {
  APPEND_TO_CONTRACT
  OVERRIDE
  USE_CONTRACT
}

type FeesConfiguration implements Node {
  allowedAdhocFeeTypes: [Fee]
  basePriceFees: [String]
  basePriceMode: BasePriceMode!
  boxOfficeFee: Int
  effectiveDate: Time
  endDate: Time
  feesApplicationRules: [FeesApplicationRule]

  """The ID of an object"""
  id: ID!
  postFanPriceFees: [String]
  taxExempt: Boolean!
  taxId: String
}

type FeesRates {
  additionalPromoterFee: Float
  bookingFee: Float
  charityFee: Float
  extraCharge: Float
  facilityFee: Float
  foodAndBeverage: Float
  fulfillment: Float
  meetAndGreet: Float
  presale: Float
  processingFee: Float
  pwlFee: Float
  tierDiff: Float
  vendor: Float
  venueFee: Float
  venueLevy: Float
}

type FeesSum {
  computed: Int!
  split: [PriceSplit]!
  type: FeeType!
}

type FinTran {
  accruals: [StripeAccrual]
  causedById: Int!
  causedByType: FinTranCausationType!
  committedAt: Time
  disposedAt: Time
  finTranItems: [FinTranItem]!
  kind: FinTranKind!
  paymentIntent: StripePaymentIntent
  purchaseId: Int!
  refunds: [StripeRefund]
  txn: String!
}

enum FinTranAccountType {
  DICE_PLATFORM
  FAN_CASH
  FAN_CREDIT
  PARTNER
  SWAP
}

enum FinTranCausationType {
  FAN_ACTION
  PAYMENT
  PURCHASE
  SUPPORT_ACTION
}

type FinTranItem {
  accountType: FinTranAccountType!
  balanceChange: Int!
  billingDestination: BillingDestination
  lineItemId: Int
  priceComponent: String
}

enum FinTranKind {
  ADJUSTMENT
  CUSTOM
  PAYMENT
  RETURN
  SWAP_IN
  SWAP_OUT
}

input FinishStripeOnboardingInput {
  clientMutationId: String!
  onboardingCode: String!
  state: String!
}

type FinishStripeOnboardingPayload {
  clientMutationId: String!
  promoter: Promoter
}

input FlagValue {
  active: Boolean
}

input ForceMarkbackAllocationInput {
  clientMutationId: String!
  id: ID!
}

type ForceMarkbackAllocationPayload {
  clientMutationId: String!
  event: Event
}

type GuestList {
  entries(after: String, before: String, first: Int, last: Int, orderBy: [GuestListOrder], searchTerm: String): GuestListEntryConnection
  guestsCount: Int
  ticketsCount: Int
}

type GuestListEntry implements Node {
  email: EmailAddress

  """The ID of an object"""
  id: ID!
  quantity: Int
  state: GuestListEntryState
  ticketType: TicketType
}

type GuestListEntryConnection {
  edges: [GuestListEntryEdge]
  pageInfo: PageInfo!
}

type GuestListEntryEdge {
  cursor: String
  node: GuestListEntry
}

enum GuestListEntryState {
  ACTIVATED
  ASSIGNED
  ERROR
  INVITED
  PENDING
  PROCESSING
}

enum GuestListOrder {
  INSERTED_AT_ASC
  INSERTED_AT_DESC
}

type HierarchicalTag implements Name & Node {
  createdAt: Time!
  deprecated: Boolean!

  """The ID of an object"""
  id: ID!
  kind: String
  name: String
  parent: HierarchicalTag
  parentId: ID
  updatedAt: Time!
}

enum HierarchicalTagOrder {
  NAME
}

input HierarchicalTagWhereInput {
  _or: [HierarchicalTagWhereInput]
  deprecated: OperatorsBooleanEqInput
  kind: String
  name: OperatorsString
  parentId: OperatorsIdEqInput
}

type HierarchicalTagsConnection {
  count: Int!
  edges: [HierarchicalTagsEdge]
  pageInfo: PageInfo!
}

type HierarchicalTagsEdge {
  cursor: String
  node: HierarchicalTag
}

type Hold {
  allocation: Int
  name: String
}

input HoldInput {
  allocation: Int
  name: String
}

input HolderName {
  email: String
  firstName: String
  idNumber: String
  lastName: String
  phoneNumber: String
}

input ImpersonateUserInput {
  accountId: ID
  clientMutationId: String!
  id: ID
  reason: String!
}

type ImpersonateUserPayload {
  clientMutationId: String!
  token: String
}

input ImportPayoutsInput {
  clientMutationId: String!
  importType: PayoutImportType
  storagePath: String
}

type ImportPayoutsPayload {
  clientMutationId: String!
  status: String
}

type IntegrationStatus {
  externalId: String
  insertedAt: Time
  status: String
}

type IntegrationToken {
  event: Event
  fullAccess: Boolean
  insertedAt: Time
  token: String
  user: User
  venue: Venue
}

type Inventory implements Node {
  currency: EventCostCurrency

  """The ID of an object"""
  id: ID!
  products: [Product]
  shopifyStore: ShopifyStore
  tokens: InventoryTokenList
}

input InventoryProductInput {
  id: ID!
  purchaseConfirmationMessage: String
}

type InventoryTokenList {
  reconciliationReportExportToken: String
}

input InviteGuestListInput {
  clientMutationId: String!
  emails: [EmailAddress]!
  quantity: Int!
  ticketTypeId: ID!
}

type InviteGuestListPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: GuestList

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input InviteUserInput {
  accountId: ID!
  clientMutationId: String!
  email: String!
  permissionProfileId: ID!
}

type InviteUserPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: AccountUserInvitation

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

scalar Json

type Label implements ChecklistsNode & Node {
  checklists: [Checklist]!
  description: String!

  """The ID of an object"""
  id: ID!
  name: String!
}

input LabelWhereInput {
  _or: [LabelWhereInput]
  name: OperatorsString
}

type LabelsConnection {
  count: Int!
  edges: [LabelsEdge]
  pageInfo: PageInfo!
}

type LabelsEdge {
  cursor: String
  node: Label
}

enum Language {
  CA
  DE
  EN_CA
  EN_GB
  EN_IN
  EN_US
  ES
  FR
  IT
  PT
}

enum LegalEntityType {
  COMPANY
  INDIVIDUAL
}

input LineupInput {
  details: String
  time: String
}

type Link {
  name: String
  url: String
}

input LinkInput {
  name: String
  url: String
}

type LinkStats {
  amount: Int!
  campaign: String!
  channel: String!
  id: ID
  postType: SocialLinkPostType!
  priceTier: PriceTier
  socialLink: SocialLink
  statsType: LinkStatsType!
  ticketType: TicketType
}

enum LinkStatsType {
  clicks
  faceValue
  purchases
  ticketsSold
}

"""Linkout"""
type Linkout implements Node {
  """Primary event artist"""
  artist: Artist

  """DICE cities for the event"""
  cities: [City]

  """Ticket currency"""
  currency: EventCostCurrency

  """Event start date"""
  date: Time!

  """DICE event, required if the linkout_type is internal"""
  destinationEvent: Event

  """Event end date"""
  endDate: Time!

  """External URL, required if linkout_type is external"""
  externalUrl: String

  """The ID of an object"""
  id: ID!

  """Event image"""
  imageAttachment: Attachment

  """Event image crop region"""
  imageCropRegion: CropRegion

  """Linkout type"""
  linkoutType: LinkoutType!

  """Event name"""
  name: String!

  """Event off-sale date"""
  offSaleDate: Time

  """Event on-sale date"""
  onSaleDate: Time

  """Representative price for the event"""
  price: Int

  """Event promoter"""
  promoter: Promoter

  """Event tags"""
  tags: [Tag]

  """Event timezone"""
  timezone: String!

  """Event venue"""
  venue: Venue
}

type LinkoutConnection {
  count: Int!
  edges: [LinkoutEdge]
  pageInfo: PageInfo!
}

type LinkoutEdge {
  cursor: String
  node: Linkout
}

"""
The type of linkout, with internal being DICE events, and external being non-DICE events.
"""
enum LinkoutType {
  EXTERNAL
  INTERNAL
}

type List {
  id: String
  name: String
}

interface Location {
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressState: String
  countryCode: String
  fullAddress: String
  latitude: Float
  longitude: Float
  postOfficeBoxNumber: String
  postalCode: String
  streetAddress: String
  timezoneName: String
}

type LocationStats {
  itemsByCity: [LocationStatsItem!]!
  itemsByCountry: [LocationStatsItem!]!
}

type LocationStatsItem {
  amount: Int!
  city: String!
  country: String!
}

type Mailchimp {
  audienceLists: [List]
  authorizeUrl: String
  settings: MailchimpSettings
}

type MailchimpSettings {
  connectedAt: Time
  mailchimpListId: String
  mailchimpStoreId: String
  syncOnlyOptIn: Boolean
}

scalar Map

input MarkBackAllocationsInput {
  clientMutationId: String!
  id: ID!
}

type MarkBackAllocationsPayload {
  clientMutationId: String!
  event: Event
}

type Marketeer implements Name & Node {
  accessToken: String
  appOptInEnabled: Boolean
  contacts: [User]
  fbAccessToken: String
  fbPixelId: String
  gaTrackingId: String
  googleAdsConversionId: String
  googleAdsPurchaseConversionLabel: String

  """The ID of an object"""
  id: ID!
  name: String
  privacyPolicyLink: String
  tiktokPixelId: String
  twitterCheckoutInitiatedPixelId: String
  twitterPixelId: String
  twitterPurchasePixelId: String
  webOptInEnabled: Boolean
}

input MarketeerWhereInput {
  _or: [MarketeerWhereInput]
  id: OperatorsIdEqInput
  name: OperatorsString
}

type MarketeersConnection {
  count: Int!
  edges: [MarketeersEdge]
  pageInfo: PageInfo!
}

type MarketeersEdge {
  cursor: String
  node: Marketeer
}

type MediaItem {
  id: ID!
  type: MediaItemTypes!
  values: Json
}

input MediaItemInputObject {
  id: ID
  type: MediaItemTypes!
  values: MediaValuesInput!
}

enum MediaItemTypes {
  appleMusicTrack
  spotifyArtist
  spotifyTrack
  trailer
}

input MediaValuesInput {
  artistId: String
  image: String
  mediaUri: String
  name: String
  openUrl: String
  previewUrl: String
  video: String
}

type MethodPair {
  method: PaymentMethod!
  value: Int!
}

input MinorAttractiveFieldsInput {
  author: String
  compatibilityAe: CompatibilityAe
  distributor: String
  id: ID
  nationality: String
  performer: String
  producer: String
}

input MinorEventImageInput {
  attachmentId: ID
  cropRegion: CropRegionInput
  id: ID
  type: String
}

input MinorPriceTierInput {
  allocation: Int
  attractivePriceType: String
  doorSalesPrice: Int
  faceValue: Int
  fees: [FeeInput]
  id: ID
  name: String
  time: Time
}

input MinorTicketPoolInput {
  id: ID
  maxAllocation: Int
  name: String
}

input MinorTicketTypeInput {
  activateCodeDateOffset: Int
  additionalPaymentMethods: [PaymentMethods]
  allocation: Int
  allowSeatChange: Boolean
  announceDate: Time
  archived: Boolean
  attractivePriceType: String
  attractiveSeatingAreaType: String
  attractiveTaxFree: Boolean
  codeLocked: Boolean
  description: String
  doorSalesEnabled: Boolean
  doorSalesPrice: Int
  endDate: Time
  externalSkus: [String]
  faceValue: Int
  fees: [FeeInput]
  hidden: Boolean
  icon: String
  id: ID
  increment: Int
  isStream: Boolean
  maximumIncrements: Int
  name: String
  offSaleDate: Time
  onSaleDate: Time
  order: Int
  priceGrade: String
  priceHidden: Boolean
  priceTierType: PriceTierTypes
  priceTiers: [MinorPriceTierInput]
  productIds: [ID]
  requiresAddress: Boolean
  reservedForGuestList: Boolean
  reservedSeating: Boolean
  reservedSeatingType: ReservedSeatingTypes
  salesLimit: Int
  seatCategories: [SeatCategoriesInput]
  seatmapUrl: String
  startDate: Time
  streamEmbedCode: String
  streamLink: String
  ticketPoolId: String
  venueScheduleId: ID
  venueScheduleIndex: Int
}

input MinorUpdateEventInput {
  """The name of the object type currently being queried."""
  
  additionalArtists: [AdditionalArtistInput]
  additionalInfos: [EventAdditionalInfoInput]
  addressCapacity: Int
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressSiaeCode: String
  addressState: String
  ageLimit: String
  announceDate: Time
  artistIds: [ID]
  artists: [EventArtistInput]
  attractiveFields: MinorAttractiveFieldsInput
  bundleIds: [ID]
  characteristicIds: [ID]
  clientMutationId: String!
  closeEventDate: Time
  countryCode: String
  date: Time
  description: String
  diceStreamDuration: Int
  diceStreamDvrEnabled: Boolean
  diceStreamRewatchEnabledUntil: Time
  disableUsTax: Boolean
  doorlistAdditionalRecipients: [String]
  endDate: Time
  eventImages: [MinorEventImageInput]
  eventLoadPredictions: [EventLoadPredictionInput]
  eventRules: EventRulesInput
  eventSharingObjects: [EventSharingObjectInput]
  eventVenues: [EventVenues]
  extraNotes: String
  fanSupportNotes: FanSupportNotesInput
  faqs: [FaqInput]
  featuredAreas: [FeaturedAreaInput]
  flags: EventFlagsInput
  fullAddress: String
  hierarchicalTagIds: [ID]
  id: ID!
  isTest: Boolean
  isTicketAvailableAtDoor: Boolean
  latitude: Float
  lineup: [LineupInput]
  links: [LinkInput]
  lockVersion: Int!
  longitude: Float
  manualValidationEnabled: Boolean
  marketeerIds: [ID]
  maxTicketsLimit: Int
  media: [MediaItemInputObject]
  musicbrainzArtists: [MusicbrainzArtists]
  name: String
  offSaleDate: Time
  onSaleDate: Time
  onSaleNotification: Boolean
  onSaleNotificationAt: Time
  postOfficeBoxNumber: String
  postalCode: String
  presentedBy: String
  products: [ProductInput]
  pwlWindow: Int
  readAccessEmails: [String]
  reason: String
  requiresBoxOfficeTicketNomination: Boolean
  requiresTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  scheduleStatus: ScheduleStatus
  seatingChannels: [SeatingChannelInput]
  sendReceiptViaSms: Boolean
  showArtistDescription: ShowArtistDescription
  socialDistancingRulesetKey: String
  streetAddress: String
  tagIds: [ID]
  thirdPartySettingsId: ID
  ticketPools: [MinorTicketPoolInput]
  ticketTypes: [MinorTicketTypeInput]
  timezoneName: String
  venueConfigurationId: ID
  venueSpaceId: ID
  waitingListExchangeWindows: [WaitingListExchangeWindowInput]
}

type MinorUpdateEventPayload {
  clientMutationId: String!
  event: Event
}

type MusicbrainzArtist implements Name & Node {
  disambiguation: String

  """The ID of an object"""
  id: ID!
  name: String
}

input MusicbrainzArtists {
  id: ID
  name: String
}

interface Name {
  id: ID!
  name: String
}

input NewProductInput {
  allTicketTypes: Boolean
  allocation: Int
  archived: Boolean
  categoryId: ID
  customCover: Boolean
  date: Time
  description: String
  endDate: Time
  excludedFromCrmAutomation: Boolean
  faceValue: Int
  fees: [FeeInput]
  fulfilledBy: String
  hasSeparateAccessBarcodes: Boolean
  hasVariants: Boolean
  locationNote: String
  name: String
  offSaleDate: Time
  onSaleDate: Time
  optionType: ProductOptionType
  productImages: [ProductImageInput]
  productType: ProductType
  purchaseConfirmationMessage: String
  sellingPoints: [SellingPointInput]
  sku: String
  variants: [VariantInput]
  venueId: ID
}

interface Node {
  """The ID of the object."""
  id: ID!
}

type NotificationBatch {
  id: ID
  message: String!
  notifyOn: Time!
  reason: NotificationReason
  state: NotificationBatchState
}

enum NotificationBatchState {
  DISABLED
  DRAFT
  SCHEDULED
  SENT
}

type NotificationPermissions {
  allowEventApprovedNotification: Boolean
  allowEventSubmittedNotification: Boolean
  allowNewDraftNotification: Boolean
}

input NotificationPermissionsInput {
  allowEventApprovedNotification: Boolean
  allowEventSubmittedNotification: Boolean
  allowNewDraftNotification: Boolean
}

enum NotificationReason {
  CANCELLED
  RESCHEDULED
}

input OperatorsBooleanEqInput {
  eq: Boolean
  ne: Boolean
  neOrNull: Boolean
}

input OperatorsDateInput {
  between: [Time]
  gt: Time
  gte: Time
  lt: Time
  lte: Time
  notBetween: [Time]
  null: Boolean
}

input OperatorsEnumCountryCode {
  eq: CountryCode
  in: [CountryCode]
}

input OperatorsEnumEventReviewStatus {
  eq: EventReviewStatus
  in: [EventReviewStatus]
}

input OperatorsEnumPromoterTier {
  eq: PromoterTier
  in: [PromoterTier]
}

input OperatorsEnumStripeRestrictionStatus {
  eq: StripeRestrictionStatus
  in: [StripeRestrictionStatus]
}

input OperatorsEnumTypeOfOrganizer {
  eq: EnumTypeOfOrganizer
  in: [EnumTypeOfOrganizer]
}

input OperatorsFanConnectStatusInput {
  eq: FanConnectStatus
  in: [FanConnectStatus]
  ne: FanConnectStatus
  notIn: [FanConnectStatus]
}

input OperatorsIdEqInput {
  eq: ID
  in: [ID]
  ne: ID
  neOrNull: ID
  notIn: [ID]
}

input OperatorsInteger {
  between: [Int]
  eq: Int
  in: [Int]
  ne: Int
  neOrNull: Int
  notBetween: [Int]
}

input OperatorsJsonInput {
  eq: Map
  ne: Map
  neOrNull: Map
}

input OperatorsListOfStringInput {
  contains: [String]
  eq: [String]
  ne: [String]
  notContains: [String]
}

input OperatorsString {
  eq: String
  iLike: String
  in: [String]
  ne: String
  neOrNull: String
  notLike: String
  null: Boolean
}

type PageInfo {
  """When paginating forwards, the cursor to continue."""
  endCursor: String

  """When paginating forwards, are there more items?"""
  hasNextPage: Boolean!

  """When paginating backwards, are there more items?"""
  hasPreviousPage: Boolean!

  """When paginating backwards, the cursor to continue."""
  startCursor: String
}

type PamUser {
  email: String
  firstName: String
  id: ID
  lastName: String
  name: String
}

type ParentCategoryBreakdown {
  category: Category
  categoryBreakdown: [CategoryBreakdown]
}

type PartnerPermissionProfileOverride {
  permissionProfile: PermissionProfile!
  subjects: [Subject]!
}

enum PartnerRefundReason {
  ACCESSIBILITY_REQUEST
  ACCIDENTAL_PURCHASE
  EVENT_BUILD_ERROR_PARTNER
  EVENT_COMPLAINT
  EVENT_POSTPONED_RESCHEDULED
  FRAUD_PREVENTION
  GOOD_WILL
  NO_VALID_ID
  OTHER
  REJECTED_AT_THE_DOOR
  SUSPECTED_RESELLER
  VENUE_COMPLAINT
}

input PauseAllocationInput {
  clientMutationId: String!
  id: ID!
}

type PauseAllocationPayload {
  clientMutationId: String!
  event: Event
}

enum PaymentMethod {
  APP
  CARD
  POS
}

enum PaymentMethods {
  AFTERPAY_CLEARPAY
  DICE_SPLIT
  GIROPAY
}

type Payout {
  amount: Float!
  arrivalDate: Time!
  created: Time!
  currency: String
  details: String
  event: Event @deprecated(reason: "Use events to get list of events instead")
  events: [Event]
  failure: String
  id: ID
  isAdvance: Boolean!
  isManual: Boolean!
  means: PayoutMeans!
  payoutId: String
  reason: PayoutReason!
  status: String!
  type: String!
}

type PayoutConnection {
  edges: [PayoutEdge]
  pageInfo: PageInfo!
}

type PayoutEdge {
  cursor: String
  node: Payout
}

enum PayoutImportType {
  """Deductions format requried for this import type"""
  DEDUCTIONS
  PAYOUTS @deprecated(reason: "Deductions format become unified format")
}

enum PayoutMeans {
  AUTO_BY_DICE
  AUTO_BY_PROMOTER
  AUTO_BY_SYSTEM
  BALANCE_ADJUSTMENT
  CREDIT @deprecated(reason: "replaced by balance adjustment")
  MANUAL_BANK_TRANSFER
  MANUAL_VIA_STRIPE_UI
  ROLLING
}

enum PayoutReason {
  ADJUSTMENT @deprecated(reason: "Use MISC_ADJUSTMENT instead")
  ADVANCE_RECOUPMENT
  CHARGEBACK_DEDUCTION
  CHARGEBACK_RECOUPMENT @deprecated(reason: "Use CHARGEBACK_DEDUCTION instead")
  MISC_ADJUSTMENT
  PAYMENT
  ROLLING_PAYMENTS_ADJUSTMENT
  WITHDRAWAL
  WITHDRAWALS @deprecated(reason: "Use WITHDRAWAL instead")
}

enum PayoutsExportKind {
  CSV
  RWQ
}

enum PayoutsOrder {
  ACCOUNT_NAME_ASC
  ACCOUNT_NAME_DESC
  AMOUNT_ASC
  AMOUNT_DESC
  ARRIVAL_DATE_ASC
  ARRIVAL_DATE_DESC
  CREATED_ASC
  CREATED_DESC
  EVENT_NAME_ASC
  EVENT_NAME_DESC
}

input PayoutsWhereInput {
  accountId: OperatorsIdEqInput
  amount: OperatorsInteger
  arrivalDate: OperatorsDateInput
  created: OperatorsDateInput
  eventId: OperatorsIdEqInput
  status: OperatorsString
}

input PerformPayoutForAccountInput {
  amount: Int!
  clientMutationId: String!
  currency: EventCostCurrency!
  id: ID!
}

type PerformPayoutForAccountPayload {
  clientMutationId: String!
  payoutId: ID!
}

enum PerformerType {
  ARTIST
  COMEDIAN
}

type PermissionProfile implements Node {
  caption: String!
  diceStaff: Boolean!

  """The ID of an object"""
  id: ID!
  insertedAt: Time!
  roleName: String!
  source: PermissionProfileSource!
  subjects: [Subject]
  updatedAt: Time!
  usedBy: Int!
}

type PermissionProfileOverride {
  id: ID!
  permissionProfile: PermissionProfile!
  subjects: [Subject]!
}

input PermissionProfileOverrideInput {
  id: ID
  permissionProfileId: ID
  subjects: [SubjectInput]
}

enum PermissionProfileSource {
  ACCOUNT
  SYSTEM
}

type PermissionProfileStructure {
  subjects: [Subject]
}

"""Phone Number"""
scalar PhoneNumber

enum PlatformAccountCode {
  AU
  CA
  DE
  ES
  FR
  GB
  IN
  IT
  PT
  US
}

type Price {
  breakdown: [FeeOutput]!
  faceValue: Int!
  fees: Int!
  friendlyFaceValue: Int
  friendlyPrice: Int
  salesTax: Int!
  split: [PriceSplit]!
  total: Int!
  totalWithPwl: Int
  totalWithoutPwl: Int
  vatAmount: Int!
  vatRate: Float!
}

interface PriceBreakdownInterface {
  price: Int
  priceBreakdown: Price
  rebate: Int
}

type PriceComponent {
  dice: Int
  partner: Int
  total: Int
  type: PriceComponentType
}

enum PriceComponentType {
  ADDITIONALPROMOTERFEE
  ADJUSTMENTLOSS
  BOOKING
  BOXOFFICEFEE
  CHARITYDONATION
  DEPOSIT
  DICECOST
  DISCOUNT
  EXTRACHARGE
  FACEVALUE
  FACILITYFEE
  OPERATIONALCOST
  OPPORTUNITYCOST
  PAIDWAITINGLIST
  POSTAL
  PRESALE
  PROCESSING
  SALESTAX
  TAXCOST
  TIERDIFF
  VENDOR
  VENUEFEE
  VENUELEVY
}

type PriceRange {
  from: Int!
  to: Int!
}

type PriceSplit {
  computed: Int!
  destination: FeeDestination!
}

type PriceTier implements FaceValueInterface & Fees & Node & PriceBreakdownInterface {
  allocation: Int
  attractivePriceType: String
  doorSalesPrice: Int
  doorSalesPriceTaxed: Int
  faceValue: Int!
  fees: [Fee]

  """The ID of an object"""
  id: ID!
  name: String
  order: Int!
  price: Int
  priceBreakdown: Price
  rebate: Int
  ticketTypeId: Int
  time: Time
}

type PriceTierBreakdown {
  appSold: Int!
  digitalValue: Int!
  fees: [SalesFees]!
  order: Int!
  payoutValue: Int!
  posSold: Int!
  priceTier: PriceTier!
  promotionsBreakdown: [PromotionBreakdown]!
  rebate: Int!
  reserved: Int!
  sold: Int!
  terminalSold: Int!
  value: Int!
}

enum PriceTierTypes {
  allocation
  time
}

input PriceTiersInput {
  allocation: Int
  attractivePriceType: String
  doorSalesPrice: Int
  faceValue: Int
  fees: [FeeInput]
  id: ID
  name: String
  time: Time
}

type PrintableTicketLegalDetails {
  activationCard: String
  emissionDate: String
  eventGenre: String
  faceValue: String
  fiscalSeal: String
  mediaType: String
  piHolder: String
  piOrganiser: String
  presaleFee: String
  priceType: String
  progressiveNumber: String
  seatingArea: String
  system: String
}

enum PrintedTicketFormat {
  BOCA_6X3_NO_LOGO
  BOCA_55X2
  NO_PRINTER
  STAR_RECEIPT
  STAR_RECEIPT_ETICKET
}

type Product implements Fees & Node & PriceBreakdownInterface {
  allTicketTypes: Boolean!
  allocation: Int!
  archived: Boolean!
  artistEvents: [ArtistEvent]
  category: Category!
  customCover: Boolean
  date: Time
  description: String!
  endDate: Time
  event: Event
  excludedFromCrmAutomation: Boolean!
  faceValue: Int!
  fees: [Fee]
  fulfilledBy: String
  hasSeparateAccessBarcodes: Boolean!
  hasVariants: Boolean!

  """The ID of an object"""
  id: ID!
  linkedLiveEventsCount: Int!
  locationNote: String
  name: String!
  offSaleDate: Time
  onSaleDate: Time
  optionType: ProductOptionType
  price: Int
  priceBreakdown: Price
  productImages: [ProductImage]
  productType: ProductType!
  purchaseConfirmationMessage: String
  rebate: Int
  rootType: ProductRootType!
  sellingPoints: [SellingPoint]!
  sku: String
  ticketTypes: [TicketType]!
  variants: [Variant]!
  venue: Venue
}

type ProductBalance {
  diceFees: Int
  diceSalesTax: Int
  partnerPayout: Int
  total: Int
}

type ProductBreakdownItem {
  product: Product!
  productId: ID!
  totalAppSold: Int!
  totalDigitalValue: Int!
  totalFaceValue: Int!
  totalSold: Int!
  variantBreakdown: [VariantBreakdownItem]
}

type ProductImage implements Node {
  attachment: Attachment
  cdnUrl: String!
  cropRegion: CropRegion

  """The ID of an object"""
  id: ID!
}

input ProductImageInput {
  attachmentId: ID
  cropRegion: CropRegionInput
  id: ID
}

input ProductInput {
  allTicketTypes: Boolean
  allocation: Int
  archived: Boolean
  categoryId: ID
  customCover: Boolean
  date: Time
  description: String
  endDate: Time
  eventId: ID
  excludedFromCrmAutomation: Boolean
  faceValue: Int
  fees: [FeeInput]
  fulfilledBy: String
  hasSeparateAccessBarcodes: Boolean
  hasVariants: Boolean
  id: ID
  locationNote: String
  name: String
  offSaleDate: Time
  onSaleDate: Time
  optionType: ProductOptionType
  productImages: [ProductImageInput]
  productType: ProductType
  purchaseConfirmationMessage: String
  sellingPoints: [SellingPointInput]
  sku: String
  ticketTypeIds: [ID]
  variants: [VariantInput]
  venueId: ID
}

enum ProductOptionType {
  CUSTOM
  SIZE
}

enum ProductRootType {
  EXTRAS
  MERCH
}

type ProductStats {
  priceComponents: [PriceComponent]
  product: Product!
  sold: Int
}

input ProductToEventsInput {
  eventIds: [ID]
  productId: ID!
}

enum ProductType {
  ADDON
}

type ProductsList {
  products: [Product]
}

type ProductsRevenueReportItem {
  faceValue: Int!
  rebate: Int!
  revenue: Int!
  soldItems: Int!
  time: Time!
}

type ProductsSales {
  parentCategoryBreakdown: [ParentCategoryBreakdown]
  productBreakdown(rootType: ProductRootType): [ProductBreakdownItem]
}

type ProfileDetails {
  description: String
  imageAttachment: Attachment
  imageCropRegion: CropRegion
}

input ProfileDetailsInput {
  description: String
  imageAttachmentId: ID
  imageCropRegion: CropRegionInput
}

type Promoter implements Contracts & Fees & Location & Name & Node & TimeStamps {
  accountBalance: AccountBalance
  accountIban: String!
  accountId: ID!
  accountManager: User @deprecated(reason: "To be removed, use account_managers instead")
  accountManagers: [User]
  accountName: String!
  accountNumber: String!
  accountPermissionProfiles: [PermissionProfile]
  accountSortCode: String!
  accountType: String!
  accountVatNumber: String!
  activities(after: String, before: String, first: Int, last: Int): ActivitiesConnection @deprecated(reason: "To be removed, please use viewer.activities instead")
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressState: String
  admissionConfigs: [AdmissionConfig]
  allowSkipReview: Boolean
  allowedLifecycleUpdates: PromoterAllowedLifecycleUpdates
  apiToken: String
  apiTokenExpiryDate: Time
  associatedMarketeers: [Marketeer]
  associatedVenues: [Venue]
  autoRescheduledEventRefunds: AutoRescheduledEventRefunds
  automaticRollingPaymentsConfiguration: AutomaticRollingPaymentsConfiguration
  bankAddress: String
  bankName: String
  billingNotes: String
  chargesEnabled: Boolean
  charityTaxFree: Boolean
  clientSuccessManagers: [User]
  connectedAccount: ConnectedAccount
  contacts: [User]
  contracts: [FeesConfiguration]
  coolingOffPeriod: Boolean
  coolingOffPeriodHours: Int
  countryCode: String
  createdAt: Time
  curatedBundle: Bundle
  defaultEventTimings: [EventTiming]
  dicePartner: Boolean
  disableUsTax: Boolean
  disabledReason: EnumAccountDisabledReason
  displayName: String
  eventDefaults: EventDefaults
  extrasEnabled: Boolean
  fanSupportNotes: FanSupportNotes
  fansStats: FansStats
  fees: [Fee]
  forbidSelfPayouts: Boolean
  fullAddress: String
  holdPayouts: Boolean

  """The ID of an object"""
  id: ID!
  isDisabled: Boolean
  isTest: Boolean
  labels: [Label]
  latitude: Float
  legalEntity: String
  licenseNumber: String!
  links: [Map]
  liveEventsCount: Int
  locationStats: LocationStats!
  longitude: Float
  merchEnabled: Boolean
  missingStripeFields: [String]
  name: String
  notes: String!
  partnerPermissionProfileOverrides: [PartnerPermissionProfileOverride]
  payoutsEnabled: Boolean
  permName: String
  permissionProfileOverrides: [PermissionProfileOverride]
  platformAccountCode: PlatformAccountCode
  postOfficeBoxNumber: String
  postalCode: String
  profileActive: Boolean
  profileDetails: ProfileDetails
  promoterTaxSettings: PromoterTaxSettings
  qflowEnabled: Boolean
  remittanceRecipients: [String]
  resoldEnabled: Boolean
  routingNumber: String!
  salesforceContracts: [SalesforceContract]
  salesforceFields: SalesforcePromoterFields
  sendReceiptViaSms: Boolean
  showPriceBreakdown: Boolean!
  showPriceSuggestions: Boolean!
  source: UserSource
  statusNotes: String
  streetAddress: String
  stripeAccountCurrencies: [String]
  stripeAccountId: String
  stripeAccountType: String

  "The reason for the stripe account to be disabled.\nPossible values are described in Stripe docs:\nhttps://stripe.com/docs/api/accounts/object#account_object-requirements-disabled_reason\n"
  stripeDisabledReason: String
  stripeDocumentId: String
  stripeFallbackAccountId: String
  stripeFallbackPlatformCode: PlatformAccountCode
  stripeLocationId: String
  stripeLoginUrl: String
  stripeOauthUrl: String!
  stripeOnboardingUrl: String!
  stripeRestrictionStatus: StripeRestrictionStatus
  stripeSetupInitiated: Boolean
  stripeVerified: Boolean
  swiftCode: String!
  tags: [Tag]
  taxCode: String
  ticketAgreementComplete: Boolean
  tier: String
  timezoneName: String
  typeOfOrganizer: EnumTypeOfOrganizer
  updatedAt: Time
  verificationDataSubmitted: Boolean
  verificationDueBy: Time
}

type PromoterAllowedLifecycleUpdates {
  accountManagers: AllowedLifecycleUpdateBase
  clientSuccessManagers: AllowedLifecycleUpdateBase
}

type PromoterTaxSettings {
  conferenceTradeshow: Boolean
}

input PromoterTaxSettingsInput {
  conferenceTradeshow: Boolean
}

enum PromoterTier {
  CORE
  MAX
  PLUS
  PRO
  WHALE
}

input PromoterWhereInput {
  _or: [PromoterWhereInput]
  accountManagerId: OperatorsIdEqInput
  chargesEnabled: OperatorsBooleanEqInput
  countryCode: OperatorsString
  destinationFallback: OperatorsBooleanEqInput
  dicePartner: OperatorsBooleanEqInput
  id: OperatorsIdEqInput
  name: OperatorsString
  payoutsEnabled: OperatorsBooleanEqInput
  profileActive: OperatorsBooleanEqInput
  stripeRestrictionStatus: OperatorsEnumStripeRestrictionStatus
  typeOfOrganizer: OperatorsEnumTypeOfOrganizer
  verificationDueBy: OperatorsDateInput
}

input PromotersBundles {
  owner: Boolean
  promoterId: ID
}

type PromotersConnection {
  count: Int!
  edges: [PromotersEdge]
  pageInfo: PageInfo!
}

type PromotersEdge {
  cursor: String
  node: Promoter
}

enum PromotersOrder {
  live_events_count_asc
  live_events_count_desc
}

enum PromotionAccessType {
  GENERAL_CODE
  UNIQUE_CODE
}

type PromotionBreakdown {
  appSold: Int!
  eventPromotion: EventPromotion
  faceValue: Int!
  fees: [SalesFees]!
  payoutValue: Int!
  posSold: Int!
  price: Int!
  priceWithPwl: Int
  priceWithoutPwl: Int
  rebate: Int!
  reserved: Int!
  sold: Int!
  terminalSold: Int!
  value: Int!
}

enum PromotionType {
  CODE_LOCK
  DISCOUNT
}

input PublishSeatingChartInput {
  clientMutationId: String!
  seatingChartId: ID!
}

type PublishSeatingChartPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: SeatingChart

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input PutAllEventsOffSaleInput {
  clientMutationId: String!
  promoterId: String!
}

type PutAllEventsOffSalePayload {
  clientMutationId: String!
  promoter: Promoter
}

type Recurrence {
  """Only used if type is MONTHLY_DOW"""
  domRepeat: [DayOfMonth]

  """"Every X" (days/weeks/months)"""
  frequency: Int!

  """
  Unused by DAILY and MONTHLY_DOW types.  Weekly, this is the DOW (0-7) to repeat on, monthly, this is the day-of-the-month (1-31)
  """
  repeatOn: [Int]
  type: RecurrenceType!
}

input RecurrenceInput {
  domRepeat: [DayOfMonthInput]
  frequency: Int!
  repeatOn: [Int]!
  type: RecurrenceType!
}

enum RecurrenceType {
  DAILY
  MONTHLY_DAY
  MONTHLY_DOW
  WEEKLY
}

type RecurrentEventsScheduleConfig {
  frequency: RepeatFrequency
  occurrences: Int
  repeatEnds: RepeatEnds
  repeatOn: RepeatOn
  until: Time
}

input RecurrentEventsScheduleInput {
  frequency: RepeatFrequency
  occurrences: Int
  repeatEnds: RepeatEnds
  repeatOn: RepeatOn
  timezoneName: String
  until: Time
}

input RefundFromDiceInput {
  clientMutationId: String!
  comment: String
  lineItemIds: [Int]!
  purchaseTicketIds: [Int]!
  refundReason: DiceRefundReason!
  revokeMode: RevokeMode!
}

type RefundFromDicePayload {
  clientMutationId: String!
  messages: [String]
  result: String
  successful: Boolean
}

input RefundFromPartnerInput {
  clientMutationId: String!
  comment: String
  eventId: ID!
  lineItemIds: [Int]!
  purchaseTicketIds: [Int]!
  refundReason: PartnerRefundReason!
  revokeMode: RevokeMode!
}

type RefundFromPartnerPayload {
  clientMutationId: String!
  messages: [String]
  result: String
  successful: Boolean
}

input RefundTicketInput {
  clientMutationId: String!
  details: String
  link: String
  markback: Boolean!
  reason: String
  revoke: Boolean!
  ticketIds: [Int]!
}

type RefundTicketPayload {
  clientMutationId: String!
  failed: [Int]
  successful: [Int]
}

type RelatedEvent {
  distance: Float!
  event: Event
}

type RelatedEventViewer {
  getRelated(artistIds: [ID]!, city: String!, eventId: ID): [RelatedEvent]
}

input RemoveFanConnectInput {
  clientMutationId: String!
  id: ID!
}

type RemoveFanConnectPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

enum RepeatEnds {
  OCCURRENCES
  UNTIL
}

enum RepeatFrequency {
  BI_WEEKLY
  DAILY
  MONTHLY
  WEEKLY
}

enum RepeatOn {
  LAST_WEEK_DAY
  SAME_DAY
  SAME_WEEK_AND_DAY
}

union ReportOrSchedule = ReportSchedule | ScheduledReport

"""Report Schedule or Scheduled Report"""
union ReportOrScheduleObject = ReportSchedule | ScheduledReport

type ReportOrScheduleObjectConnection {
  count: Int!
  edges: [ReportOrScheduleObjectEdge]
  pageInfo: PageInfo!
}

type ReportOrScheduleObjectEdge {
  cursor: String
  node: ReportOrScheduleObject
}

type ReportSchedule implements Node {
  archived: Boolean!
  emailList: [EmailAddress]
  endAt: Time
  event: Event

  """Whether any reports have a failed email"""
  hasFailedEmails: Boolean!

  """The ID of an object"""
  id: ID!
  insertedAt: Time
  lastUpdatedBy: User
  locale: String
  name: String!
  options: ScheduledReportOptions
  promoter: Promoter
  recurrence: Recurrence!
  reportType: ReportType!
  scheduledReports(after: String, before: String, filter: DateRangeInput, first: Int, last: Int): ScheduledReportsConnection

  """Count of sent reports for this schedule"""
  sentCount: Int!
  startAt: Time!
  timezoneName: String!
  updatedAt: Time
  user: User
}

enum ReportType {
  BALANCE_REPORT
  EVENT_REPORT
  EXTRA_TRANSACTION_REPORT
  PROMOTER_FANS_DATA_REPORT
  SEATING_REPORT
  TICKET_COUNT_REPORT
}

input ReportsAndSchedulesFilter {
  """Ignored unless user is DICE Staff"""
  accountId: ID
  deliveryDateFrom: Time
  deliveryDateTo: Time
  frequency: [ReportsAndSchedulesFrequency]
  searchTerm: String
  status: [ReportsAndSchedulesStatus]
}

enum ReportsAndSchedulesFrequency {
  ANNUALLY
  DAILY
  MONTHLY
  ONE_OFF
  WEEKDAYS
  WEEKLY
}

enum ReportsAndSchedulesStatus {
  ARCHIVED
  CANCELLED
  FAILED
  SCHEDULED
  SENT
}

input RequestMailchimpCredentialsInput {
  clientMutationId: String!
  code: String!
}

type RequestMailchimpCredentialsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input ResendFanConnectInput {
  clientMutationId: String!
  id: String!
}

type ResendFanConnectPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: FanConnect

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input ResendInviteInstructionsInput {
  accountId: ID!
  clientMutationId: String!
  invitationId: ID!
}

type ResendInviteInstructionsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: AccountUserInvitation

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input ResendTicketsPurchasedNotificationInput {
  clientMutationId: String!
  purchaseId: Int!
}

type ResendTicketsPurchasedNotificationPayload {
  clientMutationId: String!
  messages: [String]
  result: String
  successful: Boolean
}

enum ReservedSeatingTypes {
  assignBestSeat
  selectSeat
}

input ResetDoorlistStatusInput {
  clientMutationId: String!
  id: ID!
}

type ResetDoorlistStatusPayload {
  clientMutationId: String!
  event: Event
}

enum RestrictionKind {
  ALLOW
  DENY
}

input ResumeAllocationInput {
  clientMutationId: String!
  id: ID!
}

type ResumeAllocationPayload {
  clientMutationId: String!
  event: Event
}

input RetryFailedEmailsInput {
  clientMutationId: String!
  id: ID!
}

type RetryFailedEmailsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ScheduledReport

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

type RevenueReportItem {
  faceValue: Int!
  rebate: Int!
  revenue: Int!
  soldTickets: Int!
  ticketTypesBreakdown: [RevenueTicketTypeBreakdown]
  time: Time!
}

type RevenueTicketTypeBreakdown {
  faceValue: Int!
  rebate: Int!
  revenue: Int!
  soldTickets: Int!
  ticketTypeId: ID!
}

enum RevokeMode {
  KEEP_WITH_FAN
  REVOKE_AND_MARKBACK
  REVOKE_AND_PUT_ON_SALE
}

input RevokeUserAccessInput {
  accountId: ID!
  clientMutationId: String!
  userOrInvitationId: ID!
}

type RevokeUserAccessPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: UserAccountObject

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

type RootMutationType {
  archiveFanConnect(input: ArchiveFanConnectInput!): ArchiveFanConnectPayload
  assignProductsToEvents(input: AssignProductsToEventsInput!): AssignProductsToEventsPayload
  boxOfficePayment(input: BoxOfficePaymentInput!): BoxOfficePaymentPayload
  bulkUpdateFanSurveyQuestions(input: BulkUpdateFanSurveyQuestionsInput!): BulkUpdateFanSurveyQuestionsPayload
  cancelReport(input: CancelReportInput!): CancelReportPayload
  changeAllocation(input: ChangeAllocationInput!): ChangeAllocationPayload
  changeDates(input: ChangeDatesInput!): ChangeDatesPayload
  cloneEvent(input: CloneEventInput!): CloneEventPayload
  cloneEventPromotion(input: CloneEventPromotionInput!): CloneEventPromotionPayload
  copySeatingChart(input: CopySeatingChartInput!): CopySeatingChartPayload

  """create an admission config"""
  createAdmissionConfig(input: CreateAdmissionConfigInput!): CreateAdmissionConfigPayload
  createArtist(input: CreateArtistInput!): CreateArtistPayload
  createAttachment(input: CreateAttachmentInput!): CreateAttachmentPayload
  createBundle(input: CreateBundleInput!): CreateBundlePayload
  createCommentActivity(input: CreateCommentActivityInput!): CreateCommentActivityPayload
  createDraftEvent(input: CreateDraftEventInput!): CreateDraftEventPayload
  createEvent(input: CreateEventInput!): CreateEventPayload
  createEventChangedNotification(input: CreateEventChangedNotificationInput!): CreateEventChangedNotificationPayload
  createEventPromotion(input: CreateEventPromotionInput!): CreateEventPromotionPayload
  createEventPromotionCodes(input: CreateEventPromotionCodesInput!): CreateEventPromotionCodesPayload
  createEventSeatingChannel(input: CreateEventSeatingChannelInput!): CreateEventSeatingChannelPayload
  createEventSeatingChart(input: CreateEventSeatingChartInput!): CreateEventSeatingChartPayload
  createFacebookEvent(input: CreateFacebookEventInput!): CreateFacebookEventPayload
  createFanConnect(input: CreateFanConnectInput!): CreateFanConnectPayload
  createFeeConfiguration(input: CreateFeeConfigurationInput!): CreateFeeConfigurationPayload
  createHierarchicalTag(input: CreateHierarchicalTagInput!): CreateHierarchicalTagPayload
  createIntegrationToken(input: CreateIntegrationTokenInput!): CreateIntegrationTokenPayload
  createLabel(input: CreateLabelInput!): CreateLabelPayload
  createLinkout(input: CreateLinkoutInput!): CreateLinkoutPayload
  createManualPayout(input: CreateManualPayoutInput!): CreateManualPayoutPayload
  createMarketeer(input: CreateMarketeerInput!): CreateMarketeerPayload
  createNotificationBatch(input: CreateNotificationBatchInput!): CreateNotificationBatchPayload
  createPayout(input: CreatePayoutInput!): CreatePayoutPayload
  createPermissionProfile(input: CreatePermissionProfileInput!): CreatePermissionProfilePayload
  createProduct(input: CreateProductInput!): CreateProductPayload
  createProductsList(input: CreateProductsListInput!): CreateProductsListPayload
  createPromoter(input: CreatePromoterInput!): CreatePromoterPayload
  createReportSchedule(input: CreateReportScheduleInput!): CreateReportSchedulePayload
  createRestrictedIntegrationToken(input: CreateRestrictedIntegrationTokenInput!): CreateRestrictedIntegrationTokenPayload
  createScheduledReport(input: CreateScheduledReportInput!): CreateScheduledReportPayload
  createSeatingChart(input: CreateSeatingChartInput!): CreateSeatingChartPayload
  createSocialLink(input: CreateSocialLinkInput!): CreateSocialLinkPayload
  createTag(input: CreateTagInput!): CreateTagPayload
  createTempAttachment(input: CreateTempAttachmentInput!): CreateTempAttachmentPayload

  "Mutation to create third party APP settings.\n"
  createThirdPartySettings(input: CreateThirdPartySettingsInput!): CreateThirdPartySettingsPayload
  createTicketPool(input: CreateTicketPoolInput!): CreateTicketPoolPayload
  createUser(input: CreateUserInput!): CreateUserPayload
  createVenue(input: CreateVenueInput!): CreateVenuePayload

  "delete an admission config\nnote: if a user tries to delete a config with existing scanned tickets the server returns `existing_admission_log`.\nplease suggest to the user to disable the config instead.\n"
  deleteAdmissionConfig(input: DeleteAdmissionConfigInput!): DeleteAdmissionConfigPayload
  deleteEventPromotionCodes(input: DeleteEventPromotionCodesInput!): DeleteEventPromotionCodesPayload
  deleteEventSeatingChannel(input: DeleteEventSeatingChannelInput!): DeleteEventSeatingChannelPayload
  deleteFacebookEvent(input: DeleteFacebookEventInput!): DeleteFacebookEventPayload
  deleteIntegrationToken(input: DeleteIntegrationTokenInput!): DeleteIntegrationTokenPayload
  deleteMailchimpSettings(input: DeleteMailchimpSettingsInput!): DeleteMailchimpSettingsPayload
  deleteManualPayout(input: DeleteManualPayoutInput!): DeleteManualPayoutPayload
  deleteNode(input: DeleteNodeInput!): DeleteNodePayload
  deleteReportSchedule(input: DeleteReportScheduleInput!): DeleteReportSchedulePayload
  deleteScheduledReport(input: DeleteScheduledReportInput!): DeleteScheduledReportPayload
  deleteTicketPool(input: DeleteTicketPoolInput!): DeleteTicketPoolPayload
  disableFanSurvey(input: DisableFanSurveyInput!): DisableFanSurveyPayload
  disableSeatingChart(input: DisableSeatingChartInput!): DisableSeatingChartPayload
  doorSalesCharge(input: DoorSalesChargeInput!): DoorSalesChargePayload
  doorSalesCreateReader(input: DoorSalesCreateReaderInput!): DoorSalesCreateReaderPayload
  doorSalesEdit(input: DoorSalesEditInput!): DoorSalesEditPayload
  doorSalesRefund(input: DoorSalesRefundInput!): DoorSalesRefundPayload
  doorSalesReprint(input: DoorSalesReprintInput!): DoorSalesReprintPayload
  doorSalesReserve(input: DoorSalesReserveInput!): DoorSalesReservePayload
  doorSalesSendReceipt(input: DoorSalesSendReceiptInput!): DoorSalesSendReceiptPayload
  doorSalesUnreserve(input: DoorSalesUnreserveInput!): DoorSalesUnreservePayload
  doorSalesValidateFan(input: DoorSalesValidateFanInput!): DoorSalesValidateFanPayload
  dropHoldPayouts(input: DropHoldPayoutsInput!): DropHoldPayoutsPayload
  duplicateEvent(input: DuplicateEventInput!): DuplicateEventPayload
  enableFanSurvey(input: EnableFanSurveyInput!): EnableFanSurveyPayload
  endEventPromotion(input: EndEventPromotionInput!): EndEventPromotionPayload
  finishStripeOnboarding(input: FinishStripeOnboardingInput!): FinishStripeOnboardingPayload
  forceMarkbackAllocation(input: ForceMarkbackAllocationInput!): ForceMarkbackAllocationPayload
  impersonateUser(input: ImpersonateUserInput!): ImpersonateUserPayload
  importPayouts(input: ImportPayoutsInput!): ImportPayoutsPayload
  inviteGuestList(input: InviteGuestListInput!): InviteGuestListPayload
  inviteUser(input: InviteUserInput!): InviteUserPayload
  markBackAllocations(input: MarkBackAllocationsInput!): MarkBackAllocationsPayload
  minorUpdateEvent(input: MinorUpdateEventInput!): MinorUpdateEventPayload
  pauseAllocation(input: PauseAllocationInput!): PauseAllocationPayload
  performPayoutForAccount(input: PerformPayoutForAccountInput!): PerformPayoutForAccountPayload
  publishSeatingChart(input: PublishSeatingChartInput!): PublishSeatingChartPayload
  putAllEventsOffSale(input: PutAllEventsOffSaleInput!): PutAllEventsOffSalePayload
  refundFromDice(input: RefundFromDiceInput!): RefundFromDicePayload
  refundFromPartner(input: RefundFromPartnerInput!): RefundFromPartnerPayload
  refundTicket(input: RefundTicketInput!): RefundTicketPayload @deprecated(reason: "To be removed, use refund_from_partner mutation instead")
  removeFanConnect(input: RemoveFanConnectInput!): RemoveFanConnectPayload
  requestMailchimpCredentials(input: RequestMailchimpCredentialsInput!): RequestMailchimpCredentialsPayload
  resendFanConnect(input: ResendFanConnectInput!): ResendFanConnectPayload
  resendInviteInstructions(input: ResendInviteInstructionsInput!): ResendInviteInstructionsPayload
  resendTicketsPurchasedNotification(input: ResendTicketsPurchasedNotificationInput!): ResendTicketsPurchasedNotificationPayload
  resetDoorlistStatus(input: ResetDoorlistStatusInput!): ResetDoorlistStatusPayload
  resumeAllocation(input: ResumeAllocationInput!): ResumeAllocationPayload
  retryFailedEmails(input: RetryFailedEmailsInput!): RetryFailedEmailsPayload
  revokeUserAccess(input: RevokeUserAccessInput!): RevokeUserAccessPayload

  """save a list of scanned tickets"""
  saveScannedTickets(input: SaveScannedTicketsInput!): SaveScannedTicketsPayload

  """scan a ticket"""
  scanTicket(input: ScanTicketInput!): ScanTicketPayload
  sendReportAt(input: SendReportAtInput!): SendReportAtPayload
  setEventPromotionCodesEnabledState(input: SetEventPromotionCodesEnabledStateInput!): SetEventPromotionCodesEnabledStatePayload
  setHoldPayouts(input: SetHoldPayoutsInput!): SetHoldPayoutsPayload
  submitStripeData(input: SubmitStripeDataInput!): SubmitStripeDataPayload
  syncEvent(input: SyncEventInput!): SyncEventPayload
  syncInventoryWithShopify(input: SyncInventoryWithShopifyInput!): SyncInventoryWithShopifyPayload
  testEventChangedNotification(input: TestEventChangedNotificationInput!): TestEventChangedNotificationPayload
  testFanConnect(input: TestFanConnectInput!): TestFanConnectPayload
  uninviteGuestList(input: UninviteGuestListInput!): UninviteGuestListPayload
  updateAccount(input: UpdateAccountInput!): UpdateAccountPayload
  updateAccountUser(input: UpdateAccountUserInput!): UpdateAccountUserPayload

  """update an admission config"""
  updateAdmissionConfig(input: UpdateAdmissionConfigInput!): UpdateAdmissionConfigPayload
  updateArtist(input: UpdateArtistInput!): UpdateArtistPayload
  updateBundle(input: UpdateBundleInput!): UpdateBundlePayload
  updateDefaultEventTimings(input: UpdateDefaultEventTimingsInput!): UpdateDefaultEventTimingsPayload
  updateDraftEvent(input: UpdateDraftEventInput!): UpdateDraftEventPayload
  updateEvent(input: UpdateEventInput!): UpdateEventPayload
  updateEventPromotion(input: UpdateEventPromotionInput!): UpdateEventPromotionPayload
  updateEventReview(input: UpdateEventReviewInput!): UpdateEventReviewPayload
  updateEventSeatingChannel(input: UpdateEventSeatingChannelInput!): UpdateEventSeatingChannelPayload
  updateEventSeatingChannelObjects(input: UpdateEventSeatingChannelObjectsInput!): UpdateEventSeatingChannelObjectsPayload
  updateEventState(input: UpdateEventStateInput!): UpdateEventStatePayload
  updateFanConnect(input: UpdateFanConnectInput!): UpdateFanConnectPayload
  updateFanSurveyQuestion(input: UpdateFanSurveyQuestionInput!): UpdateFanSurveyQuestionPayload
  updateGuestListEntry(input: UpdateGuestListEntryInput!): UpdateGuestListEntryPayload
  updateHierarchicalTag(input: UpdateHierarchicalTagInput!): UpdateHierarchicalTagPayload
  updateInventoryProducts(input: UpdateInventoryProductsInput!): UpdateInventoryProductsPayload
  updateLabel(input: UpdateLabelInput!): UpdateLabelPayload
  updateLinkout(input: UpdateLinkoutInput!): UpdateLinkoutPayload
  updateMailchimpSettings(input: UpdateMailchimpSettingsInput!): UpdateMailchimpSettingsPayload
  updateMarketeer(input: UpdateMarketeerInput!): UpdateMarketeerPayload
  updatePassword(input: UpdatePasswordInput!): UpdatePasswordPayload
  updatePermissionProfile(input: UpdatePermissionProfileInput!): UpdatePermissionProfilePayload
  updatePreferredLanguage(input: UpdatePreferredLanguageInput!): UpdatePreferredLanguagePayload
  updateProfile(input: UpdateProfileInput!): UpdateProfilePayload
  updatePromoter(input: UpdatePromoterInput!): UpdatePromoterPayload
  updateReportSchedule(input: UpdateReportScheduleInput!): UpdateReportSchedulePayload
  updateScheduledReport(input: UpdateScheduledReportInput!): UpdateScheduledReportPayload
  updateTag(input: UpdateTagInput!): UpdateTagPayload

  "Mutation to update third party APP settings.\n"
  updateThirdPartySettings(input: UpdateThirdPartySettingsInput!): UpdateThirdPartySettingsPayload
  updateTicketPool(input: UpdateTicketPoolInput!): UpdateTicketPoolPayload
  updateUser(input: UpdateUserInput!): UpdateUserPayload
  updateVenue(input: UpdateVenueInput!): UpdateVenuePayload
  uploadEventPromotionCodes(input: UploadEventPromotionCodesInput!): UploadEventPromotionCodesPayload
  upsertArtistDestinationAccount(input: UpsertArtistDestinationAccountInput!): UpsertArtistDestinationAccountPayload
  upsertInventory(input: UpsertInventoryInput!): UpsertInventoryPayload
  validateDraftEvent(input: ValidateDraftEventInput!): ValidateDraftEventPayload
}

type RootQueryType {
  appleMusic: AppleMusicViewer

  """
  Given a list of emails, returns true if any are not part of the account.
  """
  externalEmails(emails: [EmailAddress!]!): Boolean
  featureFlags: FeatureFlags
  node(
    """The ID of an object."""
    id: ID!
  ): Node
  relatedEvents: RelatedEventViewer
  spotify: SpotifyViewer

  """"""
  viewer: Viewer
}

type Sales {
  ticketTypesBreakdown: [TicketTypeBreakdown]!
  totalAppSold: Int!
  totalDigitalValue: Int!
  totalFaceValue(useCache: Boolean): Int!
  totalPayoutValue(useCache: Boolean): Int!
  totalPosSold: Int!
  totalPosValue: Int!
  totalPromoterIncome(useCache: Boolean): Int!
  totalRebate(useCache: Boolean): Int!
  totalRefunded: Int!
  totalRefundedValue: Int!
  totalReserved: Int!
  totalRevoked: Int!
  totalSold(useCache: Boolean): Int!
  totalTerminalSold: Int!
  totalTerminalValue: Int!
  totalValue(useCache: Boolean): Int! @deprecated(reason: "Use total_face_value")
  totalWlIndividuals: Int!
  totalWlRequests: Int!
}

type SalesFees {
  computed: Int!
  keep: Int!
  rebate: Int!
  type: FeeType!
}

enum SalesReportGrouping {
  EVENT
  TICKET_TYPE
  TRANSACTION
}

type SalesforceContract implements Node {
  endDate: Date

  """The ID of an object"""
  id: ID!
  name: String
  num: String
  opportunityDealType: String
  opportunityId: String
  opportunityName: String
  opportunityType: String
  sfAccountId: String
  sfId: String
  startDate: Date
  status: SalesforceContractStatus
}

enum SalesforceContractStatus {
  ACTIVATED
  CANCELLED
  EXPIRED
  SIGNED
  SUPERSEDED
}

type SalesforcePromoterFields {
  defaultContract: SalesforceContract
  ownerEmail: String
  parentSalesforceId: String
  salesforceId: String
}

input SalesforcePromoterFieldsInput {
  defaultContractId: ID
}

input SaveScannedTicketsInput {
  admissionLogs: [AdmissionLogInput]!
  clientMutationId: String!
}

type SaveScannedTicketsPayload {
  clientMutationId: String!
  isValid: Boolean
}

input ScanTicketInput {
  clientMutationId: String!
  code: String!
  configIds: [ID]!
  eventIds: [ID!]!
  scanningUser: String!
}

type ScanTicketPayload {
  """
  the config the successfully scanned the ticket. Return null for the default config
  """
  admissionConfig: AdmissionConfig
  clientMutationId: String!
}

enum ScheduleStatus {
  POSTPONED
  RESCHEDULED
}

type ScheduledReport implements Node {
  archived: Boolean!
  downloadUrl: String
  emailList: [EmailAddress]
  emails: [Email]
  event: Event
  hasFailedEmails: Boolean!

  """The ID of an object"""
  id: ID!
  insertedAt: Time
  lastUpdatedBy: User
  locale: String
  name: String!
  options: ScheduledReportOptions
  promoter: Promoter
  reportSchedule: ReportSchedule
  reportType: ReportType!
  scheduledAt: Time!
  status: ScheduledReportStatus!
  updatedAt: Time
  user: User
  uuid: String!
}

type ScheduledReportOptions {
  accountId: ID
  accountIds: [ID]
  countryCode: String
  endDate: Time
  eventIds: [ID]
  fanCountries: [String]
  fields: Int
  hideTaxId: Boolean
  legalEntity: String
  marketeerId: ID
  showPayoutSummary: Boolean
  showProducts: Boolean
  showTickets: Boolean
  startDate: Time
}

input ScheduledReportOptionsInput {
  """The name of the object type currently being queried."""
  
  accountId: ID
  accountIds: [ID]
  countryCode: String
  endDate: Time
  eventIds: [ID]
  fanCountries: [String]
  fields: Int
  hideTaxId: Boolean
  legalEntity: String
  marketeerId: ID
  showPayoutSummary: Boolean
  showProducts: Boolean
  showTickets: Boolean
  startDate: Time
}

enum ScheduledReportStatus {
  CANCELLED
  DONE
  FAILED
  SCHEDULED
}

type ScheduledReportsConnection {
  edges: [ScheduledReportsEdge]
  pageInfo: PageInfo!
}

type ScheduledReportsEdge {
  cursor: String
  node: ScheduledReport
}

input SeatCategoriesInput {
  id: ID
  name: String
  seatsIoKey: String
  value: Int
}

type SeatCategory {
  id: ID
  name: String!
  seatsIoKey: String
  ticketTypeId: Int
  value: Int!
}

type SeatLabel {
  entrance: String
  own: SeatLabelType
  parent: SeatLabelType
  section: String
}

type SeatLabelType {
  label: String
  type: String
}

type SeatingAreaConfig {
  capacity: Int!
  seatingArea: String!
}

input SeatingAreaConfigInput {
  capacity: Int!
  seatingArea: String!
}

type SeatingChannel {
  channelType: ChannelType!
  name: String
  seatsIoChannel: String!
}

input SeatingChannelInput {
  channelType: ChannelType!
  id: ID
  name: String
  seatsIoChannel: String!
}

type SeatingChart implements Node {
  chartManagerCredentials: ChartManagerCredentials!
  disabled: Boolean!

  """The ID of an object"""
  id: ID!
  inUse: Boolean!
  name: String!
  published: Boolean!
  seatsIoChart: SeatsIoChart
}

type SeatingChartConnection {
  edges: [SeatingChartEdge]
  pageInfo: PageInfo!
}

type SeatingChartEdge {
  cursor: String
  node: SeatingChart
}

type SeatsIoChannel {
  color: String!
  key: String!
  name: String!
}

type SeatsIoChannelWithObjects {
  color: String!
  key: String!
  name: String!
  objects: [String]!
}

type SeatsIoChart {
  key: String
  socialDistancingRulesets: [SeatsIoSocialDistancingRuleset]
}

enum SeatsIoChartType {
  MIXED
  ROWS_WITH_SECTIONS
}

type SeatsIoEvent {
  channels: [SeatsIoChannelWithObjects]
  chartKey: String
  id: Int
  key: String
  socialDistancingRulesetKey: String
}

enum SeatsIoEventReportBy {
  BY_CATEGORY_KEY
  BY_CATEGORY_LABEL
  BY_CHANNEL
  BY_LABEL
  BY_OBJECT_TYPE
  BY_ORDER_ID
  BY_SECTION
  BY_SELECTABILITY
  BY_STATUS
}

type SeatsIoSocialDistancingRuleset {
  key: String
  name: String
}

type SellingPoint {
  name: String!
  order: Int!
  product: Product!
}

input SellingPointInput {
  name: String!
}

input SendReportAtInput {
  clientMutationId: String!
  id: ID!
  scheduledAt: Time!
}

type SendReportAtPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ScheduledReport

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input SetEventPromotionCodesEnabledStateInput {
  allCodes: Boolean
  clientMutationId: String!
  codes: [String]
  enabledState: Boolean!
  filter: CodeLockFilter
  id: ID!
}

type SetEventPromotionCodesEnabledStatePayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventPromotion

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input SetHoldPayoutsInput {
  clientMutationId: String!
  id: ID!
}

type SetHoldPayoutsPayload {
  clientMutationId: String!
  event: Event
}

type ShopifyStore implements Node {
  adminApiTokenIssued: Boolean!

  """The ID of an object"""
  id: ID!
  shopifyOauthLink: String
  storeName: String
}

input ShopifyStoreInput {
  storeName: String
}

enum ShowArtistDescription {
  CUSTOM
  DICE
  NONE
}

type SocialLink {
  artist: Artist
  campaign: String!
  channel: String!
  customUrl: String
  deelsParams: String
  diceLink: Boolean
  event: Event
  id: ID
  insertedAt: Time!
  postType: SocialLinkPostType!
  promoter: Promoter
  ticketTypes: [TicketType]

  """e.g. https://link.dice.fm/AE6xPxPd7jb"""
  url: String
  venue: Venue
}

enum SocialLinkPostType {
  organic
  paid
  unknown
}

type SpotifyArtistObject {
  externalUrls: SpotifyExternalUrlObject
  href: String!
  id: String!
  images: [SpotifyImagesObject]
  name: String!
  uri: String!
}

type SpotifyArtistsResponse {
  href: String!
  items: [SpotifyArtistObject]!
}

type SpotifyExternalUrlObject {
  spotify: String!
}

type SpotifyImagesObject {
  height: Int
  url: String!
  width: Int
}

type SpotifySearchResponse {
  artists: SpotifyArtistsResponse
  tracks: SpotifyTracksResponse
}

enum SpotifySearchTypes {
  album
  artist
  playlist
  track
}

type SpotifyTrackObject {
  artists: [SpotifyArtistObject]
  externalUrls: SpotifyExternalUrlObject
  href: String!
  id: String!
  name: String!
  previewUrl: String
  uri: String!
}

type SpotifyTracksResponse {
  href: String!
  items: [SpotifyTrackObject]!
}

type SpotifyViewer {
  search(limit: Int, offset: Int, q: String!, type: [SpotifySearchTypes]): SpotifySearchResponse
}

type StreamDetails {
  castServerUrl: String
  castStreamKey: String
  muxStreamId: String
  playbackUrl: String
  streamOnline: Boolean
}

type StreamStats {
  avgWatchTime: Int
  completionRatio: Float
  exitsBeforeVideoStart: Float
  peakTime: Time
  peakViewers: Int
  streamEnd: Time
  streamStart: Time
  totalViewers: Int
  videoStartupTime: Float
  viewersPerDeviceType: [StreamViewersBreakdownItem]
  viewersPerOs: [StreamViewersBreakdownItem]
  viewersTime: [StreamViewersTimeItem]
}

enum StreamType {
  LIVE
  REWATCH
}

type StreamViewersBreakdownItem {
  avgWatchTime: Int
  label: String
  viewers: Int
}

type StreamViewersTimeItem {
  time: Time
  viewers: Int
}

enum StreamingUrlState {
  STREAM_EMBED_CODE_BLANK
  STREAM_URL_BLANK
  STREAM_URL_READY
}

enum StripeAccountIntegrityState {
  CHARGES_DISABLED
  OK
  PAYOUTS_DISABLED
}

type StripeAccrual {
  balanceBatchId: Int
  balanceChange: Int!
  currency: String!
  destinationAccount: String!
  region: String!
  scheduledAt: Time
}

type StripeOperations {
  balanceBatches: [BalanceBatch]!
  finTrans: [FinTran]!
}

type StripePaymentIntent {
  amount: Int!
  currency: String!
  region: String!
  sid: String
}

type StripeReader {
  deviceType: String
  id: String
  label: String
  location: String
  serialNumber: String
  status: String
}

type StripeRefund {
  amount: Int!
  failureReason: String
  sid: String
  status: String
}

enum StripeRestrictionStatus {
  """This value only appears in the context of a filter"""
  ANY_PROBLEM
  CHARGES_ONLY
  FULLY_RESTRICTED

  """This value only appears in the context of a filter"""
  LONG_UNVERIFIED
  OK
  PAYOUTS_ONLY
  SOON_RESTRICTED
}

type StripeTransfer {
  executedAt: Time
  lastError: Map
  sid: String
}

type StripeTransferReversal {
  amount: Int!
  executedAt: Time
  lastError: Map
  sid: String
  stripeTransfer: StripeTransfer
}

type Subject {
  actions: [Action]
  name: String
}

input SubjectInput {
  actions: [ActionInput]
  name: String
}

input SubmitStripeDataInput {
  accountCountry: String
  accountCurrency: EventCostCurrency
  accountNumber: String
  accountRouting: String
  additionalCurrencies: [AdditionalCurrencyInput]
  address: String
  businessName: String
  businessTaxId: String
  city: String
  clientMutationId: String!
  dob: String
  firstName: String
  lastName: String
  legalEntityType: LegalEntityType
  passportFileBlob: String
  personalAddressAddress: String
  personalAddressCity: String
  personalAddressPostalCode: String
  personalState: String
  postalCode: String
  promoterId: ID!
  ssnLast4: String
  state: String
}

type SubmitStripeDataPayload {
  clientMutationId: String!
  promoter: Promoter
}

input SyncEventInput {
  clientMutationId: String!
  id: ID!
}

type SyncEventPayload {
  clientMutationId: String!
  event: Event
}

input SyncInventoryWithShopifyInput {
  clientMutationId: String!
  inventoryId: ID
}

type SyncInventoryWithShopifyPayload {
  clientMutationId: String!
  inventory: Inventory!
}

type Tag implements Name & Node {
  createdAt: Time!
  deprecated: Boolean!

  """The ID of an object"""
  id: ID!
  name: String
  updatedAt: Time!
}

enum TagOrder {
  NAME
}

input TagWhereInput {
  _or: [TagWhereInput]
  deprecated: OperatorsBooleanEqInput
  name: OperatorsString
}

type TagsConnection {
  count: Int!
  edges: [TagsEdge]
  pageInfo: PageInfo!
}

type TagsEdge {
  cursor: String
  node: Tag
}

enum TargetingMode {
  country
  location
  worldwide
}

type TaxRate {
  faceValueRate: Float
  feesRates: FeesRates
}

type TaxSettings {
  clubNight: Boolean
  franceMainstream: Boolean
}

input TaxSettingsInput {
  clubNight: Boolean
  franceMainstream: Boolean
}

type TempAttachment {
  storagePath: String!
}

input TestEventChangedNotificationInput {
  changes: [String]
  changeset: EventChangedNotificationChangeset!
  clientMutationId: String!
  eventId: ID!
  message: String
  sendMeACopy: Boolean
}

type TestEventChangedNotificationPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input TestFanConnectInput {
  clientMutationId: String!
  eventId: ID!
  message: String!
  ticketTypeIds: [ID]!
  title: String!
}

type TestFanConnectPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

type ThinUserProfile {
  email: String
  firstName: String
  lastName: String
}

type ThirdPartySettings implements Node {
  appIcon: String!
  appLink: String!
  appName: String!

  """The ID of an object"""
  id: ID!
  idVerification: Boolean!
  promoterDisplayName: String
  promoterId: ID
  provideSecureUserAuth: Boolean!
}

type TicketHolder {
  holderFirstName: String
  holderLastName: String
}

type TicketPool implements Node {
  archived: Boolean
  holds: [Hold]

  """The ID of an object"""
  id: ID!
  maxAllocation: Int
  name: String
  ticketTypes: [TicketType]
  unlimitedAllocation: Boolean
}

type TicketType implements FaceValueInterface & Fees & Node & PriceBreakdownInterface {
  activateCodeDateOffset: Int
  additionalPaymentMethods: [PaymentMethods]
  allocation: Int!
  allowSeatChange: Boolean
  announceDate: Time
  archived: Boolean
  area: VenueArea
  attractivePriceType: String
  attractiveSeatingAreaType: String
  attractiveTaxFree: Boolean @deprecated(reason: "Legacy")
  codeLocked: Boolean!
  currentPriceTier: PriceTier
  description: String
  doorSalesEnabled: Boolean!
  doorSalesPrice: Int
  doorSalesPriceCurrent: Int
  doorSalesPriceTaxed: Int
  doorSalesStats: DoorSalesStats
  doorSalesTax: Int
  endDate: Time
  eventId: Int
  eventPromotions: [EventPromotion]
  externalSkus: [String]
  faceValue: Int!
  fees: [Fee]
  hidden: Boolean!
  icon: String

  """The ID of an object"""
  id: ID!
  increment: Int
  isStream: Boolean!
  maximumIncrements: Int
  name: String
  offSaleDate: Time
  onSaleDate: Time
  onSaleNotificationSentAt: Time
  onSaleNotificationStatus: Boolean!
  order: Int!
  presale: Boolean!
  price: Int
  priceBreakdown: Price
  priceGrade: String!
  priceHidden: Boolean!
  priceTierType: PriceTierTypes
  priceTiers: [PriceTier]
  products: [Product]
  rebate: Int
  requiresAddress: Boolean
  requiresOtherTypeIds: [ID]
  reservedForGuestList: Boolean!
  reservedSeating: Boolean
  reservedSeatingType: ReservedSeatingTypes
  salesLimit: Int
  seatCategories: [SeatCategory]
  seatmapUrl: String
  startDate: Time
  streamEmbedCode: String
  streamLink: String
  ticketPoolId: String
  venueScheduleId: ID
}

type TicketTypeBreakdown {
  fees: [SalesFees]!
  priceTiersBreakdown: [PriceTierBreakdown]!
  promotionsBreakdown: [PromotionBreakdown]!
  ticketHoldersCount: Int!
  ticketType: TicketType!
  ticketTypeId: ID!
  totalAppSold: Int!
  totalDigitalValue: Int!
  totalFaceValue: Int!
  totalPayoutValue: Int!
  totalPosSold: Int!
  totalPosValue: Int!
  totalRebate: Int!
  totalRefundRequested: Int!
  totalRefundTicketsRequested: Int!
  totalRefundValueRequested: Int!
  totalReserved: Int!
  totalSold: Int!
  totalTerminalSold: Int!
  totalTerminalValue: Int!
  totalWlPurchases: Int!
  totalWlRequests: Int!
}

input TicketTypesInput {
  activateCodeDateOffset: Int
  additionalPaymentMethods: [PaymentMethods]
  allocation: Int
  allowSeatChange: Boolean
  announceDate: Time
  archived: Boolean
  area: CreateOrUpdateVenueAreaInput
  attractivePriceType: String
  attractiveSeatingAreaType: String
  attractiveTaxFree: Boolean
  codeLocked: Boolean
  description: String
  doorSalesEnabled: Boolean
  doorSalesPrice: Int
  endDate: Time
  externalSkus: [String]
  faceValue: Int
  fees: [FeeInput]
  hidden: Boolean
  icon: String
  id: ID
  increment: Int
  isStream: Boolean
  maximumIncrements: Int
  name: String
  offSaleDate: Time
  onSaleDate: Time
  presale: Boolean
  priceGrade: String
  priceHidden: Boolean
  priceTierType: PriceTierTypes
  priceTiers: [PriceTiersInput]
  productIds: [ID]
  requiresAddress: Boolean
  requiresOtherTypeIds: [ID]
  reservedForGuestList: Boolean
  reservedSeating: Boolean
  reservedSeatingType: ReservedSeatingTypes
  salesLimit: Int
  seatCategories: [SeatCategoriesInput]
  seatmapUrl: String
  startDate: Time
  streamEmbedCode: String
  streamLink: String
  ticketPoolId: String
  venueScheduleId: ID
  venueScheduleIndex: Int
}

"""ISOz time"""
scalar Time

interface TimeStamps {
  createdAt: Time
  updatedAt: Time
}

"""timeframe options for admission reports"""
enum Timeframe {
  FIFTEEN_MINUTES
  FIVE_MINUTES
  MINUTE
  THIRTY_MINUTES
}

type TokensList {
  allCustomerDataToken: String
  c1FormExportToken: String
  c2FormExportToken: String
  codeLocksExportToken: String
  doorSalesExportToken: String
  doorlistExportToken: String
  eventReportExportToken: String
  extraTransactionReportExportToken: String
  fanSurveyExportToken: String
  fullAttendeeListToken: String
  groupedSalesReportTicketExportToken: String
  groupedSalesReportTransactionExportToken: String
  ntsWhitelistExportToken: String
  refundRequestReportCsvExportToken: String
  salesReportExportToken: String
  seatingReportCsvExportToken: String
  streamingSalesReportCsvExportToken: String @deprecated(reason: "Replaced by grouped_sales_report_(transaction/ticket)_export_token")
  ticketCountReportExportToken: String
  vatReportExportToken: String
}

type TopEventContainer {
  event: Event!
}

enum TopEventsOrder {
  REVENUE
  SAVES
  TICKETS_SOLD
  WAITING_LIST
}

enum TvPlatform {
  DICE
  EXTERNAL
}

input UninviteGuestListInput {
  clientMutationId: String!
  ids: [ID]
}

type UninviteGuestListPayload {
  clientMutationId: String!
  failed: [ID]
  successful: [ID]
}

input UpdateAccountInput {
  addressCountry: String
  clientMutationId: String!
  facebookPage: FacebookPageInput
  facebookUserAccessToken: String
  hasEuEvents: Boolean
  hasUsEvents: Boolean
  id: ID!
  stripeSetupInitiated: Boolean
  taxForm: Upload
  vatNumber: String
  vatNumberProvided: Boolean
}

type UpdateAccountPayload {
  account: Account
  clientMutationId: String!
}

input UpdateAccountUserInput {
  accountId: ID!
  clientMutationId: String!
  email: String
  permissionProfileId: ID
  userId: ID!
}

type UpdateAccountUserPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: User

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateAdmissionConfigInput {
  clientMutationId: String!
  description: String
  enabled: Boolean
  fromShift: Int
  id: ID!
  name: String
  note: String
  recurringLabels: [String]
  ticketTypeIds: [ID]
  toShift: Int
  uniqueness: ConfigUniqueness
}

type UpdateAdmissionConfigPayload {
  admissionConfig: AdmissionConfig
  clientMutationId: String!
}

input UpdateArtistInput {
  backendArtistIds: [String]
  clientMutationId: String!
  description: String
  disambiguation: String
  hierarchicalTagIds: [ID]
  id: ID!
  links: [LinkInput]
  media: [MediaItemInputObject]
  musicbrainzId: String
  name: String
  performerType: PerformerType
  profileImageAttachmentId: ID
  profileImageCropRegion: CropRegionInput
  tagIds: [ID]
}

type UpdateArtistPayload {
  artist: Artist
  clientMutationId: String!
}

input UpdateBundleInput {
  cityId: String
  clientMutationId: String!
  curatedByPromoter: CuratedByPromoterInput
  curatedByVenue: CuratedByVenueInput
  eventIds: [ID]
  expiryDate: Date
  fromDate: Date
  hidden: Boolean!
  id: ID!
  maxPrice: Int
  minPrice: Int
  name: String!
  profileDetails: ProfileDetailsInput
  promotersBundles: [PromotersBundles]
  toDate: Date
}

type UpdateBundlePayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Bundle

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateDefaultEventTimingsInput {
  clientMutationId: String!
  defaultEventTimings: [EventTimingInput]
  promoterId: ID!
}

type UpdateDefaultEventTimingsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Promoter

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateDraftEventInput {
  """The name of the object type currently being queried."""
  
  additionalArtists: [AdditionalArtistInput]
  additionalInfos: [EventAdditionalInfoInput]
  addressCapacity: Int
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressSiaeCode: String
  addressState: String
  ageLimit: String
  announceDate: Time
  artistIds: [ID]
  artists: [EventArtistInput]
  attractiveFields: AttractiveFieldsInput
  barcodeType: String
  basePriceFees: [String]
  billingNotes: String
  billingPromoterId: ID
  bundleIds: [String]
  characteristicIds: [ID]
  charityEvent: Boolean
  charityId: String
  checklists: [ChecklistInput]
  cityIds: [String]
  clientMutationId: String!
  closeEventDate: Time
  colour: ColourInput
  completedSteps: Int
  costAmount: Int
  costCurrency: String
  countryCode: String
  date: Time
  description: String
  diceStatusNotes: String
  diceStreamDuration: Int
  diceStreamDvrEnabled: Boolean
  diceStreamRewatchEnabledUntil: Time
  diceTv: Boolean
  diceTvPlatform: TvPlatform
  disableUsTax: Boolean
  doorlistAdditionalRecipients: [String]
  doorlistRecipientIds: [ID]
  endDate: Time
  eventIdLive: String
  eventImages: [EventImageInput]
  eventLoadPredictions: [EventLoadPredictionInput]
  eventPromoters: [EventPromoter]
  eventRules: EventRulesInput
  eventSeatingChartId: ID
  eventSharingObjects: [EventSharingObjectInput]
  eventType: EventType
  eventVenues: [EventVenues]
  extraNotes: String
  fanFacingPromoterIds: [ID]
  fanSupportNotes: FanSupportNotesInput
  faqs: [FaqInput]
  featuredAreas: [FeaturedAreaInput]
  fees: [FeeInput]
  feesBehaviour: FeesBehaviour
  flags: EventFlagsInput
  freeEvent: Boolean
  fullAddress: String
  hierarchicalTagIds: [ID]
  id: ID!
  isTest: Boolean
  isTicketAvailableAtDoor: Boolean
  labelIds: [ID]
  latitude: Float
  licenseNumber: String
  lineup: [LineupInput]
  links: [LinkInput]
  lockVersion: Int
  longitude: Float
  manualValidationEnabled: Boolean
  marketeerIds: [ID]
  maxTicketsLimit: Int
  media: [MediaItemInputObject]
  musicbrainzArtists: [MusicbrainzArtists]
  name: String
  notes: String
  offSaleDate: Time
  onSaleDate: Time
  onSaleNotification: Boolean
  onSaleNotificationAt: Time
  onSaleNotificationSmsContent: String
  overriddenPromoterName: String
  overrideFees: Boolean
  permName: String
  platformAccountCode: PlatformAccountCode
  postFanPriceFees: [String]
  postOfficeBoxNumber: String
  postalCode: String
  presentedBy: String
  printedTicketFormat: PrintedTicketFormat
  products: [ProductInput]
  promoterIds: [ID]
  promoterStatusNotes: String
  pwlWindow: Int
  readAccessEmails: [String]
  recurrentEventSchedule: RecurrentEventsScheduleInput
  relatedEventIds: [ID]
  requiresBoxOfficeTicketNomination: Boolean
  requiresTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  salesforceContractId: ID
  scheduleStatus: ScheduleStatus
  seatingChannels: [SeatingChannelInput]
  sendReceiptViaSms: Boolean
  showArtistDescription: ShowArtistDescription
  socialDistancingRulesetKey: String
  stages: [String]
  streetAddress: String
  stripeAccountId: String
  tagIds: [ID]
  taxSettings: TaxSettingsInput
  thirdPartySettingsId: ID
  ticketPools: [AttachTicketPoolInput]
  ticketType: String
  ticketTypes: [TicketTypesInput]
  timezoneName: String
  totalTickets: Int
  venue: String
  venueConfigurationId: ID
  venueIds: [ID]
  venueName: String
  venueSchedules: [VenueScheduleInput]
  venueSpaceId: ID
  waitingListExchangeWindows: [WaitingListExchangeWindowInput]
}

type UpdateDraftEventPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Event

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateEventInput {
  """The name of the object type currently being queried."""
  
  additionalArtists: [AdditionalArtistInput]
  additionalInfos: [EventAdditionalInfoInput]
  addressCapacity: Int
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressSiaeCode: String
  addressState: String
  ageLimit: String
  announceDate: Time
  artistIds: [ID]
  artists: [EventArtistInput]
  attractiveFields: AttractiveFieldsInput
  barcodeType: String
  basePriceFees: [String]
  billingNotes: String
  billingPromoterId: ID
  bundleIds: [String]
  characteristicIds: [ID]
  charityEvent: Boolean
  charityId: String
  checklists: [ChecklistInput]
  cityIds: [String]
  clientMutationId: String!
  closeEventDate: Time
  colour: ColourInput
  completedSteps: Int
  costAmount: Int
  costCurrency: String
  countryCode: String
  date: Time
  description: String
  diceStatusNotes: String
  diceStreamDuration: Int
  diceStreamDvrEnabled: Boolean
  diceStreamRewatchEnabledUntil: Time
  diceTv: Boolean
  diceTvPlatform: TvPlatform
  disableUsTax: Boolean
  doorlistAdditionalRecipients: [String]
  doorlistRecipientIds: [ID]
  endDate: Time
  eventIdLive: String
  eventImages: [EventImageInput]
  eventLoadPredictions: [EventLoadPredictionInput]
  eventPromoters: [EventPromoter]
  eventRules: EventRulesInput
  eventSeatingChartId: ID
  eventSharingObjects: [EventSharingObjectInput]
  eventType: EventType
  eventVenues: [EventVenues]
  extraNotes: String
  fanFacingPromoterIds: [ID]
  fanSupportNotes: FanSupportNotesInput
  faqs: [FaqInput]
  featuredAreas: [FeaturedAreaInput]
  fees: [FeeInput]
  feesBehaviour: FeesBehaviour
  flags: EventFlagsInput
  freeEvent: Boolean
  fullAddress: String
  hierarchicalTagIds: [ID]
  id: ID!
  isTest: Boolean
  isTicketAvailableAtDoor: Boolean
  labelIds: [ID]
  latitude: Float
  licenseNumber: String
  lineup: [LineupInput]
  links: [LinkInput]
  lockVersion: Int
  longitude: Float
  manualValidationEnabled: Boolean
  marketeerIds: [ID]
  maxTicketsLimit: Int
  media: [MediaItemInputObject]
  musicbrainzArtists: [MusicbrainzArtists]
  name: String
  notes: String
  offSaleDate: Time
  onSaleDate: Time
  onSaleNotification: Boolean
  onSaleNotificationAt: Time
  onSaleNotificationSmsContent: String
  overriddenPromoterName: String
  overrideFees: Boolean
  permName: String
  platformAccountCode: PlatformAccountCode
  postFanPriceFees: [String]
  postOfficeBoxNumber: String
  postalCode: String
  presentedBy: String
  printedTicketFormat: PrintedTicketFormat
  products: [ProductInput]
  promoterIds: [ID]
  promoterStatusNotes: String
  pwlWindow: Int
  readAccessEmails: [String]
  recurrentEventSchedule: RecurrentEventsScheduleInput
  relatedEventIds: [ID]
  requiresBoxOfficeTicketNomination: Boolean
  requiresTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  salesforceContractId: ID
  scheduleStatus: ScheduleStatus
  seatingChannels: [SeatingChannelInput]
  sendReceiptViaSms: Boolean
  showArtistDescription: ShowArtistDescription
  socialDistancingRulesetKey: String
  stages: [String]
  streetAddress: String
  stripeAccountId: String
  tagIds: [ID]
  taxSettings: TaxSettingsInput
  thirdPartySettingsId: ID
  ticketPools: [AttachTicketPoolInput]
  ticketType: String
  ticketTypes: [TicketTypesInput]
  timezoneName: String
  totalTickets: Int
  venue: String
  venueConfigurationId: ID
  venueIds: [ID]
  venueName: String
  venueSchedules: [VenueScheduleInput]
  venueSpaceId: ID
  waitingListExchangeWindows: [WaitingListExchangeWindowInput]
}

type UpdateEventPayload {
  clientMutationId: String!
  event: Event
  warnings: [String]
}

input UpdateEventPromotionInput {
  clientMutationId: String!
  code: String
  codesOperations: [CodesOperationsInput]
  discount: Int
  endDate: Time
  fanFacingName: String
  id: ID!
  maxRedemptions: Int
  name: String
  seatsIoChannel: String
  startDate: Time
  ticketTypeIds: [ID]
  unlockAfterExpired: Boolean
}

type UpdateEventPromotionPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventPromotion

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateEventReviewInput {
  assigneeId: ID
  clientMutationId: String!
  eventId: ID!
  priority: Boolean
  status: EventReviewStatus
}

type UpdateEventReviewPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventReview

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateEventSeatingChannelInput {
  clientMutationId: String!
  color: String!
  eventPromotionId: ID
  key: ID!
  name: String!
  objects: [String]
}

input UpdateEventSeatingChannelObjectsInput {
  clientMutationId: String!
  key: ID!
  objectsToAdd: [String]
  objectsToRemove: [String]
}

type UpdateEventSeatingChannelObjectsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: SeatsIoChannelWithObjects

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

type UpdateEventSeatingChannelPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: SeatsIoChannel

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateEventStateInput {
  clientMutationId: String!
  id: ID!
  notify: Boolean
  notifyOn: Time
  state: EventState!
}

type UpdateEventStatePayload {
  clientMutationId: String!
  event: Event
}

input UpdateFanConnectInput {
  clientMutationId: String!
  eventId: ID!
  id: ID!
  message: String!
  onBehalfOfUserId: ID
  scheduledAt: Time
  sendMeACopy: Boolean
  ticketTypeIds: [ID]!
  title: String!
}

type UpdateFanConnectPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateFanSurveyQuestionInput {
  clientMutationId: String!
  eventId: ID!
  fanQuestionInput: FanQuestionInput
}

type UpdateFanSurveyQuestionPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Event

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateGuestListEntryInput {
  clientMutationId: String!
  email: EmailAddress
  id: ID!
  quantity: Int!
  ticketTypeId: ID!
}

type UpdateGuestListEntryPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: GuestListEntry

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateHierarchicalTagInput {
  clientMutationId: String!
  deprecated: Boolean
  id: ID
  name: String!
  parentId: String!
}

type UpdateHierarchicalTagPayload {
  clientMutationId: String!
  hierarchicalTag: HierarchicalTag
}

input UpdateInventoryProductsInput {
  clientMutationId: String!
  inventoryId: ID!
  products: [InventoryProductInput]!
}

type UpdateInventoryProductsPayload {
  clientMutationId: String!
  inventory: Inventory
}

input UpdateLabelInput {
  checklists: [ChecklistInput]
  clientMutationId: String!
  description: String!
  id: ID!
  name: String!
}

type UpdateLabelPayload {
  clientMutationId: String!
  label: Label
}

input UpdateLinkoutInput {
  artistId: ID
  cityIds: [String]
  clientMutationId: String!
  currency: String
  date: Time!
  destinationEventId: ID
  endDate: Time!
  externalUrl: String
  id: ID!
  imageAttachmentId: ID
  imageCropRegion: CropRegionInput
  linkoutType: LinkoutType!
  name: String!
  offSaleDate: Time
  onSaleDate: Time
  price: Int
  promoterId: ID
  tagIds: [ID]
  timezone: String!
  venueId: ID
}

type UpdateLinkoutPayload {
  clientMutationId: String!
  linkout: Linkout
}

input UpdateMailchimpSettingsInput {
  clientMutationId: String!
  listId: String!
  syncOnlyOptIn: Boolean!
}

type UpdateMailchimpSettingsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Viewer

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateMarketeerInput {
  appOptInEnabled: Boolean
  clientMutationId: String!
  contacts: [ContactsInput]
  fbAccessToken: String
  fbPixelId: String
  gaTrackingId: String
  googleAdsConversionId: String
  googleAdsPurchaseConversionLabel: String
  id: ID!
  name: String
  privacyPolicyLink: String
  tiktokPixelId: String
  twitterCheckoutInitiatedPixelId: String
  twitterPixelId: String
  twitterPurchasePixelId: String
  webOptInEnabled: Boolean
}

type UpdateMarketeerPayload {
  clientMutationId: String!
  marketeer: Marketeer
}

input UpdatePasswordInput {
  clientMutationId: String!
  newPassword: String
  password: String
}

type UpdatePasswordPayload {
  clientMutationId: String!
  viewer: Viewer
}

input UpdatePermissionProfileInput {
  caption: String
  clientMutationId: String!
  id: ID!
  subjects: [SubjectInput]
}

type UpdatePermissionProfilePayload {
  clientMutationId: String!
  permissionProfile: PermissionProfile
}

input UpdatePreferredLanguageInput {
  clientMutationId: String!
  preferredLanguage: Language
}

type UpdatePreferredLanguagePayload {
  clientMutationId: String!
  viewer: Viewer
}

input UpdateProfileInput {
  clientMutationId: String!
  email: String
  firstName: String
  lastName: String
  mioRedesignV2: Boolean
  preferredLanguage: Language
  timezoneName: String
}

type UpdateProfilePayload {
  clientMutationId: String!
  viewer: Viewer
}

input UpdatePromoterInput {
  accountIban: String
  accountManagerId: ID
  accountManagerIds: [ID]
  accountName: String
  accountNumber: String
  accountSortCode: String
  accountType: String
  accountVatNumber: String
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressState: String
  allowSkipReview: Boolean
  apiToken: String
  apiTokenExpiryDate: Time
  associatedMarketeerIds: [ID]
  autoRescheduledEventRefunds: AutoRescheduledEventRefundsInput
  automaticRollingPaymentsConfiguration: AutomaticRollingPaymentsConfigurationInput
  bankAddress: String
  bankName: String
  billingNotes: String
  charityTaxFree: Boolean
  clientMutationId: String!
  clientSuccessManagerIds: [ID]
  contacts: [ContactsInput]
  coolingOffPeriod: Boolean
  coolingOffPeriodHours: Int
  countryCode: String
  curatedBundle: CuratedBundleInput
  dicePartner: Boolean
  disableUsTax: Boolean
  disabledReason: EnumAccountDisabledReason
  displayName: String
  eventDefaults: EventDefaultsInput
  extrasEnabled: Boolean
  fanSupportNotes: FanSupportNotesInput
  fees: [FeeInput]
  forbidSelfPayouts: Boolean
  fullAddress: String
  holdPayouts: Boolean
  id: ID!
  isDisabled: Boolean
  isTest: Boolean
  labelIds: [ID]
  latitude: Float
  legalEntity: String
  licenseNumber: String
  links: [LinkInput]
  longitude: Float
  merchEnabled: Boolean
  name: String
  notes: String
  permissionProfileOverrides: [PermissionProfileOverrideInput]
  postOfficeBoxNumber: String
  postalCode: String
  profileActive: Boolean
  profileDetails: ProfileDetailsInput
  promoterTaxSettings: PromoterTaxSettingsInput
  qflowEnabled: Boolean
  resoldEnabled: Boolean
  routingNumber: String
  salesforcePromoterFields: SalesforcePromoterFieldsInput
  sendReceiptViaSms: Boolean
  showPriceBreakdown: Boolean
  statusNotes: String
  streetAddress: String
  stripeAccountId: String
  stripeDocumentId: String
  stripeFallbackAccountId: String
  stripeFallbackPlatformCode: PlatformAccountCode
  stripeLocationId: String
  stripeVerified: Boolean
  swiftCode: String
  tagIds: [ID]
  taxCode: String
  ticketAgreementComplete: Boolean
  timezoneName: String
  typeOfOrganizer: EnumTypeOfOrganizer
}

type UpdatePromoterPayload {
  clientMutationId: String!
  promoter: Promoter
}

input UpdateReportScheduleInput {
  clientMutationId: String!
  emailList: [EmailAddress]
  endAt: Time
  id: ID!
  locale: String
  name: String
  options: ScheduledReportOptionsInput
  recurrence: RecurrenceInput
  startAt: Time
  timezoneName: String
}

type UpdateReportSchedulePayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ReportSchedule

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateScheduledReportInput {
  clientMutationId: String!
  emailList: [EmailAddress]
  id: ID!
  locale: String
  name: String
  options: ScheduledReportOptionsInput
  scheduledAt: Time
}

type UpdateScheduledReportPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ScheduledReport

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateTagInput {
  clientMutationId: String!
  deprecated: Boolean
  id: ID
  name: String!
}

type UpdateTagPayload {
  clientMutationId: String!
  tag: Tag
}

input UpdateThirdPartySettingsInput {
  appIcon: String!
  appLink: String!
  appName: String!
  clientMutationId: String!
  id: ID!
  idVerification: Boolean!
  promoterDisplayName: String
  promoterId: ID
  provideSecureUserAuth: Boolean!
}

type UpdateThirdPartySettingsPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: ThirdPartySettings

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateTicketPoolInput {
  archived: Boolean
  clientMutationId: String!
  holds: [HoldInput]
  id: ID!
  maxAllocation: Int
  name: String
  ticketTypes: [TicketTypesInput]
  unlimitedAllocation: Boolean
}

type UpdateTicketPoolPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: TicketPool

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpdateUserInput {
  clientMutationId: String!
  email: String
  firstName: String
  id: ID
  lastName: String
  mioRedesignV2: Boolean
  mongoUserId: String
  name: String
  notificationPermissions: NotificationPermissionsInput
  permissionProfileId: ID
  phone: String
  position: String
  preferredLanguage: Language
}

type UpdateUserPayload {
  clientMutationId: String!
  user: User
}

input UpdateVenueInput {
  accessControl: String
  accessibilityLink: String
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressState: String
  ageLimit: String
  allocatedSeating: String
  areas: [CreateOrUpdateVenueAreaInput]
  associatedPromoterIds: [ID]
  attractiveEnabled: Boolean
  attractiveRoomSiaeCode: String
  attributes: [String]
  barcodeType: String
  capacity: Int
  characteristicIds: [ID]
  cityId: String
  clientMutationId: String!
  configurations: [CreateOrUpdateVenueConfigurationInput]
  contactEmail: String
  contactPhone: String
  contacts: [ContactsInput]
  countryCode: String
  curatedBundle: CuratedBundleInput
  dicePartner: Boolean
  externalLinks: ExternalLinksInput
  facebookPageId: String
  faqs: [FaqInput]
  fees: [FeeInput]
  flags: VenueFlagsInput
  fullAddress: String
  hideCapacity: Boolean
  id: ID!
  isSecret: Boolean
  isTest: Boolean
  labelIds: [ID]
  latitude: Float
  links: [LinkInput]
  logoAttachmentId: ID
  logoCropRegion: CropRegionInput
  longitude: Float
  mobileTicketsEnabled: Boolean
  multizone: Boolean
  name: String
  notes: String
  postOfficeBoxNumber: String
  postalCode: String
  profileActive: Boolean
  profileDetails: ProfileDetailsInput
  promoterAllocation: Int
  qflowEnabled: Boolean
  scannerModel: String
  scannerType: String
  streetAddress: String
  ticketType: String
  ticketValidation: String
  ticketingPartner: String
  tier: VenueTier
  timezoneName: String
  type: VenueType
  venueAllocation: Int
  venueImages: [VenueImageInput]
  venueOwnerIds: [ID]
  venueSpaces: [VenueSpaceInput]
}

type UpdateVenuePayload {
  clientMutationId: String!
  venue: Venue
}

"Represents an uploaded file.\n"
scalar Upload

input UploadEventPromotionCodesInput {
  clientMutationId: String!
  filename: String!
  force: Boolean
  id: ID!
}

type UploadEventPromotionCodesPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: EventPromotion

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

input UpsertArtistDestinationAccountInput {
  artistId: ID!
  clientMutationId: String!
  id: ID
  region: String
}

type UpsertArtistDestinationAccountPayload {
  clientMutationId: String!
  destinationAccount: DestinationAccount
}

input UpsertInventoryInput {
  artistId: ID!
  clientMutationId: String!
  shopifyStore: ShopifyStoreInput
}

type UpsertInventoryPayload {
  clientMutationId: String!
  inventory: Inventory
}

type User implements Name & Node {
  accountUsers: [AccountUser]
  detailedRemittanceRecipient: Boolean!
  doorlistRecipient: Boolean!
  email: String
  firstName: String
  hasQflowAccount: Boolean!

  """The ID of an object"""
  id: ID!
  lastName: String
  loginEnabled: Boolean!
  mioRedesignV2: Boolean
  mongoUserId: String
  name: String
  notificationPermissions: NotificationPermissions
  pamUser: Boolean
  permissionProfile: PermissionProfile
  phone: String
  position: String
  preferredLanguage: Language
  promoters: [Promoter]
  remittanceRecipient: Boolean!
  suggestedMongoUserIds: [String]
  timezoneName: String
  venues: [Venue]
}

"""Account user or invitation request"""
union UserAccountObject = AccountUserInvitation | User

type UserAccountObjectConnection {
  edges: [UserAccountObjectEdge]
  pageInfo: PageInfo!
}

type UserAccountObjectEdge {
  cursor: String
  node: UserAccountObject
}

enum UserSource {
  KIM
  SELF_SERVICE
  SELF_SERVICE_V2
}

type UsersConnection {
  count: Int!
  edges: [UsersEdge]
  pageInfo: PageInfo!
}

type UsersEdge {
  cursor: String
  node: User
}

input UsersWhereInput {
  _or: [UsersWhereInput]
  email: OperatorsString
  name: OperatorsString
}

scalar Uuid

input ValidateDraftEventInput {
  """The name of the object type currently being queried."""
  
  additionalArtists: [AdditionalArtistInput]
  additionalInfos: [EventAdditionalInfoInput]
  addressCapacity: Int
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressSiaeCode: String
  addressState: String
  ageLimit: String
  announceDate: Time
  artistIds: [ID]
  artists: [EventArtistInput]
  attractiveFields: AttractiveFieldsInput
  barcodeType: String
  basePriceFees: [String]
  billingNotes: String
  billingPromoterId: ID
  bundleIds: [String]
  characteristicIds: [ID]
  charityEvent: Boolean
  charityId: String
  checklists: [ChecklistInput]
  cityIds: [String]
  clientMutationId: String!
  closeEventDate: Time
  colour: ColourInput
  completedSteps: Int
  costAmount: Int
  costCurrency: String
  countryCode: String
  date: Time
  description: String
  diceStatusNotes: String
  diceStreamDuration: Int
  diceStreamDvrEnabled: Boolean
  diceStreamRewatchEnabledUntil: Time
  diceTv: Boolean
  diceTvPlatform: TvPlatform
  disableUsTax: Boolean
  doorlistAdditionalRecipients: [String]
  doorlistRecipientIds: [ID]
  endDate: Time
  eventIdLive: String
  eventImages: [EventImageInput]
  eventLoadPredictions: [EventLoadPredictionInput]
  eventPromoters: [EventPromoter]
  eventRules: EventRulesInput
  eventSeatingChartId: ID
  eventSharingObjects: [EventSharingObjectInput]
  eventType: EventType
  eventVenues: [EventVenues]
  extraNotes: String
  fanFacingPromoterIds: [ID]
  fanSupportNotes: FanSupportNotesInput
  faqs: [FaqInput]
  featuredAreas: [FeaturedAreaInput]
  fees: [FeeInput]
  feesBehaviour: FeesBehaviour
  flags: EventFlagsInput
  freeEvent: Boolean
  fullAddress: String
  hierarchicalTagIds: [ID]
  id: ID!
  isTest: Boolean
  isTicketAvailableAtDoor: Boolean
  labelIds: [ID]
  latitude: Float
  licenseNumber: String
  lineup: [LineupInput]
  links: [LinkInput]
  lockVersion: Int
  longitude: Float
  manualValidationEnabled: Boolean
  marketeerIds: [ID]
  maxTicketsLimit: Int
  media: [MediaItemInputObject]
  musicbrainzArtists: [MusicbrainzArtists]
  name: String
  notes: String
  offSaleDate: Time
  onSaleDate: Time
  onSaleNotification: Boolean
  onSaleNotificationAt: Time
  onSaleNotificationSmsContent: String
  overriddenPromoterName: String
  overrideFees: Boolean
  permName: String
  platformAccountCode: PlatformAccountCode
  postFanPriceFees: [String]
  postOfficeBoxNumber: String
  postalCode: String
  presentedBy: String
  printedTicketFormat: PrintedTicketFormat
  products: [ProductInput]
  promoterIds: [ID]
  promoterStatusNotes: String
  pwlWindow: Int
  readAccessEmails: [String]
  recurrentEventSchedule: RecurrentEventsScheduleInput
  relatedEventIds: [ID]
  requiresBoxOfficeTicketNomination: Boolean
  requiresTicketNomination: Boolean
  restrictCountries: [CountryCode]
  restrictCountriesKind: RestrictionKind
  salesforceContractId: ID
  scheduleStatus: ScheduleStatus
  seatingChannels: [SeatingChannelInput]
  sendReceiptViaSms: Boolean
  showArtistDescription: ShowArtistDescription
  socialDistancingRulesetKey: String
  stages: [String]
  streetAddress: String
  stripeAccountId: String
  tagIds: [ID]
  taxSettings: TaxSettingsInput
  thirdPartySettingsId: ID
  ticketPools: [AttachTicketPoolInput]
  ticketType: String
  ticketTypes: [TicketTypesInput]
  timezoneName: String
  totalTickets: Int
  venue: String
  venueConfigurationId: ID
  venueIds: [ID]
  venueName: String
  venueSchedules: [VenueScheduleInput]
  venueSpaceId: ID
  waitingListExchangeWindows: [WaitingListExchangeWindowInput]
}

type ValidateDraftEventPayload {
  clientMutationId: String!

  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: Event

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

"  Validation messages are returned when mutation input does not meet the requirements.\n  While client-side validation is highly recommended to provide the best User Experience,\n  All inputs will always be validated server-side.\n\n  Some examples of validations are:\n\n  * Username must be at least 10 characters\n  * Email field does not contain an email address\n  * Birth Date is required\n\n  While GraphQL has support for required values, mutation data fields are always\n  set to optional in our API. This allows 'required field' messages\n  to be returned in the same manner as other validations. The only exceptions\n  are id fields, which may be required to perform updates or deletes.\n"
type ValidationMessage {
  """A unique error code for the type of validation used."""
  code: String!

  "The input field that the error applies to. The field can be used to\nidentify which field the error message should be displayed next to in the\npresentation layer.\n\nIf there are multiple errors to display for a field, multiple validation\nmessages will be in the result.\n\nThis field may be null in cases where an error cannot be applied to a specific field.\n"
  field: String

  "A friendly error message, appropriate for display to the end user.\n\nThe message is interpolated to include the appropriate variables.\n\nExample: `Username must be at least 10 characters`\n\nThis message may change without notice, so we do not recommend you match against the text.\nInstead, use the *code* field for matching.\n"
  message: String

  """A list of substitutions to be applied to a validation message template"""
  options: [ValidationOption]

  "A template used to generate the error message, with placeholders for option substiution.\n\nExample: `Username must be at least {count} characters`\n\nThis message may change without notice, so we do not recommend you match against the text.\nInstead, use the *code* field for matching.\n"
  template: String
}

type ValidationOption {
  """
  The name of a variable to be subsituted in a validation message template
  """
  key: String!

  """
  The value of a variable to be substituted in a validation message template
  """
  value: String!
}

type Variant implements Node {
  allocation: Int!

  """The ID of an object"""
  id: ID!
  name: String!
  product: Product!
  size: String @deprecated(reason: "Deprecated, use name instead")
  sku: String
}

type VariantBreakdownItem {
  totalAppSold: Int!
  totalDigitalValue: Int!
  totalFaceValue: Int!
  totalSold: Int!
  variant: Variant
  variantId: ID!
}

input VariantInput {
  allocation: Int!
  id: ID
  name: String!
  size: String
  sku: String
}

type Venue implements Contracts & Fees & Location & Name & Node {
  accessControl: String!
  accessibilityLink: String
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressState: String
  ageLimit: String
  allocatedSeating: String!
  allowedForSubmission: Boolean!
  areas: [VenueArea]
  associatedPromoters: [Promoter]
  attractiveEnabled: Boolean
  attractiveRoomSiaeCode: String
  attributes: [String]
  barcodeType: String!
  bundle: Bundle
  capacity: Int!
  characteristics: [Characteristic]
  cityId: String
  configurations: [VenueConfiguration]
  contactEmail: String
  contactPhone: String
  contacts: [User]
  contracts: [FeesConfiguration]
  countryCode: String
  createdAt: Time
  curatedBundle: Bundle
  dicePartner: Boolean
  externalLinks: ExternalLinks!
  facebookPageId: String
  faqs: [Faq]
  fees: [Fee]
  flags: VenueFlags!
  fullAddress: String
  hideCapacity: Boolean!

  """The ID of an object"""
  id: ID!
  isSecret: Boolean
  isTest: Boolean
  labels: [Label]
  latitude: Float
  links: [Map]! @deprecated(reason: "Use externalLinks instead")
  logoAttachment: Attachment
  logoCropRegion: CropRegion
  longitude: Float
  mobileTicketsEnabled: Boolean!
  multizone: Boolean!
  name: String
  notes: String!
  permName: String
  postOfficeBoxNumber: String
  postalCode: String
  profileActive: Boolean
  profileDetails: ProfileDetails
  promoterAllocation: Int!
  qflowEnabled: Boolean
  scannerModel: String!
  scannerType: String!
  seatingCharts(after: String, before: String, first: Int, last: Int): SeatingChartConnection
  streetAddress: String
  ticketType: String!
  ticketValidation: String!
  ticketingPartner: String!
  tier: VenueTier
  timezoneName: String
  type: VenueType
  updatedAt: Time
  venueAllocation: Int!
  venueImages: [VenueImage]
  venueOwners: [Promoter]
  venueSpaces: [VenueSpace]
}

interface VenueAddress {
  addressCapacity: Int
  addressCountry: String
  addressLocality: String
  addressRegion: String
  addressSiaeCode: String
  addressState: String
  countryCode: String
  fullAddress: String
  latitude: Float
  longitude: Float
  postOfficeBoxNumber: String
  postalCode: String
  streetAddress: String
  timezoneName: String
}

type VenueArea implements Node {
  code: String!

  """The ID of an object"""
  id: ID!
  name: String!
}

type VenueConfiguration implements Node {
  attractiveRoomSiaeCode: String
  capacity: Int!

  """The ID of an object"""
  id: ID!
  name: String!
  seatingAreaConfigs: [SeatingAreaConfig]!
}

type VenueFlags {
  doorListRequired: Map!
}

input VenueFlagsInput {
  doorListRequired: FlagValue
}

type VenueImage implements Node {
  attachment: Attachment

  """The ID of an object"""
  id: ID!
}

input VenueImageInput {
  attachmentId: ID
  id: ID
}

type VenueSchedule implements Node {
  date: Time
  endDate: Time

  """The ID of an object"""
  id: ID!
  name: String
  venueConfigurationId: ID
  venueId: ID
}

input VenueScheduleInput {
  date: Time
  endDate: Time
  id: ID
  name: String
  venueConfigurationId: ID
  venueId: ID
}

type VenueSpace implements Node {
  """The ID of an object"""
  id: ID!
  name: String!
}

input VenueSpaceInput {
  id: ID
  name: String!
}

enum VenueTier {
  TIER_1
  TIER_2
  TIER_3
}

enum VenueType {
  arena
  bar
  church
  cinema
  club
  comedy_club
  multiroom_club
  pub
  railway_arch
  underground
}

input VenueWhereInput {
  _or: [VenueWhereInput]
  id: OperatorsIdEqInput
  name: OperatorsString
  profileActive: OperatorsBooleanEqInput
}

type VenuesConnection {
  count: Int!
  edges: [VenuesEdge]
  pageInfo: PageInfo!
}

type VenuesEdge {
  cursor: String
  node: Venue
}

type Viewer implements Node {
  account: Account
  activities(after: String, before: String, first: Int, last: Int, where: ActivityWhereInput): ActivitiesConnection

  """get all admission_tickets for an event"""
  admissionTickets(after: String, before: String, eventId: ID!, fanFilter: FanFilterInput, first: Int, last: Int): AdmissionTicketsConnection
  aggregatedSales(currency: EventCostCurrency, endDate: Time, startDate: Time): AggregatedSalesReport
  artists(after: String, before: String, first: Int, last: Int, searchTerm: String, where: ArtistWhereInput): ArtistConnection
  availableAccounts: [AccountAvailability]!
  availableEntities: [AssociatedEntities]

  """get average scans per timeframe"""
  avgScanPerTimeframe(eventId: ID!, timeframe: Timeframe!): Map
  balanceReportExportToken(accountIds: [ID], countryCode: String, endDate: Time!, legalEntity: String, startDate: Time!): String!

  """get basic report"""
  baseReport(eventId: ID!): BaseReport
  bundles(after: String, before: String, first: Int, last: Int, orderBy: [BundleOrder], where: BundleWhereInput): BundleConnection
  characteristics(after: String, before: String, first: Int, last: Int, where: CharacteristicWhereInput): CharacteristicConnection
  chargebacksReportExportToken(accountIds: [ID], endDate: Time, startDate: Time): String!
  cities(includeGlobal: Boolean, includeHidden: Boolean, where: CitiesWhereInput): [City]
  config: Config
  defaultDoorlistRecipients(venueIds: [ID!]): [String]
  dicePartner: Boolean!
  diceStaff: Boolean!
  doorSalesExports(after: String, before: String, endDate: Date, first: Int, last: Int, startDate: Date): DoorSalesExportConnection
  email: String
  eventPromotionPriceCalculation(discount: Int!, eventId: ID!, ticketTypeIds: [ID]): [EventPromotionTicketTypePrices]
  eventReviews(after: String, before: String, first: Int, last: Int, orderBy: [EventsConnectionOrder], searchTerm: String, where: EventReviewWhereInput): EventReviewConnection
  events(after: String, before: String, first: Int, last: Int, orderBy: [EventsConnectionOrder], scopes: EventScopesInput, searchTerm: String, where: EventWhereInput): EventConnection
  eventsCurrencies: [EventCostCurrency]!
  externalEntities(entityType: String, integrationType: String): [ExternalEntity]
  extrasRevenueReport(currency: EventCostCurrency, endDate: Time, eventId: ID, productId: ID, startDate: Time): [ExtrasRevenueReportItem] @deprecated(reason: "Please use products_revenue_report instead")
  faceValueSalesAttribution(from: Date!, promoterId: ID!, to: Date!): Map
  fanActivities(after: String, before: String, fanId: ID!, first: Int, last: Int, where: FanActivityWhereInput): FanActivityConnection
  fanConnects(after: String, before: String, first: Int, last: Int, orderBy: [FanConnectOrder], searchTerm: String, where: FanConnectWhereInput): FanConnectConnection
  fans(after: String, before: String, first: Int, last: Int, searchTerm: String, where: FanWhereInput): FanConnection
  featuredAreaEstimates(lat: Float!, lng: Float!, radius: Float!): [FeaturedAreaEstimate]
  feeConfigurationByPromoter(eventId: ID, promoterId: ID!): FeesConfiguration
  firstName: String
  hierarchicalTags(after: String, before: String, first: Int, last: Int, orderBy: [HierarchicalTagOrder], where: HierarchicalTagWhereInput): HierarchicalTagsConnection

  """The ID of an object"""
  id: ID!
  impersonator: User
  labels(after: String, before: String, first: Int, last: Int, where: LabelWhereInput): LabelsConnection
  lastName: String
  linkouts(after: String, before: String, first: Int, last: Int, searchTerm: String): LinkoutConnection
  manualPayoutExportToken(endDate: Time!, fallbackAccountId: String!, startDate: Time!): String
  marketeers(after: String, before: String, first: Int, last: Int, where: MarketeerWhereInput): MarketeersConnection
  mioRedesignV2: Boolean!
  musicbrainzArtists(filterKimArtists: Boolean, limit: Int, query: String!): [MusicbrainzArtist]
  name: String
  payouts(after: String, before: String, first: Int, last: Int, orderBy: [PayoutsOrder], searchTerm: String, where: PayoutsWhereInput): PayoutConnection
  payoutsExportToken(accountIds: [ID], endDate: Time, kind: PayoutsExportKind!, startDate: Time): String
  permissionProfileStructure(externalOnly: Boolean): PermissionProfileStructure
  permissionProfiles: [PermissionProfile]
  permissions: [String]
  preferredLanguage: Language
  priceCalculation(basePriceFees: [String] = [], billingPromoterId: ID, disableUsTax: Boolean = false, eventId: ID, faceValue: Int!, fees: [FeeInput], forcePwlActive: Boolean, ignorePwlFee: Boolean = false, postFanPriceFees: [String] = [], venueIds: [ID] = []): Price
  productCategories: [Category]
  productPriceCalculation(basePriceFees: [String] = [], billingPromoterId: ID, categoryId: ID!, disableUsTax: Boolean = false, eventId: ID, faceValue: Int!, fees: [FeeInput], postFanPriceFees: [String] = [], venueId: ID): Price
  productsRevenueReport(currency: EventCostCurrency, endDate: Time, eventId: ID, productId: ID, rootType: ProductRootType, startDate: Time): [ProductsRevenueReportItem]
  promoterFansDataExportToken(accountId: ID): String!
  promoters(after: String, before: String, first: Int, last: Int, orderBy: PromotersOrder, searchTerm: String, where: PromoterWhereInput): PromotersConnection
  recurrentEventsSchedule(announceDate: Time!, date: Time!, endDate: Time!, frequency: RepeatFrequency!, occurrences: Int, offSaleDate: Time!, onSaleDate: Time!, repeatEnds: RepeatEnds!, repeatOn: RepeatOn, timezoneName: String, until: Time): [EventSchedule]
  reportsAndSchedules(after: String, before: String, filters: ReportsAndSchedulesFilter, first: Int, last: Int): ReportOrScheduleObjectConnection
  revenueReport(currency: EventCostCurrency, endDate: Time, eventId: ID, startDate: Time, ticketTypeId: ID): [RevenueReportItem]
  salesReportExportToken(accountIds: [ID], endDate: Time!, groupBy: SalesReportGrouping!, startDate: Time!): String!
  salesTaxCalculation(amount: Int!, eventId: ID!, venueId: ID!): Int
  searchAttractiveTickets(after: String, barcode: String, before: String, cardNumber: String, emissionId: Int, eventDate: Time, eventName: String, first: Int, fiscalDateMax: Time, fiscalDateMin: Time, fiscalSeal: String, last: Int, progressiveNumber: String): AttractiveTicketConnection
  source: UserSource

  """get status breakdown"""
  statusBreakdown(eventId: ID!): Map

  """get status breakdown per ticket type"""
  statusBreakdownPerTty(eventId: ID!): Map
  stripeOperations(q: String!): StripeOperations
  switchAccount(accountId: ID!): String
  tags(after: String, before: String, first: Int, last: Int, orderBy: [TagOrder], where: TagWhereInput): TagsConnection

  "List all Third Party Settings created for the given promoter ID.\n"
  thirdPartySettings(promoterId: ID!): [ThirdPartySettings]
  ticketCountSalesAttribution(from: Date!, promoterId: ID!, to: Date!): Map
  timezoneName: String
  topEvents(limit: Int!, orderBy: TopEventsOrder!): [TopEventContainer]

  """get the total scans per timeframe"""
  totalScanPerTimeframe(eventId: ID!, fromDate: Time!, timeframe: Timeframe!, toDate: Time!): Map

  """get scans per user"""
  totalScanPerUser(eventId: ID!): Map
  underMaintenance: Boolean!
  users(after: String, before: String, first: Int, last: Int, where: UsersWhereInput): UsersConnection
  venues(after: String, before: String, first: Int, last: Int, searchTerm: String, where: VenueWhereInput): VenuesConnection
}

type WaitingListEntry implements Node {
  amount: Int!
  expiresAt: Time
  fan: Fan

  """The ID of an object"""
  id: ID!
  joinedAt: Time
  price: Int!
  reservedAt: Time
  status: String
  ticketType: TicketType
}

type WaitingListExchangeWindow {
  duration: Int!
  id: ID
  offset: Int
}

input WaitingListExchangeWindowInput {
  duration: Int!
  id: ID
  offset: Int
}