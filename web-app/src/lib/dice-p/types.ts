// src/lib/dice-p/types.ts

// Fee object inside tickets
export interface Fee {
    active: boolean | null
    amount: number
    applicable: boolean | null
    type: string
    unit: string
  }
  
  // Breakdown entry inside priceBreakdown
  export interface PriceBreakdownEntry {
    active: boolean
    amount: number
    applicable: boolean
    computed: number
    type: string
    unit: string
  }
  
  // Main PriceBreakdown
  export interface PriceBreakdown {
    faceValue: number
    fees: number
    friendlyFaceValue: string | null
    friendlyPrice: string | null
    salesTax: number
    total: number
    totalWithPwl: number | null
    totalWithoutPwl: number | null
    vatAmount: number
    vatRate: number
    breakdown?: PriceBreakdownEntry[]
  }
  
  // Price tier for ticket types
  export interface PriceTier {
    allocation: number
    attractivePriceType: string | null
    doorSalesPrice: number
    doorSalesPriceTaxed: number
    faceValue: number
    fees: Fee[]
    id: string
    name: string
    order: number
    price: number
    rebate: number
    ticketTypeId: number
    time: string | null
    priceBreakdown: PriceBreakdown
  }
  
  // Detailed ticket type
  export interface TicketTypeDetail {
    faceValue: number
    activateCodeDateOffset: number
    id: string
    startDate: string | null
    attractiveSeatingAreaType: string | null
    doorSalesPriceCurrent: number
    externalSkus: string[]
    endDate: string | null
    announceDate: string | null
    priceBreakdown: PriceBreakdown
    seatmapUrl: string | null
    onSaleNotificationStatus: boolean
    fees: Fee[]
    icon: string | null
    allowSeatChange: boolean
    isStream: boolean
    ticketPoolId: string | null
    reservedSeatingType: string | null
    priceTiers: PriceTier[]
    archived: boolean
    additionalPaymentMethods: string[]
    order: number
    streamEmbedCode: string | null
    doorSalesTax: number
    allocation: number
    presale: boolean
    attractiveTaxFree: boolean
    currentPriceTier: PriceTier | null
    maximumIncrements: number
    priceHidden: boolean
    streamLink: string | null
    eventId: number
    offSaleDate: string | null
    priceTierType: string | null
    salesLimit: number | null
    price: number
    doorSalesPriceTaxed: number
    onSaleNotificationSentAt: string | null
    doorSalesPrice: number
    hidden: boolean
    rebate: number
    doorSalesEnabled: boolean
    onSaleDate: string | null
    attractivePriceType: string | null
    description: string | null
    requiresAddress: boolean
    venueScheduleId: string | null
    codeLocked: boolean
    increment: number
    name: string
    reservedSeating: boolean
    reservedForGuestList: boolean
    priceGrade: string | null
    requiresOtherTypeIds: string[]
  }
  
  // Event node returned
  export interface EventNode {
    ticketType: string
    ticketTypes: TicketTypeDetail[]
    name: string
  }
  
  // Full GraphQL response
  export interface GetEventWithTicketTypesResponse {
    viewer: {
      events: {
        edges: Array<{ node: EventNode }>  
      }
      id: string
    }
  }
  