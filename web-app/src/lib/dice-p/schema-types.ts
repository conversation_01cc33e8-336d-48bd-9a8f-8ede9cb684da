import { GraphQLClient, RequestOptions } from 'graphql-request';
import gql from 'graphql-tag';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
type GraphQLClientRequestHeaders = RequestOptions['requestHeaders'];
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  CumulativePurchasesAsJson: { input: any; output: any; }
  /** ISO 8601 Date - YYYY-MM-DD */
  Date: { input: any; output: any; }
  /**
   * Email Address : represent a addr-spec in `local-part@domain` format.
   *       It can parse mailbox format `display name <address>` but it only returns the address part.
   */
  EmailAddress: { input: any; output: any; }
  Json: { input: any; output: any; }
  Map: { input: any; output: any; }
  /** Phone Number */
  PhoneNumber: { input: any; output: any; }
  /** ISOz time */
  Time: { input: any; output: any; }
  /**
   * Represents an uploaded file.
   *
   */
  Upload: { input: any; output: any; }
  Uuid: { input: any; output: any; }
};

export type Account = Node & {
  __typename?: 'Account';
  accountUsers?: Maybe<UserAccountObjectConnection>;
  addressCountry: Scalars['String']['output'];
  allowSkipReview: Scalars['Boolean']['output'];
  allowedAdhocFees?: Maybe<Array<Maybe<Fee>>>;
  allowedForFiscalDataChanges: Scalars['Boolean']['output'];
  automaticRollingPaymentsEnabled?: Maybe<Scalars['Boolean']['output']>;
  availableEventsForPayout?: Maybe<Array<Maybe<Event>>>;
  billingAccountDueDate?: Maybe<Scalars['Time']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  disabledReason?: Maybe<EnumAccountDisabledReason>;
  displayName?: Maybe<Scalars['String']['output']>;
  eventsApiToken?: Maybe<EventsApiToken>;
  extrasEnabled?: Maybe<Scalars['Boolean']['output']>;
  facebookPage?: Maybe<FacebookPage>;
  financialDataProvided: Scalars['Boolean']['output'];
  forbidSelfPayouts?: Maybe<Scalars['Boolean']['output']>;
  hasEuEvents?: Maybe<Scalars['Boolean']['output']>;
  hasUsEvents?: Maybe<Scalars['Boolean']['output']>;
  holdPayouts?: Maybe<Scalars['Boolean']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  integrationTokens?: Maybe<Array<Maybe<IntegrationToken>>>;
  isDisabled?: Maybe<Scalars['Boolean']['output']>;
  licenseNumber?: Maybe<Scalars['String']['output']>;
  mailchimp?: Maybe<Mailchimp>;
  merchEnabled?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
  stripeAccountId?: Maybe<Scalars['String']['output']>;
  stripeAccountState: StripeAccountIntegrityState;
  stripeAccountType?: Maybe<Scalars['String']['output']>;
  stripeLoginUrl?: Maybe<Scalars['String']['output']>;
  stripeOauthUrl: Scalars['String']['output'];
  taxFormProvided: Scalars['Boolean']['output'];
  typeOfOrganizer?: Maybe<EnumTypeOfOrganizer>;
  vatNumber?: Maybe<Scalars['String']['output']>;
  vatNumberProvided: Scalars['Boolean']['output'];
};


export type AccountAccountUsersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  permissionProfileIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  term?: InputMaybe<Scalars['String']['input']>;
};


export type AccountAllowedAdhocFeesArgs = {
  eventId?: InputMaybe<Scalars['ID']['input']>;
};


export type AccountStripeOauthUrlArgs = {
  app?: InputMaybe<Scalars['String']['input']>;
};

export type AccountAvailability = {
  __typename?: 'AccountAvailability';
  account: Account;
  disabledReason: EnumAccountDisabledReason;
  isDisabled: Scalars['Boolean']['output'];
  membershipType: AccountMembershipType;
  permissionProfile: PermissionProfile;
};

export type AccountBalance = {
  __typename?: 'AccountBalance';
  accountId: Scalars['String']['output'];
  available: Scalars['Map']['output'];
  pending: Scalars['Map']['output'];
};

export enum AccountMembershipType {
  COLLABORATORS = 'COLLABORATORS',
  MEMBER = 'MEMBER'
}

export type AccountUser = {
  __typename?: 'AccountUser';
  account?: Maybe<Promoter>;
  current: Scalars['Boolean']['output'];
  permissionProfile?: Maybe<PermissionProfile>;
};

export type AccountUserInvitation = Node & {
  __typename?: 'AccountUserInvitation';
  email: Scalars['String']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  invitedBy?: Maybe<User>;
  permissionProfile?: Maybe<PermissionProfile>;
  status?: Maybe<EnumAccountUserInvitaionStatus>;
  user?: Maybe<User>;
};

export type Action = {
  __typename?: 'Action';
  category?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type ActionInput = {
  name?: InputMaybe<Scalars['String']['input']>;
};

export type ActivitiesConnection = {
  __typename?: 'ActivitiesConnection';
  edges?: Maybe<Array<Maybe<ActivitiesEdge>>>;
  pageInfo: PageInfo;
};

export type ActivitiesEdge = {
  __typename?: 'ActivitiesEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Activity>;
};

export type Activity = Node & {
  __typename?: 'Activity';
  date?: Maybe<Scalars['Time']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  itemId: Scalars['Int']['output'];
  itemType: ActivityItemType;
  legacy?: Maybe<Scalars['Boolean']['output']>;
  onBehalfOfUser?: Maybe<PamUser>;
  payload?: Maybe<Scalars['Map']['output']>;
  type: ActivityType;
  user?: Maybe<PamUser>;
};

export enum ActivityItemType {
  Artist = 'Artist',
  Event = 'Event',
  Label = 'Label',
  PermissionProfile = 'PermissionProfile',
  Promoter = 'Promoter',
  TicketType = 'TicketType',
  Venue = 'Venue',
  VenueArea = 'VenueArea'
}

export enum ActivityType {
  change = 'change',
  comment = 'comment',
  creation = 'creation',
  event_changed_notification = 'event_changed_notification',
  markback = 'markback',
  synchronisation = 'synchronisation',
  tier_closure = 'tier_closure'
}

export type ActivityWhereInput = {
  date?: InputMaybe<OperatorsDateInput>;
  itemId?: InputMaybe<Scalars['ID']['input']>;
  itemType?: InputMaybe<OperatorsString>;
  legacy?: InputMaybe<OperatorsBooleanEqInput>;
  onlyImpersonated?: InputMaybe<Scalars['Boolean']['input']>;
  type?: InputMaybe<OperatorsString>;
};

export type AdditionalArtist = {
  __typename?: 'AdditionalArtist';
  description?: Maybe<Scalars['String']['output']>;
  hierarchicalTags?: Maybe<Array<Maybe<HierarchicalTag>>>;
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
};

export type AdditionalArtistInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type AdditionalCurrencyInput = {
  currency?: InputMaybe<EventCostCurrency>;
  token?: InputMaybe<Scalars['String']['input']>;
};

export type AdjustmentsStats = {
  __typename?: 'AdjustmentsStats';
  completed: Scalars['Int']['output'];
  failed: Scalars['Int']['output'];
  notProcessed: Scalars['Int']['output'];
};

/** admission config */
export type AdmissionConfig = Node & {
  __typename?: 'AdmissionConfig';
  description?: Maybe<Scalars['String']['output']>;
  enabled?: Maybe<Scalars['Boolean']['output']>;
  fromShift: Scalars['Int']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  note?: Maybe<Scalars['String']['output']>;
  recurringLabels?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  toShift: Scalars['Int']['output'];
  uniqueness: ConfigUniqueness;
};

/** admission log: use to sync scanned tickets with server and other scanners */
export type AdmissionLogInput = {
  configId?: InputMaybe<Scalars['ID']['input']>;
  scannedAt: Scalars['Time']['input'];
  scanningUser: Scalars['String']['input'];
  source: Scalars['String']['input'];
  status: AdmissionLogStatus;
  ticketId: Scalars['Int']['input'];
  ticketTypeId: Scalars['Int']['input'];
};

/** the different admission_log statuses */
export enum AdmissionLogStatus {
  /** In */
  CHECKED_IN = 'CHECKED_IN',
  /**
   * prevent this ticket to be scanned (ex: the ticket is revoked
   *       after the devices downloaded the list of admission tickets)
   */
  DENIED = 'DENIED',
  /**
   * In but invalid ticket (ex: no ticket, not in the list,
   *         scanning issue, wrong time, wrong entrance...)
   */
  FORCED = 'FORCED',
  /**  In (searched manually from guestlist) */
  MANUAL_CHECKED_IN = 'MANUAL_CHECKED_IN',
  /** Not scanned yet */
  PENDING = 'PENDING'
}

/** admission_ticket: represents a ticket entrance */
export type AdmissionTicket = Node & {
  __typename?: 'AdmissionTicket';
  code: Scalars['String']['output'];
  fanEmail?: Maybe<Scalars['String']['output']>;
  fanFirstName?: Maybe<Scalars['String']['output']>;
  fanId: Scalars['String']['output'];
  fanLastName?: Maybe<Scalars['String']['output']>;
  fanPhoneNumber?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  source: Scalars['String']['output'];
  status: AdmissionLogStatus;
  ticketId: Scalars['Int']['output'];
  ticketTypeId: Scalars['Int']['output'];
};

export type AdmissionTicketsConnection = {
  __typename?: 'AdmissionTicketsConnection';
  edges?: Maybe<Array<Maybe<AdmissionTicketsEdge>>>;
  pageInfo: PageInfo;
};

export type AdmissionTicketsEdge = {
  __typename?: 'AdmissionTicketsEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<AdmissionTicket>;
};

export type AgeBreakdownItem = {
  __typename?: 'AgeBreakdownItem';
  count: Scalars['Int']['output'];
  from: Scalars['Int']['output'];
  to: Scalars['Int']['output'];
};

export type AggregatedSalesReport = {
  __typename?: 'AggregatedSalesReport';
  eventsCount: Scalars['Int']['output'];
  onSaleEventsCount: Scalars['Int']['output'];
  soldTickets: Scalars['Int']['output'];
  totalAllocation: Scalars['Int']['output'];
};

export type AllowedActions = {
  __typename?: 'AllowedActions';
  /** @deprecated Please use add_products instead */
  addExtras?: Maybe<Scalars['Boolean']['output']>;
  addProducts?: Maybe<Scalars['Boolean']['output']>;
  changeAllocation?: Maybe<Scalars['Boolean']['output']>;
  changeDates?: Maybe<Scalars['Boolean']['output']>;
  closeTier?: Maybe<Scalars['Boolean']['output']>;
  createSocialLinks?: Maybe<Scalars['Boolean']['output']>;
  downloadC1Form?: Maybe<Scalars['Boolean']['output']>;
  downloadChargebacksReport?: Maybe<Scalars['Boolean']['output']>;
  downloadDoorlist?: Maybe<Scalars['Boolean']['output']>;
  downloadDoorlistExtras?: Maybe<Scalars['Boolean']['output']>;
  downloadDoorlistMerch?: Maybe<Scalars['Boolean']['output']>;
  downloadSalesReport?: Maybe<Scalars['Boolean']['output']>;
  exportReturnRequests?: Maybe<Scalars['Boolean']['output']>;
  inviteExternalGuest?: Maybe<Scalars['Boolean']['output']>;
  manageBoxOffice?: Maybe<Scalars['Boolean']['output']>;
  manageCodeLocks?: Maybe<Scalars['Boolean']['output']>;
  manageDataCollection?: Maybe<Scalars['Boolean']['output']>;
  manageDiscounts?: Maybe<Scalars['Boolean']['output']>;
  /** @deprecated Please use manage_products_allocation instead */
  manageExtrasAllocation?: Maybe<Scalars['Boolean']['output']>;
  manageFacebook?: Maybe<Scalars['Boolean']['output']>;
  manageLinks?: Maybe<Scalars['Boolean']['output']>;
  manageProductsAllocation?: Maybe<Scalars['Boolean']['output']>;
  managePromotions?: Maybe<Scalars['Boolean']['output']>;
  managePromotionsMaxRedemptions?: Maybe<Scalars['Boolean']['output']>;
  manageTicketPools?: Maybe<Scalars['Boolean']['output']>;
  manageTickets?: Maybe<Scalars['Boolean']['output']>;
  markBackEvent?: Maybe<Scalars['Boolean']['output']>;
  minorUpdate?: Maybe<Scalars['Boolean']['output']>;
  performPayout?: Maybe<Scalars['Boolean']['output']>;
  readAdvancedStats?: Maybe<Scalars['Boolean']['output']>;
  readAllCustomerData?: Maybe<Scalars['Boolean']['output']>;
  readAnalytics?: Maybe<Scalars['Boolean']['output']>;
  readDoorSalesActivities?: Maybe<Scalars['Boolean']['output']>;
  readDoorlist?: Maybe<Scalars['Boolean']['output']>;
  readDoorlistExtras?: Maybe<Scalars['Boolean']['output']>;
  readDoorlistMerch?: Maybe<Scalars['Boolean']['output']>;
  readExtras?: Maybe<Scalars['Boolean']['output']>;
  readMarketingOptIns?: Maybe<Scalars['Boolean']['output']>;
  readMerch?: Maybe<Scalars['Boolean']['output']>;
  readSocialLinks?: Maybe<Scalars['Boolean']['output']>;
  toggleFanSeatSelection?: Maybe<Scalars['Boolean']['output']>;
};

export type AllowedLifecycleUpdateBase = {
  __typename?: 'AllowedLifecycleUpdateBase';
  canUpdate?: Maybe<Scalars['Boolean']['output']>;
};

export type AllowedLifecycleUpdates = {
  __typename?: 'AllowedLifecycleUpdates';
  ageLimit?: Maybe<AllowedLifecycleUpdateBase>;
  announceDate?: Maybe<AllowedLifecycleUpdateBase>;
  artistIds?: Maybe<AllowedLifecycleUpdateBase>;
  attractiveCompatibilityAe?: Maybe<AllowedLifecycleUpdateBase>;
  billingPromoter?: Maybe<AllowedLifecycleUpdateBase>;
  date?: Maybe<AllowedLifecycleUpdateBase>;
  description?: Maybe<AllowedLifecycleUpdateBase>;
  diceStreamDuration?: Maybe<AllowedLifecycleUpdateBase>;
  diceStreamDvrEnabled?: Maybe<AllowedLifecycleUpdateBase>;
  diceStreamRewatchEnabledUntil?: Maybe<AllowedLifecycleUpdateBase>;
  endDate?: Maybe<AllowedLifecycleUpdateBase>;
  eventImages?: Maybe<AllowedLifecycleUpdateBase>;
  eventRules?: Maybe<AllowedLifecycleUpdateBase>;
  extraNotes?: Maybe<AllowedLifecycleUpdateBase>;
  lineUp?: Maybe<AllowedLifecycleUpdateBase>;
  links?: Maybe<AllowedLifecycleUpdateBase>;
  manualValidationEnabled?: Maybe<AllowedLifecycleUpdateBase>;
  maxTicketsLimit?: Maybe<AllowedLifecycleUpdateBase>;
  media?: Maybe<AllowedLifecycleUpdateBase>;
  name?: Maybe<AllowedLifecycleUpdateBase>;
  offSaleDate?: Maybe<AllowedLifecycleUpdateBase>;
  onSaleDate?: Maybe<AllowedLifecycleUpdateBase>;
  paidWaitingList?: Maybe<AllowedLifecycleUpdateBase>;
  presentedBy?: Maybe<AllowedLifecycleUpdateBase>;
  products?: Maybe<AllowedLifecycleUpdatesProducts>;
  recurringEvents?: Maybe<AllowedLifecycleUpdateBase>;
  requiresBoxOfficeTicketNomination?: Maybe<AllowedLifecycleUpdateBase>;
  requiresTicketNomination?: Maybe<AllowedLifecycleUpdateBase>;
  restrictCountries?: Maybe<AllowedLifecycleUpdateBase>;
  restrictCountriesKind?: Maybe<AllowedLifecycleUpdateBase>;
  sendReceiptViaSms?: Maybe<AllowedLifecycleUpdateBase>;
  streamEmbedCode?: Maybe<AllowedLifecycleUpdateBase>;
  streamLink?: Maybe<AllowedLifecycleUpdateBase>;
  ticketPools?: Maybe<AllowedLifecycleUpdatesTicketPools>;
  ticketTypes?: Maybe<AllowedLifecycleUpdatesTicketTypes>;
  venues?: Maybe<AllowedLifecycleUpdateBase>;
};

export type AllowedLifecycleUpdatesProducts = {
  __typename?: 'AllowedLifecycleUpdatesProducts';
  canAdd?: Maybe<Scalars['Boolean']['output']>;
  canChangeAllocation?: Maybe<Scalars['Boolean']['output']>;
  canChangeOrder?: Maybe<Scalars['Boolean']['output']>;
  canDelete?: Maybe<Scalars['Boolean']['output']>;
  canUpdate?: Maybe<Scalars['Boolean']['output']>;
  canUpdateImages?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
  canUpdatePrice?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
};

export type AllowedLifecycleUpdatesTicketPools = {
  __typename?: 'AllowedLifecycleUpdatesTicketPools';
  canAdd?: Maybe<Scalars['Boolean']['output']>;
  canChangeAllocation?: Maybe<Scalars['Boolean']['output']>;
  canUpdate?: Maybe<Scalars['Boolean']['output']>;
  removablePools?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
};

export type AllowedLifecycleUpdatesTicketTypes = {
  __typename?: 'AllowedLifecycleUpdatesTicketTypes';
  canAdd?: Maybe<Scalars['Boolean']['output']>;
  canChangeAllocation?: Maybe<Scalars['Boolean']['output']>;
  canChangeDoorSalesEnabled?: Maybe<Scalars['Boolean']['output']>;
  canChangeExternalSkus?: Maybe<Scalars['Boolean']['output']>;
  canChangeIcon?: Maybe<Scalars['Boolean']['output']>;
  canChangeIncrements?: Maybe<Scalars['Boolean']['output']>;
  canChangeLimit?: Maybe<Scalars['Boolean']['output']>;
  canChangeOffSaleDate?: Maybe<Scalars['Boolean']['output']>;
  canChangeOnSaleDate?: Maybe<Scalars['Boolean']['output']>;
  canChangeOrder?: Maybe<Scalars['Boolean']['output']>;
  canChangeSeatmap?: Maybe<Scalars['Boolean']['output']>;
  canChangeTierNames?: Maybe<Scalars['Boolean']['output']>;
  canDelete?: Maybe<Scalars['Boolean']['output']>;
  canHide?: Maybe<Scalars['Boolean']['output']>;
  canUpdate?: Maybe<Scalars['Boolean']['output']>;
  updateablePriceTiers?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
  updateablePriceTtys?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
};

export type AppleMusicSearchResponse = {
  __typename?: 'AppleMusicSearchResponse';
  resultCount: Scalars['Int']['output'];
  results: Array<Maybe<AppleMusicTrack>>;
};

export type AppleMusicTrack = {
  __typename?: 'AppleMusicTrack';
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  openUrl?: Maybe<Scalars['String']['output']>;
  previewUrl?: Maybe<Scalars['String']['output']>;
};

export type AppleMusicViewer = {
  __typename?: 'AppleMusicViewer';
  search?: Maybe<AppleMusicSearchResponse>;
};


export type AppleMusicViewerSearchArgs = {
  q: Scalars['String']['input'];
};

export type ArchiveFanConnectInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type ArchiveFanConnectPayload = {
  __typename?: 'ArchiveFanConnectPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type Artist = Name & Node & {
  __typename?: 'Artist';
  /** @deprecated To be removed, please use viewer.activities instead */
  activities?: Maybe<ActivitiesConnection>;
  artistEvents?: Maybe<Array<Maybe<ArtistEvent>>>;
  backendArtistIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  createdAt?: Maybe<Scalars['Time']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  destinationAccount?: Maybe<DestinationAccount>;
  disambiguation?: Maybe<Scalars['String']['output']>;
  events?: Maybe<EventConnection>;
  hierarchicalTags?: Maybe<Array<Maybe<HierarchicalTag>>>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  inventory?: Maybe<Inventory>;
  links: Array<Maybe<Scalars['Map']['output']>>;
  media: Array<Maybe<MediaItem>>;
  musicbrainzId?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  performerType: PerformerType;
  permName?: Maybe<Scalars['String']['output']>;
  profileImageAttachment?: Maybe<Attachment>;
  profileImageCropRegion?: Maybe<CropRegion>;
  tags?: Maybe<Array<Maybe<Tag>>>;
  updatedAt?: Maybe<Scalars['Time']['output']>;
};


export type ArtistActivitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


export type ArtistArtistEventsArgs = {
  scopes?: InputMaybe<EventScopesInput>;
};


export type ArtistEventsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};

export type ArtistConnection = {
  __typename?: 'ArtistConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<ArtistEdge>>>;
  pageInfo: PageInfo;
};

export type ArtistEdge = {
  __typename?: 'ArtistEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Artist>;
};

/** Short version of an event to show it on artist merch field */
export type ArtistEvent = Node & {
  __typename?: 'ArtistEvent';
  addressCountry?: Maybe<Scalars['String']['output']>;
  addressLocality?: Maybe<Scalars['String']['output']>;
  addressRegion?: Maybe<Scalars['String']['output']>;
  addressState?: Maybe<Scalars['String']['output']>;
  announceDate?: Maybe<Scalars['Time']['output']>;
  costCurrency?: Maybe<EventCostCurrency>;
  countryCode?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['Time']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  eventId?: Maybe<Scalars['ID']['output']>;
  eventIdLive?: Maybe<Scalars['String']['output']>;
  eventImages?: Maybe<Array<Maybe<EventImage>>>;
  fullAddress?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  latitude?: Maybe<Scalars['Float']['output']>;
  longitude?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  offSaleDate?: Maybe<Scalars['Time']['output']>;
  onSaleDate?: Maybe<Scalars['Time']['output']>;
  organicSocialLink?: Maybe<Scalars['String']['output']>;
  postOfficeBoxNumber?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  streetAddress?: Maybe<Scalars['String']['output']>;
  timezoneName?: Maybe<Scalars['String']['output']>;
  venueName?: Maybe<Scalars['String']['output']>;
};

export type ArtistWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<ArtistWhereInput>>>;
  musicbrainzId?: InputMaybe<Scalars['Uuid']['input']>;
  name?: InputMaybe<OperatorsString>;
  spotifyArtistId?: InputMaybe<Scalars['String']['input']>;
};

export type AssignProductsToEventsInput = {
  clientMutationId: Scalars['String']['input'];
  inventoryId?: InputMaybe<Scalars['ID']['input']>;
  productToEvents?: InputMaybe<Array<InputMaybe<ProductToEventsInput>>>;
};

export type AssignProductsToEventsPayload = {
  __typename?: 'AssignProductsToEventsPayload';
  clientMutationId: Scalars['String']['output'];
  inventory?: Maybe<Inventory>;
};

export type AssociatedEntities = Promoter | Venue;

export type AttachTicketPoolInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  maxAllocation?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type Attachment = Node & {
  __typename?: 'Attachment';
  cdnUrl: Scalars['String']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
};

export type AttractiveFields = {
  __typename?: 'AttractiveFields';
  /** field dedicated to siae_genre_type: 01 */
  author?: Maybe<Scalars['String']['output']>;
  compatibilityAe?: Maybe<CompatibilityAe>;
  /** field dedicated to siae_genre_type: 01 */
  distributor?: Maybe<Scalars['String']['output']>;
  entertainmentPercent?: Maybe<Scalars['Int']['output']>;
  forceSubscription?: Maybe<Scalars['Boolean']['output']>;
  forceSubscriptionLimit?: Maybe<Scalars['Int']['output']>;
  integrationDisabled: Scalars['Boolean']['output'];
  linkedEvents?: Maybe<Array<Maybe<Event>>>;
  /** field dedicated to siae_genre_type: 01 */
  nationality?: Maybe<Scalars['String']['output']>;
  /** field dedicated to siae_genre_type: 45 */
  performer?: Maybe<Scalars['String']['output']>;
  /** field dedicated to siae_genre_type: 01 */
  producer?: Maybe<Scalars['String']['output']>;
  seatingAreaConfigs: Array<Maybe<SeatingAreaConfig>>;
  siaeGenreType?: Maybe<Scalars['String']['output']>;
  streamingTicketsIntegrationDisabled: Scalars['Boolean']['output'];
  subscriptionCode?: Maybe<Scalars['String']['output']>;
  subscriptionEventsLimit?: Maybe<Scalars['Int']['output']>;
  taxFree?: Maybe<Scalars['Boolean']['output']>;
};

export type AttractiveFieldsInput = {
  /** field dedicated to siae_genre_type: 01 */
  author?: InputMaybe<Scalars['String']['input']>;
  compatibilityAe?: InputMaybe<CompatibilityAe>;
  /** field dedicated to siae_genre_type: 01 */
  distributor?: InputMaybe<Scalars['String']['input']>;
  entertainmentPercent?: InputMaybe<Scalars['Int']['input']>;
  forceSubscription?: InputMaybe<Scalars['Boolean']['input']>;
  forceSubscriptionLimit?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  integrationDisabled?: InputMaybe<Scalars['Boolean']['input']>;
  linkedEventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  /** field dedicated to siae_genre_type: 01 */
  nationality?: InputMaybe<Scalars['String']['input']>;
  /** field dedicated to siae_genre_type: 45 */
  performer?: InputMaybe<Scalars['String']['input']>;
  /** field dedicated to siae_genre_type: 01 */
  producer?: InputMaybe<Scalars['String']['input']>;
  seatingAreaConfigs?: InputMaybe<Array<InputMaybe<SeatingAreaConfigInput>>>;
  siaeGenreType?: InputMaybe<Scalars['String']['input']>;
  streamingTicketsIntegrationDisabled?: InputMaybe<Scalars['Boolean']['input']>;
  subscriptionCode?: InputMaybe<Scalars['String']['input']>;
  subscriptionEventsLimit?: InputMaybe<Scalars['Int']['input']>;
  taxFree?: InputMaybe<Scalars['Boolean']['input']>;
};

export type AttractiveTicket = {
  __typename?: 'AttractiveTicket';
  activationCard?: Maybe<Scalars['String']['output']>;
  agency?: Maybe<Scalars['String']['output']>;
  agencyId?: Maybe<Scalars['Int']['output']>;
  barcode?: Maybe<Scalars['String']['output']>;
  barcodeAlt?: Maybe<Scalars['String']['output']>;
  block?: Maybe<Scalars['String']['output']>;
  blockId?: Maybe<Scalars['Int']['output']>;
  businessName?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  emissionDate?: Maybe<Scalars['Time']['output']>;
  emissionId?: Maybe<Scalars['Int']['output']>;
  eventDate?: Maybe<Scalars['Time']['output']>;
  eventDateId?: Maybe<Scalars['Int']['output']>;
  eventId?: Maybe<Scalars['Int']['output']>;
  eventTitle?: Maybe<Scalars['String']['output']>;
  eventsEntered?: Maybe<Scalars['Int']['output']>;
  eventsQuantity?: Maybe<Scalars['Int']['output']>;
  figurativeAmount?: Maybe<Scalars['Float']['output']>;
  figurativeVat?: Maybe<Scalars['Float']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  fiscalDate?: Maybe<Scalars['Time']['output']>;
  fiscalSeal?: Maybe<Scalars['String']['output']>;
  genre?: Maybe<Scalars['String']['output']>;
  genreDescription?: Maybe<Scalars['String']['output']>;
  isAbbonamento?: Maybe<Scalars['Boolean']['output']>;
  isVoided?: Maybe<Scalars['Boolean']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  movementValue?: Maybe<Scalars['Float']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  noteState?: Maybe<Scalars['String']['output']>;
  ntcEntranceDate?: Maybe<Scalars['Time']['output']>;
  orderCode?: Maybe<Scalars['String']['output']>;
  orderReference?: Maybe<Scalars['String']['output']>;
  originalEmissionId?: Maybe<Scalars['Int']['output']>;
  originalFirstName?: Maybe<Scalars['String']['output']>;
  originalLastName?: Maybe<Scalars['String']['output']>;
  personalId?: Maybe<Scalars['Int']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  piHolder?: Maybe<Scalars['String']['output']>;
  prepaidVat?: Maybe<Scalars['String']['output']>;
  prepaidVatDescription?: Maybe<Scalars['String']['output']>;
  preprint?: Maybe<Scalars['String']['output']>;
  presaleFee?: Maybe<Scalars['Float']['output']>;
  presaleVat?: Maybe<Scalars['Float']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
  priceType?: Maybe<Scalars['String']['output']>;
  priceVat?: Maybe<Scalars['Float']['output']>;
  printDate?: Maybe<Scalars['Time']['output']>;
  progressiveNumber?: Maybe<Scalars['String']['output']>;
  progressiveSector?: Maybe<Scalars['Int']['output']>;
  promoterFiscalCode?: Maybe<Scalars['String']['output']>;
  promoterName?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  reissueEmissionId?: Maybe<Scalars['Int']['output']>;
  row?: Maybe<Scalars['String']['output']>;
  seat?: Maybe<Scalars['String']['output']>;
  seatingArea?: Maybe<Scalars['String']['output']>;
  seatingAreaId?: Maybe<Scalars['Int']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  subscriptionCode?: Maybe<Scalars['String']['output']>;
  subscriptionFiscalCode?: Maybe<Scalars['String']['output']>;
  subscriptionProgressiveNumber?: Maybe<Scalars['String']['output']>;
  subscriptionText?: Maybe<Scalars['String']['output']>;
  supportCode?: Maybe<Scalars['String']['output']>;
  supportType?: Maybe<Scalars['String']['output']>;
  systemNumber?: Maybe<Scalars['String']['output']>;
  terminalId?: Maybe<Scalars['Int']['output']>;
  ticketType?: Maybe<Scalars['String']['output']>;
  timestamp?: Maybe<Scalars['String']['output']>;
  turn?: Maybe<Scalars['String']['output']>;
  validity?: Maybe<Scalars['Time']['output']>;
  venue?: Maybe<Scalars['String']['output']>;
  venueCode?: Maybe<Scalars['String']['output']>;
  voidedTicketEmissionId?: Maybe<Scalars['Int']['output']>;
  voidingCard?: Maybe<Scalars['String']['output']>;
  voidingDate?: Maybe<Scalars['Time']['output']>;
  voidingFiscalSeal?: Maybe<Scalars['String']['output']>;
  voidingProgressiveNumber?: Maybe<Scalars['String']['output']>;
  voidingReason?: Maybe<Scalars['String']['output']>;
  voidingTransactionEmissionId?: Maybe<Scalars['Int']['output']>;
  voidingType?: Maybe<Scalars['String']['output']>;
};

export type AttractiveTicketConnection = {
  __typename?: 'AttractiveTicketConnection';
  edges?: Maybe<Array<Maybe<AttractiveTicketEdge>>>;
  pageInfo: PageInfo;
};

export type AttractiveTicketEdge = {
  __typename?: 'AttractiveTicketEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<AttractiveTicket>;
};

export type AutoRescheduledEventRefunds = {
  __typename?: 'AutoRescheduledEventRefunds';
  active?: Maybe<Scalars['Boolean']['output']>;
  cutoffDays?: Maybe<Scalars['Int']['output']>;
};

export type AutoRescheduledEventRefundsInput = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
  cutoffDays?: InputMaybe<Scalars['Int']['input']>;
};

export type AutomaticRollingPaymentsConfiguration = {
  __typename?: 'AutomaticRollingPaymentsConfiguration';
  active?: Maybe<Scalars['Boolean']['output']>;
  holdbackForDisputes?: Maybe<Scalars['Float']['output']>;
  holdbackInterval?: Maybe<Scalars['Int']['output']>;
  payoutPercent?: Maybe<Scalars['Float']['output']>;
  reportOnly?: Maybe<Scalars['Boolean']['output']>;
  reportRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type AutomaticRollingPaymentsConfigurationInput = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
  holdbackForDisputes?: InputMaybe<Scalars['Float']['input']>;
  holdbackInterval?: InputMaybe<Scalars['Int']['input']>;
  payoutPercent?: InputMaybe<Scalars['Float']['input']>;
  reportOnly?: InputMaybe<Scalars['Boolean']['input']>;
  reportRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type BackendEvent = {
  __typename?: 'BackendEvent';
  address?: Maybe<Scalars['String']['output']>;
  artistIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  backgroundImage?: Maybe<Scalars['String']['output']>;
  barCodeType?: Maybe<Scalars['String']['output']>;
  cityIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  codeLockedImage?: Maybe<Scalars['String']['output']>;
  colour?: Maybe<Scalars['Map']['output']>;
  cost?: Maybe<BackendEventCost>;
  date?: Maybe<Scalars['String']['output']>;
  dateEnd?: Maybe<Scalars['Time']['output']>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  faqs?: Maybe<Array<Maybe<BackendFaq>>>;
  flags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  images?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  lineup?: Maybe<Scalars['Map']['output']>;
  location?: Maybe<BackendEventLocation>;
  locked?: Maybe<Scalars['Boolean']['output']>;
  maxTicketsLimit?: Maybe<Scalars['Int']['output']>;
  media?: Maybe<Scalars['Map']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  payments?: Maybe<Scalars['Map']['output']>;
  permName?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['Map']['output']>;
  relatedEventIds?: Maybe<Scalars['Map']['output']>;
  returns?: Maybe<Scalars['Map']['output']>;
  saleStartDate?: Maybe<Scalars['Time']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  ticketImage?: Maybe<Scalars['String']['output']>;
  ticketTypes?: Maybe<Array<Maybe<BackendTicketType>>>;
  totalNumTickets?: Maybe<Scalars['Int']['output']>;
  transfer?: Maybe<BackendEventTransfer>;
  userIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  venue?: Maybe<Scalars['String']['output']>;
};

export type BackendEventCost = {
  __typename?: 'BackendEventCost';
  amount: Scalars['Int']['output'];
  currency: Scalars['String']['output'];
  feeAmount?: Maybe<Scalars['Int']['output']>;
};

export type BackendEventLocation = {
  __typename?: 'BackendEventLocation';
  accuracy: Scalars['Float']['output'];
  lat: Scalars['Float']['output'];
  lng: Scalars['Float']['output'];
  place: Scalars['String']['output'];
};

export type BackendEventTransfer = {
  __typename?: 'BackendEventTransfer';
  deadline?: Maybe<Scalars['Time']['output']>;
  mode: Scalars['Int']['output'];
};

export type BackendFaq = {
  __typename?: 'BackendFaq';
  body?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  order?: Maybe<Scalars['Int']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type BackendTicketType = {
  __typename?: 'BackendTicketType';
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  tiers?: Maybe<Array<Maybe<Scalars['Map']['output']>>>;
};

export type BalanceBatch = {
  __typename?: 'BalanceBatch';
  balanceChange: Scalars['Int']['output'];
  completedAt?: Maybe<Scalars['Time']['output']>;
  currency: Scalars['String']['output'];
  destinationAccount: Scalars['String']['output'];
  eventId: Scalars['Int']['output'];
  intId: Scalars['Int']['output'];
  region: Scalars['String']['output'];
  transfer?: Maybe<StripeTransfer>;
  transferReversals: Array<Maybe<StripeTransferReversal>>;
};

export enum BasePriceMode {
  FACE_VALUE = 'FACE_VALUE',
  FACE_VALUE_PLUS_VENUE_LEVY = 'FACE_VALUE_PLUS_VENUE_LEVY'
}

/** admission base report */
export type BaseReport = {
  __typename?: 'BaseReport';
  notScanned: Scalars['Int']['output'];
  scanned: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type BillingDestination = {
  __typename?: 'BillingDestination';
  billingAccount?: Maybe<Promoter>;
};

export type BoxOfficePaymentInput = {
  amount?: InputMaybe<Scalars['Int']['input']>;
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  terminalSerialId?: InputMaybe<Scalars['String']['input']>;
  ticketIds?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};

export type BoxOfficePaymentPayload = {
  __typename?: 'BoxOfficePaymentPayload';
  clientMutationId: Scalars['String']['output'];
  secret: Scalars['String']['output'];
  taxAmount: Scalars['Int']['output'];
};

export type BulkUpdateFanSurveyQuestionsInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  fanQuestionsInput?: InputMaybe<Array<InputMaybe<FanQuestionInput>>>;
};

export type BulkUpdateFanSurveyQuestionsPayload = {
  __typename?: 'BulkUpdateFanSurveyQuestionsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Event>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

/** Custom event bundles */
export type Bundle = Node & {
  __typename?: 'Bundle';
  canManage?: Maybe<Scalars['Boolean']['output']>;
  city?: Maybe<City>;
  events?: Maybe<Array<Maybe<Event>>>;
  expiryDate?: Maybe<Scalars['Date']['output']>;
  fromDate?: Maybe<Scalars['Date']['output']>;
  hidden: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  maxPrice?: Maybe<Scalars['Int']['output']>;
  minPrice?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  owner?: Maybe<Promoter>;
  permName?: Maybe<Scalars['String']['output']>;
  profileDetails?: Maybe<ProfileDetails>;
  promoterCurator?: Maybe<Promoter>;
  promoters?: Maybe<Array<Maybe<Promoter>>>;
  socialLinks?: Maybe<Array<Maybe<SocialLink>>>;
  toDate?: Maybe<Scalars['Date']['output']>;
  venueCurator?: Maybe<Venue>;
};

export type BundleConnection = {
  __typename?: 'BundleConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<BundleEdge>>>;
  pageInfo: PageInfo;
};

export type BundleEdge = {
  __typename?: 'BundleEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Bundle>;
};

export enum BundleOrder {
  EXPIRY_DATE_ASC = 'EXPIRY_DATE_ASC',
  EXPIRY_DATE_DESC = 'EXPIRY_DATE_DESC'
}

export type BundleWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<BundleWhereInput>>>;
  active?: InputMaybe<OperatorsBooleanEqInput>;
  id?: InputMaybe<OperatorsIdEqInput>;
  name?: InputMaybe<OperatorsString>;
};

export type CancelReportInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type CancelReportPayload = {
  __typename?: 'CancelReportPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ReportOrSchedule>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type Category = Node & {
  __typename?: 'Category';
  categories: Array<Maybe<Category>>;
  coverBackgroundColor?: Maybe<Scalars['String']['output']>;
  coverImageUrl?: Maybe<Scalars['String']['output']>;
  hidden: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  parentCategory?: Maybe<Category>;
  rootType?: Maybe<ProductRootType>;
  type?: Maybe<CategoryType>;
};

export type CategoryBreakdown = {
  __typename?: 'CategoryBreakdown';
  category?: Maybe<Category>;
  productBreakdown?: Maybe<Array<Maybe<ProductStats>>>;
};

export enum CategoryType {
  ACCESS_PASS = 'ACCESS_PASS',
  AFTER_PARTY_PASS = 'AFTER_PARTY_PASS',
  ARTIST_MEET_AND_GREET = 'ARTIST_MEET_AND_GREET',
  ARTIST_MERCH = 'ARTIST_MERCH',
  CAMPING = 'CAMPING',
  COACH_BUS = 'COACH_BUS',
  DRINKS_ALCOHOLIC = 'DRINKS_ALCOHOLIC',
  DRINKS_NON_ALCOHOLIC = 'DRINKS_NON_ALCOHOLIC',
  EARLY_ENTRY_PASS = 'EARLY_ENTRY_PASS',
  EXPERIENCE = 'EXPERIENCE',
  FOOD = 'FOOD',
  FOOD_AND_DRINK = 'FOOD_AND_DRINK',
  JUMPER = 'JUMPER',
  MERCH = 'MERCH',
  OTHER = 'OTHER',
  PARKING = 'PARKING',
  QUEUE_JUMP = 'QUEUE_JUMP',
  TRAVEL_AND_ACCOMMODATION = 'TRAVEL_AND_ACCOMMODATION',
  T_SHIRT = 'T_SHIRT',
  VIP_UPGRADE = 'VIP_UPGRADE'
}

export type ChangeAllocationInput = {
  allocation: Scalars['Int']['input'];
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type ChangeAllocationPayload = {
  __typename?: 'ChangeAllocationPayload';
  clientMutationId: Scalars['String']['output'];
  object?: Maybe<Event>;
};

export type ChangeDatesInput = {
  announceDate: Scalars['Time']['input'];
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  onSaleDate: Scalars['Time']['input'];
};

export type ChangeDatesPayload = {
  __typename?: 'ChangeDatesPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export enum ChannelType {
  APP_SALE = 'APP_SALE',
  BOX_OFFICE_SALE = 'BOX_OFFICE_SALE',
  HOLD = 'HOLD',
  NON_DICE = 'NON_DICE',
  OTHER = 'OTHER'
}

export type Characteristic = Node & {
  __typename?: 'Characteristic';
  /** The ID of an object */
  id: Scalars['ID']['output'];
  level?: Maybe<CharacteristicLevel>;
  name?: Maybe<Scalars['String']['output']>;
};

export type CharacteristicConnection = {
  __typename?: 'CharacteristicConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<CharacteristicEdge>>>;
  pageInfo: PageInfo;
};

export type CharacteristicEdge = {
  __typename?: 'CharacteristicEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Characteristic>;
};

export enum CharacteristicLevel {
  event = 'event',
  venue = 'venue'
}

export type CharacteristicWhereInput = {
  level?: InputMaybe<CharacteristicLevel>;
  name?: InputMaybe<OperatorsString>;
};

export type Charge = {
  __typename?: 'Charge';
  paymentMethodType?: Maybe<Scalars['String']['output']>;
  stripeLink?: Maybe<Scalars['String']['output']>;
};

export type ChartManagerCredentials = {
  __typename?: 'ChartManagerCredentials';
  chartId: Scalars['String']['output'];
  publicKey: Scalars['String']['output'];
  secretKey: Scalars['String']['output'];
};

export type Checklist = {
  __typename?: 'Checklist';
  items: Array<Maybe<ChecklistItem>>;
  name: Scalars['String']['output'];
  uuid: Scalars['String']['output'];
};

export type ChecklistInput = {
  items: Array<InputMaybe<ChecklistItemInput>>;
  name: Scalars['String']['input'];
  uuid: Scalars['String']['input'];
};

export type ChecklistItem = {
  __typename?: 'ChecklistItem';
  completed: Scalars['Boolean']['output'];
  name?: Maybe<Scalars['String']['output']>;
  uuid: Scalars['String']['output'];
};

export type ChecklistItemInput = {
  completed: Scalars['Boolean']['input'];
  name: Scalars['String']['input'];
  uuid: Scalars['String']['input'];
};

export type ChecklistsNode = {
  checklists: Array<Maybe<Checklist>>;
  id: Scalars['ID']['output'];
};

export type CitiesWhereInput = {
  name?: InputMaybe<Scalars['String']['input']>;
};

export type City = {
  __typename?: 'City';
  code: Scalars['String']['output'];
  countryId: Scalars['String']['output'];
  countryName: Scalars['String']['output'];
  countryPermName: Scalars['String']['output'];
  id: Scalars['String']['output'];
  location?: Maybe<CityLocation>;
  name: Scalars['String']['output'];
  permName: Scalars['String']['output'];
};

export type CityLocation = {
  __typename?: 'CityLocation';
  accuracy: Scalars['Int']['output'];
  lat: Scalars['Float']['output'];
  lng: Scalars['Float']['output'];
  place: Scalars['String']['output'];
};

export type CloneEventInput = {
  clientMutationId: Scalars['String']['input'];
  frequency: RepeatFrequency;
  id: Scalars['ID']['input'];
  occurrences?: InputMaybe<Scalars['Int']['input']>;
  repeatEnds: RepeatEnds;
  repeatOn?: InputMaybe<RepeatOn>;
  until?: InputMaybe<Scalars['Time']['input']>;
};

export type CloneEventPayload = {
  __typename?: 'CloneEventPayload';
  clientMutationId: Scalars['String']['output'];
  events?: Maybe<Array<Maybe<Event>>>;
};

export type CloneEventPromotionInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type CloneEventPromotionPayload = {
  __typename?: 'CloneEventPromotionPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventPromotion>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CodeLock = Node & {
  __typename?: 'CodeLock';
  code: Scalars['String']['output'];
  createdUses?: Maybe<Scalars['Int']['output']>;
  disabled?: Maybe<Scalars['Boolean']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  link?: Maybe<Scalars['String']['output']>;
  status: CodeLockStatus;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  unlimited?: Maybe<Scalars['Boolean']['output']>;
  usageStatus: CodeLockUsageStatus;
  uses?: Maybe<Scalars['Int']['output']>;
  validFrom?: Maybe<Scalars['Time']['output']>;
  validTo?: Maybe<Scalars['Time']['output']>;
};

export type CodeLockConnection = {
  __typename?: 'CodeLockConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<CodeLockEdge>>>;
  pageInfo: PageInfo;
};

export type CodeLockEdge = {
  __typename?: 'CodeLockEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<CodeLock>;
};

export type CodeLockFilter = {
  code?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<CodeLockUsageStatus>;
};

export enum CodeLockStatus {
  AVAILABLE = 'AVAILABLE',
  DELETED = 'DELETED',
  UNLIMITED_USE = 'UNLIMITED_USE',
  USED = 'USED'
}

export enum CodeLockUsageStatus {
  DISABLED = 'DISABLED',
  NOT_USED = 'NOT_USED',
  USED = 'USED'
}

export type CodeUsage = {
  __typename?: 'CodeUsage';
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
  ticketType?: Maybe<TicketType>;
  ticketsSold: Scalars['Int']['output'];
  usedOn: Scalars['Time']['output'];
};

export type CodeUsageConnection = {
  __typename?: 'CodeUsageConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<CodeUsageEdge>>>;
  pageInfo: PageInfo;
};

export type CodeUsageEdge = {
  __typename?: 'CodeUsageEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<CodeUsage>;
};

export enum CodesOperationType {
  CREATE_CODES = 'CREATE_CODES',
  DELETE_CODES = 'DELETE_CODES',
  DISABLE_CODES = 'DISABLE_CODES',
  ENABLE_CODES = 'ENABLE_CODES',
  UPLOAD_CODES = 'UPLOAD_CODES'
}

export type CodesOperationsInput = {
  allCodes?: InputMaybe<Scalars['Boolean']['input']>;
  amount?: InputMaybe<Scalars['Int']['input']>;
  codes?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  filename?: InputMaybe<Scalars['String']['input']>;
  force?: InputMaybe<Scalars['Boolean']['input']>;
  operationType: CodesOperationType;
};

export type ColourInput = {
  alpha?: InputMaybe<Scalars['Int']['input']>;
  blue?: InputMaybe<Scalars['Int']['input']>;
  green?: InputMaybe<Scalars['Int']['input']>;
  red?: InputMaybe<Scalars['Int']['input']>;
};

export enum CompatibilityAe {
  _0 = '_0',
  _1000 = '_1000',
  _5000 = '_5000'
}

export type Config = {
  __typename?: 'Config';
  s3Policy: Scalars['String']['output'];
  s3Signature: Scalars['String']['output'];
};

/** admission config options for the uniqueness definition */
export enum ConfigUniqueness {
  INFINITY = 'INFINITY',
  UNIQUE = 'UNIQUE',
  UNIQUE_PER_CONFIG = 'UNIQUE_PER_CONFIG'
}

export type ConnectedAccount = {
  __typename?: 'ConnectedAccount';
  accountCountry?: Maybe<Scalars['String']['output']>;
  accountCurrency?: Maybe<EventCostCurrency>;
  accountNumber?: Maybe<Scalars['String']['output']>;
  accountRouting?: Maybe<Scalars['String']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  businessName?: Maybe<Scalars['String']['output']>;
  businessTaxId?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  dob?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  legalEntityType?: Maybe<LegalEntityType>;
  personalAddressAddress?: Maybe<Scalars['String']['output']>;
  personalAddressCity?: Maybe<Scalars['String']['output']>;
  personalAddressPostalCode?: Maybe<Scalars['String']['output']>;
  personalState?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  ssnLast4?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
};

export type ContactAccountUser = {
  accountId: Scalars['ID']['input'];
  permissionProfileId: Scalars['ID']['input'];
};

export type ContactsInput = {
  additionalAccountsAccess?: InputMaybe<Array<InputMaybe<ContactAccountUser>>>;
  detailedRemittanceRecipient?: InputMaybe<Scalars['Boolean']['input']>;
  doorlistRecipient?: InputMaybe<Scalars['Boolean']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  hasQflowAccount?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  loginEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  mioRedesignV2?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  pamUser?: InputMaybe<Scalars['Boolean']['input']>;
  permissionProfileId?: InputMaybe<Scalars['ID']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  position?: InputMaybe<Scalars['String']['input']>;
  preferredLanguage?: InputMaybe<Language>;
  promoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  remittanceRecipient?: InputMaybe<Scalars['Boolean']['input']>;
  venueIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type Contracts = {
  contracts?: Maybe<Array<Maybe<FeesConfiguration>>>;
};

export type CoolingOffPeriodInput = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
  hours?: InputMaybe<Scalars['Int']['input']>;
};

export type CopySeatingChartInput = {
  clientMutationId: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  seatingChartId: Scalars['ID']['input'];
};

export type CopySeatingChartPayload = {
  __typename?: 'CopySeatingChartPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<SeatingChart>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export enum CountryCode {
  AD = 'AD',
  AE = 'AE',
  AF = 'AF',
  AG = 'AG',
  AI = 'AI',
  AL = 'AL',
  AM = 'AM',
  AN = 'AN',
  AO = 'AO',
  AQ = 'AQ',
  AR = 'AR',
  AS = 'AS',
  AT = 'AT',
  AU = 'AU',
  AW = 'AW',
  AX = 'AX',
  AZ = 'AZ',
  BA = 'BA',
  BB = 'BB',
  BD = 'BD',
  BE = 'BE',
  BF = 'BF',
  BG = 'BG',
  BH = 'BH',
  BI = 'BI',
  BJ = 'BJ',
  BL = 'BL',
  BM = 'BM',
  BN = 'BN',
  BO = 'BO',
  BQ = 'BQ',
  BR = 'BR',
  BS = 'BS',
  BT = 'BT',
  BV = 'BV',
  BW = 'BW',
  BY = 'BY',
  BZ = 'BZ',
  CA = 'CA',
  CC = 'CC',
  CD = 'CD',
  CF = 'CF',
  CG = 'CG',
  CH = 'CH',
  CI = 'CI',
  CK = 'CK',
  CL = 'CL',
  CM = 'CM',
  CN = 'CN',
  CO = 'CO',
  CR = 'CR',
  CU = 'CU',
  CV = 'CV',
  CW = 'CW',
  CX = 'CX',
  CY = 'CY',
  CZ = 'CZ',
  DE = 'DE',
  DJ = 'DJ',
  DK = 'DK',
  DM = 'DM',
  DO = 'DO',
  DZ = 'DZ',
  EC = 'EC',
  EE = 'EE',
  EG = 'EG',
  EH = 'EH',
  ER = 'ER',
  ES = 'ES',
  ET = 'ET',
  FI = 'FI',
  FJ = 'FJ',
  FK = 'FK',
  FM = 'FM',
  FO = 'FO',
  FR = 'FR',
  GA = 'GA',
  GB = 'GB',
  GD = 'GD',
  GE = 'GE',
  GF = 'GF',
  GG = 'GG',
  GH = 'GH',
  GI = 'GI',
  GL = 'GL',
  GM = 'GM',
  GN = 'GN',
  GP = 'GP',
  GQ = 'GQ',
  GR = 'GR',
  GS = 'GS',
  GT = 'GT',
  GU = 'GU',
  GW = 'GW',
  GY = 'GY',
  HK = 'HK',
  HM = 'HM',
  HN = 'HN',
  HR = 'HR',
  HT = 'HT',
  HU = 'HU',
  ID = 'ID',
  IE = 'IE',
  IL = 'IL',
  IM = 'IM',
  IN = 'IN',
  IO = 'IO',
  IQ = 'IQ',
  IR = 'IR',
  IS = 'IS',
  IT = 'IT',
  JE = 'JE',
  JM = 'JM',
  JO = 'JO',
  JP = 'JP',
  KE = 'KE',
  KG = 'KG',
  KH = 'KH',
  KI = 'KI',
  KM = 'KM',
  KN = 'KN',
  KP = 'KP',
  KR = 'KR',
  KW = 'KW',
  KY = 'KY',
  KZ = 'KZ',
  LA = 'LA',
  LB = 'LB',
  LC = 'LC',
  LI = 'LI',
  LK = 'LK',
  LR = 'LR',
  LS = 'LS',
  LT = 'LT',
  LU = 'LU',
  LV = 'LV',
  LY = 'LY',
  MA = 'MA',
  MC = 'MC',
  MD = 'MD',
  ME = 'ME',
  MF = 'MF',
  MG = 'MG',
  MH = 'MH',
  MK = 'MK',
  ML = 'ML',
  MM = 'MM',
  MN = 'MN',
  MO = 'MO',
  MP = 'MP',
  MQ = 'MQ',
  MR = 'MR',
  MS = 'MS',
  MT = 'MT',
  MU = 'MU',
  MV = 'MV',
  MW = 'MW',
  MX = 'MX',
  MY = 'MY',
  MZ = 'MZ',
  NA = 'NA',
  NC = 'NC',
  NE = 'NE',
  NF = 'NF',
  NG = 'NG',
  NI = 'NI',
  NL = 'NL',
  NO = 'NO',
  NP = 'NP',
  NR = 'NR',
  NU = 'NU',
  NZ = 'NZ',
  OM = 'OM',
  PA = 'PA',
  PE = 'PE',
  PF = 'PF',
  PG = 'PG',
  PH = 'PH',
  PK = 'PK',
  PL = 'PL',
  PM = 'PM',
  PN = 'PN',
  PR = 'PR',
  PS = 'PS',
  PT = 'PT',
  PW = 'PW',
  PY = 'PY',
  QA = 'QA',
  RE = 'RE',
  RO = 'RO',
  RS = 'RS',
  RU = 'RU',
  RW = 'RW',
  SA = 'SA',
  SB = 'SB',
  SC = 'SC',
  SD = 'SD',
  SE = 'SE',
  SG = 'SG',
  SH = 'SH',
  SI = 'SI',
  SJ = 'SJ',
  SK = 'SK',
  SL = 'SL',
  SM = 'SM',
  SN = 'SN',
  SO = 'SO',
  SR = 'SR',
  SS = 'SS',
  ST = 'ST',
  SV = 'SV',
  SX = 'SX',
  SY = 'SY',
  SZ = 'SZ',
  TC = 'TC',
  TD = 'TD',
  TF = 'TF',
  TG = 'TG',
  TH = 'TH',
  TJ = 'TJ',
  TK = 'TK',
  TL = 'TL',
  TM = 'TM',
  TN = 'TN',
  TO = 'TO',
  TR = 'TR',
  TT = 'TT',
  TV = 'TV',
  TW = 'TW',
  TZ = 'TZ',
  UA = 'UA',
  UG = 'UG',
  UM = 'UM',
  US = 'US',
  UY = 'UY',
  UZ = 'UZ',
  VA = 'VA',
  VC = 'VC',
  VE = 'VE',
  VG = 'VG',
  VI = 'VI',
  VN = 'VN',
  VU = 'VU',
  WF = 'WF',
  WS = 'WS',
  YE = 'YE',
  YT = 'YT',
  ZA = 'ZA',
  ZM = 'ZM',
  ZW = 'ZW'
}

export type CreateAdmissionConfigInput = {
  clientMutationId: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  enabled?: InputMaybe<Scalars['Boolean']['input']>;
  fromShift: Scalars['Int']['input'];
  name: Scalars['String']['input'];
  note?: InputMaybe<Scalars['String']['input']>;
  promoterId: Scalars['ID']['input'];
  recurringLabels?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  ticketTypeIds: Array<InputMaybe<Scalars['ID']['input']>>;
  toShift: Scalars['Int']['input'];
  uniqueness: ConfigUniqueness;
};

export type CreateAdmissionConfigPayload = {
  __typename?: 'CreateAdmissionConfigPayload';
  admissionConfig?: Maybe<AdmissionConfig>;
  clientMutationId: Scalars['String']['output'];
};

export type CreateArtistInput = {
  backendArtistIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  disambiguation?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  media?: InputMaybe<Array<InputMaybe<MediaItemInputObject>>>;
  musicbrainzId?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  performerType?: InputMaybe<PerformerType>;
  profileImageAttachmentId?: InputMaybe<Scalars['ID']['input']>;
  profileImageCropRegion?: InputMaybe<CropRegionInput>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type CreateArtistPayload = {
  __typename?: 'CreateArtistPayload';
  artist?: Maybe<Artist>;
  clientMutationId: Scalars['String']['output'];
};

export type CreateAttachmentInput = {
  clientMutationId: Scalars['String']['input'];
  format: Scalars['String']['input'];
};

export type CreateAttachmentPayload = {
  __typename?: 'CreateAttachmentPayload';
  attachment?: Maybe<Attachment>;
  clientMutationId: Scalars['String']['output'];
  presignedUploadUrl: Scalars['String']['output'];
};

export type CreateBundleInput = {
  cityId?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  curatedByPromoter?: InputMaybe<CuratedByPromoterInput>;
  curatedByVenue?: InputMaybe<CuratedByVenueInput>;
  eventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  expiryDate?: InputMaybe<Scalars['Date']['input']>;
  fromDate?: InputMaybe<Scalars['Date']['input']>;
  hidden: Scalars['Boolean']['input'];
  maxPrice?: InputMaybe<Scalars['Int']['input']>;
  minPrice?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
  profileDetails?: InputMaybe<ProfileDetailsInput>;
  promotersBundles?: InputMaybe<Array<InputMaybe<PromotersBundles>>>;
  toDate?: InputMaybe<Scalars['Date']['input']>;
};

export type CreateBundlePayload = {
  __typename?: 'CreateBundlePayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Bundle>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateCommentActivityInput = {
  clientMutationId: Scalars['String']['input'];
  content: Scalars['String']['input'];
  itemId: Scalars['ID']['input'];
};

export type CreateCommentActivityPayload = {
  __typename?: 'CreateCommentActivityPayload';
  activity?: Maybe<Activity>;
  clientMutationId: Scalars['String']['output'];
};

export type CreateDraftEventInput = {
  /** The name of the object type currently being queried. */
  additionalArtists?: InputMaybe<Array<InputMaybe<AdditionalArtistInput>>>;
  additionalInfos?: InputMaybe<Array<InputMaybe<EventAdditionalInfoInput>>>;
  addressCapacity?: InputMaybe<Scalars['Int']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressSiaeCode?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  ageLimit?: InputMaybe<Scalars['String']['input']>;
  announceDate?: InputMaybe<Scalars['Time']['input']>;
  artistIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  artists?: InputMaybe<Array<InputMaybe<EventArtistInput>>>;
  attractiveFields?: InputMaybe<AttractiveFieldsInput>;
  barcodeType?: InputMaybe<Scalars['String']['input']>;
  basePriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  billingNotes?: InputMaybe<Scalars['String']['input']>;
  billingPromoterId?: InputMaybe<Scalars['ID']['input']>;
  bundleIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  characteristicIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  charityEvent?: InputMaybe<Scalars['Boolean']['input']>;
  charityId?: InputMaybe<Scalars['String']['input']>;
  checklists?: InputMaybe<Array<InputMaybe<ChecklistInput>>>;
  cityIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  closeEventDate?: InputMaybe<Scalars['Time']['input']>;
  colour?: InputMaybe<ColourInput>;
  completedSteps?: InputMaybe<Scalars['Int']['input']>;
  costAmount?: InputMaybe<Scalars['Int']['input']>;
  costCurrency?: InputMaybe<Scalars['String']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  diceStatusNotes?: InputMaybe<Scalars['String']['input']>;
  diceStreamDuration?: InputMaybe<Scalars['Int']['input']>;
  diceStreamDvrEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  diceStreamRewatchEnabledUntil?: InputMaybe<Scalars['Time']['input']>;
  diceTv?: InputMaybe<Scalars['Boolean']['input']>;
  diceTvPlatform?: InputMaybe<TvPlatform>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  doorlistAdditionalRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  doorlistRecipientIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventIdLive?: InputMaybe<Scalars['String']['input']>;
  eventImages?: InputMaybe<Array<InputMaybe<EventImageInput>>>;
  eventLoadPredictions?: InputMaybe<Array<InputMaybe<EventLoadPredictionInput>>>;
  eventPromoters?: InputMaybe<Array<InputMaybe<EventPromoter>>>;
  eventRules?: InputMaybe<EventRulesInput>;
  eventSeatingChartId?: InputMaybe<Scalars['ID']['input']>;
  eventSharingObjects?: InputMaybe<Array<InputMaybe<EventSharingObjectInput>>>;
  eventType?: InputMaybe<EventType>;
  eventVenues?: InputMaybe<Array<InputMaybe<EventVenues>>>;
  extraNotes?: InputMaybe<Scalars['String']['input']>;
  fanFacingPromoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  fanSupportNotes?: InputMaybe<FanSupportNotesInput>;
  faqs?: InputMaybe<Array<InputMaybe<FaqInput>>>;
  featuredAreas?: InputMaybe<Array<InputMaybe<FeaturedAreaInput>>>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  feesBehaviour?: InputMaybe<FeesBehaviour>;
  flags?: InputMaybe<EventFlagsInput>;
  freeEvent?: InputMaybe<Scalars['Boolean']['input']>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  isTicketAvailableAtDoor?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  licenseNumber?: InputMaybe<Scalars['String']['input']>;
  lineup?: InputMaybe<Array<InputMaybe<LineupInput>>>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  lockVersion?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  manualValidationEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  marketeerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  maxTicketsLimit?: InputMaybe<Scalars['Int']['input']>;
  media?: InputMaybe<Array<InputMaybe<MediaItemInputObject>>>;
  musicbrainzArtists?: InputMaybe<Array<InputMaybe<MusicbrainzArtists>>>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotification?: InputMaybe<Scalars['Boolean']['input']>;
  onSaleNotificationAt?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotificationSmsContent?: InputMaybe<Scalars['String']['input']>;
  overriddenPromoterName?: InputMaybe<Scalars['String']['input']>;
  overrideFees?: InputMaybe<Scalars['Boolean']['input']>;
  permName?: InputMaybe<Scalars['String']['input']>;
  platformAccountCode?: InputMaybe<PlatformAccountCode>;
  postFanPriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  presentedBy?: InputMaybe<Scalars['String']['input']>;
  printedTicketFormat?: InputMaybe<PrintedTicketFormat>;
  products?: InputMaybe<Array<InputMaybe<ProductInput>>>;
  promoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  promoterStatusNotes?: InputMaybe<Scalars['String']['input']>;
  pwlWindow?: InputMaybe<Scalars['Int']['input']>;
  readAccessEmails?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  recurrentEventSchedule?: InputMaybe<RecurrentEventsScheduleInput>;
  relatedEventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  requiresBoxOfficeTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  requiresTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  restrictCountries?: InputMaybe<Array<InputMaybe<CountryCode>>>;
  restrictCountriesKind?: InputMaybe<RestrictionKind>;
  salesforceContractId?: InputMaybe<Scalars['ID']['input']>;
  scheduleStatus?: InputMaybe<ScheduleStatus>;
  seatingChannels?: InputMaybe<Array<InputMaybe<SeatingChannelInput>>>;
  sendReceiptViaSms?: InputMaybe<Scalars['Boolean']['input']>;
  showArtistDescription?: InputMaybe<ShowArtistDescription>;
  socialDistancingRulesetKey?: InputMaybe<Scalars['String']['input']>;
  stages?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  stripeAccountId?: InputMaybe<Scalars['String']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  taxSettings?: InputMaybe<TaxSettingsInput>;
  thirdPartySettingsId?: InputMaybe<Scalars['ID']['input']>;
  ticketPools?: InputMaybe<Array<InputMaybe<AttachTicketPoolInput>>>;
  ticketType?: InputMaybe<Scalars['String']['input']>;
  ticketTypes?: InputMaybe<Array<InputMaybe<TicketTypesInput>>>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  totalTickets?: InputMaybe<Scalars['Int']['input']>;
  venue?: InputMaybe<Scalars['String']['input']>;
  venueConfigurationId?: InputMaybe<Scalars['ID']['input']>;
  venueIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  venueName?: InputMaybe<Scalars['String']['input']>;
  venueSchedules?: InputMaybe<Array<InputMaybe<VenueScheduleInput>>>;
  venueSpaceId?: InputMaybe<Scalars['ID']['input']>;
  waitingListExchangeWindows?: InputMaybe<Array<InputMaybe<WaitingListExchangeWindowInput>>>;
};

export type CreateDraftEventPayload = {
  __typename?: 'CreateDraftEventPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Event>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateEventChangedNotificationInput = {
  changes?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  message?: InputMaybe<Scalars['String']['input']>;
  sendMeACopy?: InputMaybe<Scalars['Boolean']['input']>;
};

export type CreateEventChangedNotificationPayload = {
  __typename?: 'CreateEventChangedNotificationPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateEventInput = {
  /** The name of the object type currently being queried. */
  additionalArtists?: InputMaybe<Array<InputMaybe<AdditionalArtistInput>>>;
  additionalInfos?: InputMaybe<Array<InputMaybe<EventAdditionalInfoInput>>>;
  addressCapacity?: InputMaybe<Scalars['Int']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressSiaeCode?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  ageLimit?: InputMaybe<Scalars['String']['input']>;
  announceDate?: InputMaybe<Scalars['Time']['input']>;
  artistIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  artists?: InputMaybe<Array<InputMaybe<EventArtistInput>>>;
  attractiveFields?: InputMaybe<AttractiveFieldsInput>;
  barcodeType?: InputMaybe<Scalars['String']['input']>;
  basePriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  billingNotes?: InputMaybe<Scalars['String']['input']>;
  billingPromoterId?: InputMaybe<Scalars['ID']['input']>;
  bundleIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  characteristicIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  charityEvent?: InputMaybe<Scalars['Boolean']['input']>;
  charityId?: InputMaybe<Scalars['String']['input']>;
  checklists?: InputMaybe<Array<InputMaybe<ChecklistInput>>>;
  cityIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  closeEventDate?: InputMaybe<Scalars['Time']['input']>;
  colour?: InputMaybe<ColourInput>;
  completedSteps?: InputMaybe<Scalars['Int']['input']>;
  costAmount?: InputMaybe<Scalars['Int']['input']>;
  costCurrency?: InputMaybe<Scalars['String']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  diceStatusNotes?: InputMaybe<Scalars['String']['input']>;
  diceStreamDuration?: InputMaybe<Scalars['Int']['input']>;
  diceStreamDvrEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  diceStreamRewatchEnabledUntil?: InputMaybe<Scalars['Time']['input']>;
  diceTv?: InputMaybe<Scalars['Boolean']['input']>;
  diceTvPlatform?: InputMaybe<TvPlatform>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  doorlistAdditionalRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  doorlistRecipientIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventIdLive?: InputMaybe<Scalars['String']['input']>;
  eventImages?: InputMaybe<Array<InputMaybe<EventImageInput>>>;
  eventLoadPredictions?: InputMaybe<Array<InputMaybe<EventLoadPredictionInput>>>;
  eventPromoters?: InputMaybe<Array<InputMaybe<EventPromoter>>>;
  eventRules?: InputMaybe<EventRulesInput>;
  eventSeatingChartId?: InputMaybe<Scalars['ID']['input']>;
  eventSharingObjects?: InputMaybe<Array<InputMaybe<EventSharingObjectInput>>>;
  eventType?: InputMaybe<EventType>;
  eventVenues?: InputMaybe<Array<InputMaybe<EventVenues>>>;
  extraNotes?: InputMaybe<Scalars['String']['input']>;
  fanFacingPromoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  fanSupportNotes?: InputMaybe<FanSupportNotesInput>;
  faqs?: InputMaybe<Array<InputMaybe<FaqInput>>>;
  featuredAreas?: InputMaybe<Array<InputMaybe<FeaturedAreaInput>>>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  feesBehaviour?: InputMaybe<FeesBehaviour>;
  flags?: InputMaybe<EventFlagsInput>;
  freeEvent?: InputMaybe<Scalars['Boolean']['input']>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  isTicketAvailableAtDoor?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  licenseNumber?: InputMaybe<Scalars['String']['input']>;
  lineup?: InputMaybe<Array<InputMaybe<LineupInput>>>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  lockVersion?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  manualValidationEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  marketeerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  maxTicketsLimit?: InputMaybe<Scalars['Int']['input']>;
  media?: InputMaybe<Array<InputMaybe<MediaItemInputObject>>>;
  musicbrainzArtists?: InputMaybe<Array<InputMaybe<MusicbrainzArtists>>>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotification?: InputMaybe<Scalars['Boolean']['input']>;
  onSaleNotificationAt?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotificationSmsContent?: InputMaybe<Scalars['String']['input']>;
  overriddenPromoterName?: InputMaybe<Scalars['String']['input']>;
  overrideFees?: InputMaybe<Scalars['Boolean']['input']>;
  permName?: InputMaybe<Scalars['String']['input']>;
  platformAccountCode?: InputMaybe<PlatformAccountCode>;
  postFanPriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  presentedBy?: InputMaybe<Scalars['String']['input']>;
  printedTicketFormat?: InputMaybe<PrintedTicketFormat>;
  products?: InputMaybe<Array<InputMaybe<ProductInput>>>;
  promoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  promoterStatusNotes?: InputMaybe<Scalars['String']['input']>;
  pwlWindow?: InputMaybe<Scalars['Int']['input']>;
  readAccessEmails?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  recurrentEventSchedule?: InputMaybe<RecurrentEventsScheduleInput>;
  relatedEventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  requiresBoxOfficeTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  requiresTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  restrictCountries?: InputMaybe<Array<InputMaybe<CountryCode>>>;
  restrictCountriesKind?: InputMaybe<RestrictionKind>;
  salesforceContractId?: InputMaybe<Scalars['ID']['input']>;
  scheduleStatus?: InputMaybe<ScheduleStatus>;
  seatingChannels?: InputMaybe<Array<InputMaybe<SeatingChannelInput>>>;
  sendReceiptViaSms?: InputMaybe<Scalars['Boolean']['input']>;
  showArtistDescription?: InputMaybe<ShowArtistDescription>;
  socialDistancingRulesetKey?: InputMaybe<Scalars['String']['input']>;
  stages?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  stripeAccountId?: InputMaybe<Scalars['String']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  taxSettings?: InputMaybe<TaxSettingsInput>;
  thirdPartySettingsId?: InputMaybe<Scalars['ID']['input']>;
  ticketPools?: InputMaybe<Array<InputMaybe<AttachTicketPoolInput>>>;
  ticketType?: InputMaybe<Scalars['String']['input']>;
  ticketTypes?: InputMaybe<Array<InputMaybe<TicketTypesInput>>>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  totalTickets?: InputMaybe<Scalars['Int']['input']>;
  venue?: InputMaybe<Scalars['String']['input']>;
  venueConfigurationId?: InputMaybe<Scalars['ID']['input']>;
  venueIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  venueName?: InputMaybe<Scalars['String']['input']>;
  venueSchedules?: InputMaybe<Array<InputMaybe<VenueScheduleInput>>>;
  venueSpaceId?: InputMaybe<Scalars['ID']['input']>;
  waitingListExchangeWindows?: InputMaybe<Array<InputMaybe<WaitingListExchangeWindowInput>>>;
};

export type CreateEventPayload = {
  __typename?: 'CreateEventPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Event>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateEventPromotionCodesInput = {
  amount: Scalars['Int']['input'];
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type CreateEventPromotionCodesPayload = {
  __typename?: 'CreateEventPromotionCodesPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventPromotion>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateEventPromotionInput = {
  accessType: PromotionAccessType;
  clientMutationId: Scalars['String']['input'];
  code?: InputMaybe<Scalars['String']['input']>;
  codesOperations?: InputMaybe<Array<InputMaybe<CodesOperationsInput>>>;
  discount?: InputMaybe<Scalars['Int']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventId: Scalars['ID']['input'];
  fanFacingName?: InputMaybe<Scalars['String']['input']>;
  maxRedemptions?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
  promotionType: PromotionType;
  seatsIoChannel?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
  ticketTypeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  unlockAfterExpired?: InputMaybe<Scalars['Boolean']['input']>;
};

export type CreateEventPromotionPayload = {
  __typename?: 'CreateEventPromotionPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventPromotion>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateEventSeatingChannelInput = {
  clientMutationId: Scalars['String']['input'];
  color: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  eventPromotionId?: InputMaybe<Scalars['ID']['input']>;
  name: Scalars['String']['input'];
  objects?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type CreateEventSeatingChannelPayload = {
  __typename?: 'CreateEventSeatingChannelPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<SeatsIoChannel>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateEventSeatingChartInput = {
  clientMutationId: Scalars['String']['input'];
  socialDistancingRulesetKey?: InputMaybe<Scalars['String']['input']>;
  venueChartId: Scalars['ID']['input'];
};

export type CreateEventSeatingChartPayload = {
  __typename?: 'CreateEventSeatingChartPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventSeatingChart>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateFacebookEventInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  pageId: Scalars['String']['input'];
  userAccessToken: Scalars['String']['input'];
};

export type CreateFacebookEventPayload = {
  __typename?: 'CreateFacebookEventPayload';
  clientMutationId: Scalars['String']['output'];
  facebookEventId?: Maybe<Scalars['ID']['output']>;
};

export type CreateFanConnectInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  message: Scalars['String']['input'];
  onBehalfOfUserId?: InputMaybe<Scalars['ID']['input']>;
  scheduledAt?: InputMaybe<Scalars['Time']['input']>;
  sendMeACopy?: InputMaybe<Scalars['Boolean']['input']>;
  ticketTypeIds: Array<InputMaybe<Scalars['ID']['input']>>;
  title: Scalars['String']['input'];
};

export type CreateFanConnectPayload = {
  __typename?: 'CreateFanConnectPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateFeeConfigurationInput = {
  allowedAdhocFeeTypes?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  basePriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  basePriceMode?: InputMaybe<BasePriceMode>;
  boxOfficeFee?: InputMaybe<Scalars['Int']['input']>;
  clientMutationId: Scalars['String']['input'];
  effectiveDate: Scalars['Time']['input'];
  endDate?: InputMaybe<Scalars['Time']['input']>;
  feesApplicationRules?: InputMaybe<Array<InputMaybe<FeesApplicationRuleInput>>>;
  itemId: Scalars['ID']['input'];
  postFanPriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  taxExempt: Scalars['Boolean']['input'];
  taxId?: InputMaybe<Scalars['String']['input']>;
};

export type CreateFeeConfigurationPayload = {
  __typename?: 'CreateFeeConfigurationPayload';
  clientMutationId: Scalars['String']['output'];
  feesConfiguration?: Maybe<FeesConfiguration>;
};

export type CreateHierarchicalTagInput = {
  clientMutationId: Scalars['String']['input'];
  deprecated?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  parentId: Scalars['ID']['input'];
};

export type CreateHierarchicalTagPayload = {
  __typename?: 'CreateHierarchicalTagPayload';
  clientMutationId: Scalars['String']['output'];
  hierarchicalTag?: Maybe<HierarchicalTag>;
};

export type CreateIntegrationTokenInput = {
  clientMutationId: Scalars['String']['input'];
};

export type CreateIntegrationTokenPayload = {
  __typename?: 'CreateIntegrationTokenPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateLabelInput = {
  checklists?: InputMaybe<Array<InputMaybe<ChecklistInput>>>;
  clientMutationId: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type CreateLabelPayload = {
  __typename?: 'CreateLabelPayload';
  clientMutationId: Scalars['String']['output'];
  label?: Maybe<Label>;
};

export type CreateLinkoutInput = {
  artistId?: InputMaybe<Scalars['ID']['input']>;
  cityIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  currency?: InputMaybe<Scalars['String']['input']>;
  date: Scalars['Time']['input'];
  destinationEventId?: InputMaybe<Scalars['ID']['input']>;
  endDate: Scalars['Time']['input'];
  externalUrl?: InputMaybe<Scalars['String']['input']>;
  imageAttachmentId?: InputMaybe<Scalars['ID']['input']>;
  imageCropRegion?: InputMaybe<CropRegionInput>;
  linkoutType: LinkoutType;
  name: Scalars['String']['input'];
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  price?: InputMaybe<Scalars['Int']['input']>;
  promoterId?: InputMaybe<Scalars['ID']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  timezone: Scalars['String']['input'];
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export type CreateLinkoutPayload = {
  __typename?: 'CreateLinkoutPayload';
  clientMutationId: Scalars['String']['output'];
  linkout?: Maybe<Linkout>;
};

export type CreateManualPayoutInput = {
  amount: Scalars['Int']['input'];
  clientMutationId: Scalars['String']['input'];
  date: Scalars['Time']['input'];
  details?: InputMaybe<Scalars['String']['input']>;
  eventId: Scalars['ID']['input'];
  isAdvance: Scalars['Boolean']['input'];
  means: PayoutMeans;
  reason: PayoutReason;
};

export type CreateManualPayoutPayload = {
  __typename?: 'CreateManualPayoutPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type CreateMarketeerInput = {
  appOptInEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  clientMutationId: Scalars['String']['input'];
  contacts?: InputMaybe<Array<InputMaybe<ContactsInput>>>;
  fbAccessToken?: InputMaybe<Scalars['String']['input']>;
  fbPixelId?: InputMaybe<Scalars['String']['input']>;
  gaTrackingId?: InputMaybe<Scalars['String']['input']>;
  googleAdsConversionId?: InputMaybe<Scalars['String']['input']>;
  googleAdsPurchaseConversionLabel?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  privacyPolicyLink?: InputMaybe<Scalars['String']['input']>;
  tiktokPixelId?: InputMaybe<Scalars['String']['input']>;
  twitterCheckoutInitiatedPixelId?: InputMaybe<Scalars['String']['input']>;
  twitterPixelId?: InputMaybe<Scalars['String']['input']>;
  twitterPurchasePixelId?: InputMaybe<Scalars['String']['input']>;
  webOptInEnabled?: InputMaybe<Scalars['Boolean']['input']>;
};

export type CreateMarketeerPayload = {
  __typename?: 'CreateMarketeerPayload';
  clientMutationId: Scalars['String']['output'];
  marketeer?: Maybe<Marketeer>;
};

export type CreateNotificationBatchInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  message: Scalars['String']['input'];
  notifyOn: Scalars['Time']['input'];
  reason: NotificationReason;
};

export type CreateNotificationBatchPayload = {
  __typename?: 'CreateNotificationBatchPayload';
  clientMutationId: Scalars['String']['output'];
  notificationBatch?: Maybe<NotificationBatch>;
};

export type CreateOrUpdateVenueAreaInput = {
  code?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type CreateOrUpdateVenueConfigurationInput = {
  attractiveRoomSiaeCode?: InputMaybe<Scalars['String']['input']>;
  capacity?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  seatingAreaConfigs?: InputMaybe<Array<InputMaybe<SeatingAreaConfigInput>>>;
};

export type CreatePayoutInput = {
  amount: Scalars['Int']['input'];
  clientMutationId: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  sendRemittanceEmail: Scalars['Boolean']['input'];
};

export type CreatePayoutPayload = {
  __typename?: 'CreatePayoutPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type CreatePermissionProfileInput = {
  accountId?: InputMaybe<Scalars['ID']['input']>;
  caption?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  subjects?: InputMaybe<Array<InputMaybe<SubjectInput>>>;
};

export type CreatePermissionProfilePayload = {
  __typename?: 'CreatePermissionProfilePayload';
  clientMutationId: Scalars['String']['output'];
  permissionProfile?: Maybe<PermissionProfile>;
};

export type CreateProductInput = {
  allTicketTypes?: InputMaybe<Scalars['Boolean']['input']>;
  allocation?: InputMaybe<Scalars['Int']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  categoryId?: InputMaybe<Scalars['ID']['input']>;
  clientMutationId: Scalars['String']['input'];
  customCover?: InputMaybe<Scalars['Boolean']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  excludedFromCrmAutomation?: InputMaybe<Scalars['Boolean']['input']>;
  faceValue?: InputMaybe<Scalars['Int']['input']>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  fulfilledBy?: InputMaybe<Scalars['String']['input']>;
  hasSeparateAccessBarcodes?: InputMaybe<Scalars['Boolean']['input']>;
  hasVariants?: InputMaybe<Scalars['Boolean']['input']>;
  locationNote?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  optionType?: InputMaybe<ProductOptionType>;
  productImages?: InputMaybe<Array<InputMaybe<ProductImageInput>>>;
  productType?: InputMaybe<ProductType>;
  purchaseConfirmationMessage?: InputMaybe<Scalars['String']['input']>;
  sellingPoints?: InputMaybe<Array<InputMaybe<SellingPointInput>>>;
  sku?: InputMaybe<Scalars['String']['input']>;
  variants?: InputMaybe<Array<InputMaybe<VariantInput>>>;
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export type CreateProductPayload = {
  __typename?: 'CreateProductPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Product>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateProductsListInput = {
  clientMutationId: Scalars['String']['input'];
  products?: InputMaybe<Array<InputMaybe<NewProductInput>>>;
};

export type CreateProductsListPayload = {
  __typename?: 'CreateProductsListPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ProductsList>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreatePromoterInput = {
  accountIban?: InputMaybe<Scalars['String']['input']>;
  accountManagerId?: InputMaybe<Scalars['ID']['input']>;
  accountManagerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  accountName?: InputMaybe<Scalars['String']['input']>;
  accountNumber?: InputMaybe<Scalars['String']['input']>;
  accountSortCode?: InputMaybe<Scalars['String']['input']>;
  accountType?: InputMaybe<Scalars['String']['input']>;
  accountVatNumber?: InputMaybe<Scalars['String']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  allowSkipReview?: InputMaybe<Scalars['Boolean']['input']>;
  apiToken?: InputMaybe<Scalars['String']['input']>;
  apiTokenExpiryDate?: InputMaybe<Scalars['Time']['input']>;
  associatedMarketeerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  autoRescheduledEventRefunds?: InputMaybe<AutoRescheduledEventRefundsInput>;
  automaticRollingPaymentsConfiguration?: InputMaybe<AutomaticRollingPaymentsConfigurationInput>;
  bankAddress?: InputMaybe<Scalars['String']['input']>;
  bankName?: InputMaybe<Scalars['String']['input']>;
  billingNotes?: InputMaybe<Scalars['String']['input']>;
  charityTaxFree?: InputMaybe<Scalars['Boolean']['input']>;
  clientMutationId: Scalars['String']['input'];
  clientSuccessManagerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  contacts?: InputMaybe<Array<InputMaybe<ContactsInput>>>;
  coolingOffPeriod?: InputMaybe<Scalars['Boolean']['input']>;
  coolingOffPeriodHours?: InputMaybe<Scalars['Int']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  curatedBundle?: InputMaybe<CuratedBundleInput>;
  dicePartner?: InputMaybe<Scalars['Boolean']['input']>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  disabledReason?: InputMaybe<EnumAccountDisabledReason>;
  displayName?: InputMaybe<Scalars['String']['input']>;
  eventDefaults?: InputMaybe<EventDefaultsInput>;
  extrasEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  fanSupportNotes?: InputMaybe<FanSupportNotesInput>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  forbidSelfPayouts?: InputMaybe<Scalars['Boolean']['input']>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  holdPayouts?: InputMaybe<Scalars['Boolean']['input']>;
  isDisabled?: InputMaybe<Scalars['Boolean']['input']>;
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  legalEntity?: InputMaybe<Scalars['String']['input']>;
  licenseNumber?: InputMaybe<Scalars['String']['input']>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  merchEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  permissionProfileOverrides?: InputMaybe<Array<InputMaybe<PermissionProfileOverrideInput>>>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  profileActive?: InputMaybe<Scalars['Boolean']['input']>;
  profileDetails?: InputMaybe<ProfileDetailsInput>;
  promoterTaxSettings?: InputMaybe<PromoterTaxSettingsInput>;
  qflowEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  resoldEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  routingNumber?: InputMaybe<Scalars['String']['input']>;
  salesforcePromoterFields?: InputMaybe<SalesforcePromoterFieldsInput>;
  sendReceiptViaSms?: InputMaybe<Scalars['Boolean']['input']>;
  showPriceBreakdown?: InputMaybe<Scalars['Boolean']['input']>;
  statusNotes?: InputMaybe<Scalars['String']['input']>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  stripeAccountId?: InputMaybe<Scalars['String']['input']>;
  stripeDocumentId?: InputMaybe<Scalars['String']['input']>;
  stripeFallbackAccountId?: InputMaybe<Scalars['String']['input']>;
  stripeFallbackPlatformCode?: InputMaybe<PlatformAccountCode>;
  stripeLocationId?: InputMaybe<Scalars['String']['input']>;
  stripeVerified?: InputMaybe<Scalars['Boolean']['input']>;
  swiftCode?: InputMaybe<Scalars['String']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  taxCode?: InputMaybe<Scalars['String']['input']>;
  ticketAgreementComplete?: InputMaybe<Scalars['Boolean']['input']>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  typeOfOrganizer?: InputMaybe<EnumTypeOfOrganizer>;
};

export type CreatePromoterPayload = {
  __typename?: 'CreatePromoterPayload';
  clientMutationId: Scalars['String']['output'];
  promoter?: Maybe<Promoter>;
};

export type CreateReportScheduleInput = {
  clientMutationId: Scalars['String']['input'];
  emailList: Array<InputMaybe<Scalars['EmailAddress']['input']>>;
  endAt?: InputMaybe<Scalars['Time']['input']>;
  eventId?: InputMaybe<Scalars['ID']['input']>;
  locale?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  options?: InputMaybe<ScheduledReportOptionsInput>;
  recurrence?: InputMaybe<RecurrenceInput>;
  reportType: ReportType;
  startAt: Scalars['Time']['input'];
  /** Time zone name for localized start_at time.  Defaults to user's saved timezone, or UTC, if not provided. */
  timezoneName?: InputMaybe<Scalars['String']['input']>;
};

export type CreateReportSchedulePayload = {
  __typename?: 'CreateReportSchedulePayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ReportSchedule>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateRestrictedIntegrationTokenInput = {
  clientMutationId: Scalars['String']['input'];
  eventId?: InputMaybe<Scalars['ID']['input']>;
  fullAccess?: InputMaybe<Scalars['Boolean']['input']>;
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export type CreateRestrictedIntegrationTokenPayload = {
  __typename?: 'CreateRestrictedIntegrationTokenPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateScheduledReportInput = {
  clientMutationId: Scalars['String']['input'];
  emailList: Array<InputMaybe<Scalars['EmailAddress']['input']>>;
  eventId?: InputMaybe<Scalars['ID']['input']>;
  locale?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  options?: InputMaybe<ScheduledReportOptionsInput>;
  reportType: ReportType;
  scheduledAt: Scalars['Time']['input'];
};

export type CreateScheduledReportPayload = {
  __typename?: 'CreateScheduledReportPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ScheduledReport>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateSeatingChartInput = {
  clientMutationId: Scalars['String']['input'];
  name: Scalars['String']['input'];
  venueId: Scalars['ID']['input'];
  venueType?: InputMaybe<SeatsIoChartType>;
};

export type CreateSeatingChartPayload = {
  __typename?: 'CreateSeatingChartPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<SeatingChart>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateSocialLinkInput = {
  campaign?: InputMaybe<Scalars['String']['input']>;
  channel?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  customUrl?: InputMaybe<Scalars['String']['input']>;
  diceLink?: InputMaybe<Scalars['Boolean']['input']>;
  eventId: Scalars['ID']['input'];
  postType?: InputMaybe<SocialLinkPostType>;
  sourceId?: InputMaybe<Scalars['ID']['input']>;
  ticketTypeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type CreateSocialLinkPayload = {
  __typename?: 'CreateSocialLinkPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type CreateTagInput = {
  clientMutationId: Scalars['String']['input'];
  deprecated?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
};

export type CreateTagPayload = {
  __typename?: 'CreateTagPayload';
  clientMutationId: Scalars['String']['output'];
  tag?: Maybe<Tag>;
};

export type CreateTempAttachmentInput = {
  clientMutationId: Scalars['String']['input'];
  format: Scalars['String']['input'];
};

export type CreateTempAttachmentPayload = {
  __typename?: 'CreateTempAttachmentPayload';
  attachment?: Maybe<TempAttachment>;
  clientMutationId: Scalars['String']['output'];
  presignedUploadUrl: Scalars['String']['output'];
};

export type CreateThirdPartySettingsInput = {
  appIcon: Scalars['String']['input'];
  appLink: Scalars['String']['input'];
  appName: Scalars['String']['input'];
  clientMutationId: Scalars['String']['input'];
  idVerification: Scalars['Boolean']['input'];
  promoterDisplayName?: InputMaybe<Scalars['String']['input']>;
  promoterId?: InputMaybe<Scalars['ID']['input']>;
  provideSecureUserAuth: Scalars['Boolean']['input'];
};

export type CreateThirdPartySettingsPayload = {
  __typename?: 'CreateThirdPartySettingsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ThirdPartySettings>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateTicketPoolInput = {
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  clientMutationId: Scalars['String']['input'];
  holds?: InputMaybe<Array<InputMaybe<HoldInput>>>;
  maxAllocation?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  ticketTypes?: InputMaybe<Array<InputMaybe<TicketTypesInput>>>;
  unlimitedAllocation?: InputMaybe<Scalars['Boolean']['input']>;
};

export type CreateTicketPoolPayload = {
  __typename?: 'CreateTicketPoolPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<TicketPool>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type CreateUserInput = {
  clientMutationId: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  mioRedesignV2?: InputMaybe<Scalars['Boolean']['input']>;
  mongoUserId?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  notificationPermissions?: InputMaybe<NotificationPermissionsInput>;
  permissionProfileId?: InputMaybe<Scalars['ID']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  position?: InputMaybe<Scalars['String']['input']>;
  preferredLanguage?: InputMaybe<Language>;
};

export type CreateUserPayload = {
  __typename?: 'CreateUserPayload';
  clientMutationId: Scalars['String']['output'];
  user?: Maybe<User>;
};

export type CreateVenueInput = {
  accessControl?: InputMaybe<Scalars['String']['input']>;
  accessibilityLink?: InputMaybe<Scalars['String']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  ageLimit?: InputMaybe<Scalars['String']['input']>;
  allocatedSeating?: InputMaybe<Scalars['String']['input']>;
  areas?: InputMaybe<Array<InputMaybe<CreateOrUpdateVenueAreaInput>>>;
  associatedPromoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  attractiveEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  attractiveRoomSiaeCode?: InputMaybe<Scalars['String']['input']>;
  attributes?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  barcodeType?: InputMaybe<Scalars['String']['input']>;
  capacity?: InputMaybe<Scalars['Int']['input']>;
  characteristicIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  cityId?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  configurations?: InputMaybe<Array<InputMaybe<CreateOrUpdateVenueConfigurationInput>>>;
  contactEmail?: InputMaybe<Scalars['String']['input']>;
  contactPhone?: InputMaybe<Scalars['String']['input']>;
  contacts?: InputMaybe<Array<InputMaybe<ContactsInput>>>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  curatedBundle?: InputMaybe<CuratedBundleInput>;
  dicePartner?: InputMaybe<Scalars['Boolean']['input']>;
  externalLinks?: InputMaybe<ExternalLinksInput>;
  facebookPageId?: InputMaybe<Scalars['String']['input']>;
  faqs?: InputMaybe<Array<InputMaybe<FaqInput>>>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  flags?: InputMaybe<VenueFlagsInput>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  hideCapacity?: InputMaybe<Scalars['Boolean']['input']>;
  isSecret?: InputMaybe<Scalars['Boolean']['input']>;
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  logoAttachmentId?: InputMaybe<Scalars['ID']['input']>;
  logoCropRegion?: InputMaybe<CropRegionInput>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  mobileTicketsEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  multizone?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  profileActive?: InputMaybe<Scalars['Boolean']['input']>;
  profileDetails?: InputMaybe<ProfileDetailsInput>;
  promoterAllocation?: InputMaybe<Scalars['Int']['input']>;
  qflowEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  scannerModel?: InputMaybe<Scalars['String']['input']>;
  scannerType?: InputMaybe<Scalars['String']['input']>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  ticketType?: InputMaybe<Scalars['String']['input']>;
  ticketValidation?: InputMaybe<Scalars['String']['input']>;
  ticketingPartner?: InputMaybe<Scalars['String']['input']>;
  tier?: InputMaybe<VenueTier>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<VenueType>;
  venueAllocation?: InputMaybe<Scalars['Int']['input']>;
  venueImages?: InputMaybe<Array<InputMaybe<VenueImageInput>>>;
  venueOwnerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  venueSpaces?: InputMaybe<Array<InputMaybe<VenueSpaceInput>>>;
};

export type CreateVenuePayload = {
  __typename?: 'CreateVenuePayload';
  clientMutationId: Scalars['String']['output'];
  venue?: Maybe<Venue>;
};

export type CreditBalance = {
  __typename?: 'CreditBalance';
  balance?: Maybe<Scalars['Int']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
};

export type CropRegion = {
  __typename?: 'CropRegion';
  height?: Maybe<Scalars['Int']['output']>;
  width?: Maybe<Scalars['Int']['output']>;
  x?: Maybe<Scalars['Int']['output']>;
  y?: Maybe<Scalars['Int']['output']>;
};

export type CropRegionInput = {
  height?: InputMaybe<Scalars['Int']['input']>;
  width?: InputMaybe<Scalars['Int']['input']>;
  x?: InputMaybe<Scalars['Int']['input']>;
  y?: InputMaybe<Scalars['Int']['input']>;
};

export type CumulativeExtrasPurchaseItem = {
  __typename?: 'CumulativeExtrasPurchaseItem';
  purchasedAt: Scalars['Time']['output'];
  totalPurchases: Scalars['Int']['output'];
};

export type CumulativeProductsPurchaseItem = {
  __typename?: 'CumulativeProductsPurchaseItem';
  purchasedAt: Scalars['Time']['output'];
  totalPurchases: Scalars['Int']['output'];
};

export type CuratedBundleInput = {
  bundleId?: InputMaybe<Scalars['ID']['input']>;
};

export type CuratedByPromoterInput = {
  promoterId?: InputMaybe<Scalars['ID']['input']>;
};

export type CuratedByVenueInput = {
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export type CurrencyBalance = {
  __typename?: 'CurrencyBalance';
  availableBalance: Scalars['Int']['output'];
  availableForPayout: Scalars['Int']['output'];
  currency: EventCostCurrency;
  pendingBalance: Scalars['Int']['output'];
};

export type DateRangeInput = {
  endDate?: InputMaybe<Scalars['Time']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
};

export type DayOfMonth = {
  __typename?: 'DayOfMonth';
  dayOfWeek: Scalars['Int']['output'];
  nthOfMonth: Scalars['Int']['output'];
};

export type DayOfMonthInput = {
  dayOfWeek: Scalars['Int']['input'];
  nthOfMonth: Scalars['Int']['input'];
};

export type DeadlineInput = {
  active: Scalars['Boolean']['input'];
  deadline?: InputMaybe<Scalars['Time']['input']>;
};

export type DeleteAdmissionConfigInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type DeleteAdmissionConfigPayload = {
  __typename?: 'DeleteAdmissionConfigPayload';
  admissionConfig?: Maybe<AdmissionConfig>;
  clientMutationId: Scalars['String']['output'];
};

export type DeleteEventPromotionCodesInput = {
  allCodes?: InputMaybe<Scalars['Boolean']['input']>;
  clientMutationId: Scalars['String']['input'];
  codes?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  filter?: InputMaybe<CodeLockFilter>;
  force?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['ID']['input'];
};

export type DeleteEventPromotionCodesPayload = {
  __typename?: 'DeleteEventPromotionCodesPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventPromotion>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type DeleteEventSeatingChannelInput = {
  clientMutationId: Scalars['String']['input'];
  key: Scalars['String']['input'];
};

export type DeleteEventSeatingChannelPayload = {
  __typename?: 'DeleteEventSeatingChannelPayload';
  clientMutationId: Scalars['String']['output'];
  successful: Scalars['Boolean']['output'];
};

export type DeleteFacebookEventInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
};

export type DeleteFacebookEventPayload = {
  __typename?: 'DeleteFacebookEventPayload';
  clientMutationId: Scalars['String']['output'];
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type DeleteIntegrationTokenInput = {
  clientMutationId: Scalars['String']['input'];
  token: Scalars['String']['input'];
};

export type DeleteIntegrationTokenPayload = {
  __typename?: 'DeleteIntegrationTokenPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type DeleteMailchimpSettingsInput = {
  clientMutationId: Scalars['String']['input'];
};

export type DeleteMailchimpSettingsPayload = {
  __typename?: 'DeleteMailchimpSettingsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type DeleteManualPayoutInput = {
  clientMutationId: Scalars['String']['input'];
  payoutId: Scalars['ID']['input'];
};

export type DeleteManualPayoutPayload = {
  __typename?: 'DeleteManualPayoutPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type DeleteNodeInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type DeleteNodePayload = {
  __typename?: 'DeleteNodePayload';
  clientMutationId: Scalars['String']['output'];
  id: Scalars['ID']['output'];
};

export type DeleteReportScheduleInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type DeleteReportSchedulePayload = {
  __typename?: 'DeleteReportSchedulePayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ReportSchedule>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type DeleteScheduledReportInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type DeleteScheduledReportPayload = {
  __typename?: 'DeleteScheduledReportPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ScheduledReport>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type DeleteTicketPoolInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type DeleteTicketPoolPayload = {
  __typename?: 'DeleteTicketPoolPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<TicketPool>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type DestinationAccount = Node & {
  __typename?: 'DestinationAccount';
  accountId?: Maybe<Scalars['String']['output']>;
  balances: Array<Maybe<CurrencyBalance>>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  region?: Maybe<Scalars['String']['output']>;
  stripeOauthUrl?: Maybe<Scalars['String']['output']>;
};

export enum DiceRefundReason {
  ACCESSIBILITY_REQUEST = 'ACCESSIBILITY_REQUEST',
  ACCIDENTAL_PURCHASE = 'ACCIDENTAL_PURCHASE',
  APP_TECH_ISSUE = 'APP_TECH_ISSUE',
  DISPUTED_TRANSACTION = 'DISPUTED_TRANSACTION',
  EVENT_BUILD_ERROR_DICE = 'EVENT_BUILD_ERROR_DICE',
  EVENT_BUILD_ERROR_PARTNER = 'EVENT_BUILD_ERROR_PARTNER',
  EVENT_COMPLAINT = 'EVENT_COMPLAINT',
  EVENT_POSTPONED_RESCHEDULED = 'EVENT_POSTPONED_RESCHEDULED',
  FRAUD_PREVENTION = 'FRAUD_PREVENTION',
  OTHER = 'OTHER',
  PROMOTER_REQUEST = 'PROMOTER_REQUEST',
  SUPPORT_DISCRETION = 'SUPPORT_DISCRETION',
  SUSPECTED_RESELLER = 'SUSPECTED_RESELLER',
  SWAP_REQUEST = 'SWAP_REQUEST',
  VENUE_COMPLAINT = 'VENUE_COMPLAINT'
}

export type DisableFanSurveyInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
};

export type DisableFanSurveyPayload = {
  __typename?: 'DisableFanSurveyPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Event>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type DisableSeatingChartInput = {
  clientMutationId: Scalars['String']['input'];
  seatingChartId: Scalars['ID']['input'];
};

export type DisableSeatingChartPayload = {
  __typename?: 'DisableSeatingChartPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<SeatingChart>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export enum DistanceUnits {
  kilometers = 'kilometers',
  miles = 'miles'
}

export type DoorSalesActivity = Node & {
  __typename?: 'DoorSalesActivity';
  allowedActions?: Maybe<DoorSalesAllowedActions>;
  author?: Maybe<User>;
  charge: DoorSalesCharge;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  insertedAt: Scalars['Time']['output'];
  items: Array<Maybe<DoorSalesActivityItem>>;
  warning?: Maybe<DoorSalesWarning>;
};

export type DoorSalesActivityConnection = {
  __typename?: 'DoorSalesActivityConnection';
  edges?: Maybe<Array<Maybe<DoorSalesActivityEdge>>>;
  pageInfo: PageInfo;
};

export type DoorSalesActivityEdge = {
  __typename?: 'DoorSalesActivityEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<DoorSalesActivity>;
};

export type DoorSalesActivityItem = {
  __typename?: 'DoorSalesActivityItem';
  code?: Maybe<Scalars['String']['output']>;
  price?: Maybe<Scalars['Int']['output']>;
  salesTax?: Maybe<Scalars['Int']['output']>;
  seatName?: Maybe<Scalars['String']['output']>;
  state?: Maybe<DoorSalesTicketState>;
  ticketId: Scalars['ID']['output'];
  updatedAt: Scalars['Time']['output'];
};

/** Actions the user can make to the object */
export type DoorSalesAllowedActions = {
  __typename?: 'DoorSalesAllowedActions';
  /** Edit the activity (adjust payment type) */
  edit?: Maybe<Scalars['Boolean']['output']>;
};

export type DoorSalesCharge = Node & {
  __typename?: 'DoorSalesCharge';
  cardLastDigits: Scalars['String']['output'];
  event: Event;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  paymentType: DoorSalesPaymentType;
  printableReceipt?: Maybe<Scalars['String']['output']>;
  ticketType: TicketType;
};

export type DoorSalesChargeInput = {
  /** For box office sales with p400 terminal */
  chargeId?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  /** For box office sales with p400 terminal */
  paymentIntentId?: InputMaybe<Scalars['String']['input']>;
  paymentType: DoorSalesPaymentType;
  /** FAN # to assign tickets to */
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
  ticketHolderNames?: InputMaybe<Array<InputMaybe<HolderName>>>;
  ticketIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type DoorSalesChargePayload = {
  __typename?: 'DoorSalesChargePayload';
  clientMutationId: Scalars['String']['output'];
  event: Event;
  newActivityEdge: DoorSalesActivityEdge;
  printableTickets?: Maybe<Array<Maybe<DoorSalesPrintableTicket>>>;
};

export type DoorSalesCreateReaderInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  label?: InputMaybe<Scalars['String']['input']>;
  registrationCode: Scalars['String']['input'];
};

export type DoorSalesCreateReaderPayload = {
  __typename?: 'DoorSalesCreateReaderPayload';
  clientMutationId: Scalars['String']['output'];
  reader?: Maybe<StripeReader>;
};

export type DoorSalesEditInput = {
  activityId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
  paymentType: DoorSalesPaymentType;
};

export type DoorSalesEditPayload = {
  __typename?: 'DoorSalesEditPayload';
  activity: DoorSalesActivity;
  clientMutationId: Scalars['String']['output'];
};

export type DoorSalesExport = {
  __typename?: 'DoorSalesExport';
  downloadUrl?: Maybe<Scalars['String']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  s3Path?: Maybe<Scalars['String']['output']>;
  startDate?: Maybe<Scalars['Time']['output']>;
};

export type DoorSalesExportConnection = {
  __typename?: 'DoorSalesExportConnection';
  edges?: Maybe<Array<Maybe<DoorSalesExportEdge>>>;
  pageInfo: PageInfo;
};

export type DoorSalesExportEdge = {
  __typename?: 'DoorSalesExportEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<DoorSalesExport>;
};

export enum DoorSalesPaymentType {
  CARD = 'CARD',
  CASH = 'CASH',
  MIXED = 'MIXED',
  NO_PAYMENT = 'NO_PAYMENT'
}

export type DoorSalesPrintableTicket = {
  __typename?: 'DoorSalesPrintableTicket';
  /** @deprecated To be removed, use legalDetails instead */
  activationCard?: Maybe<Scalars['String']['output']>;
  charge: DoorSalesCharge;
  code?: Maybe<Scalars['String']['output']>;
  /** @deprecated To be removed, use legalDetails instead */
  emissionDate?: Maybe<Scalars['String']['output']>;
  /** @deprecated To be removed, use legalDetails instead */
  fiscalSeal?: Maybe<Scalars['String']['output']>;
  holder?: Maybe<TicketHolder>;
  id: Scalars['String']['output'];
  legalDetails?: Maybe<PrintableTicketLegalDetails>;
  price: Scalars['Int']['output'];
  printableReceipt?: Maybe<Scalars['String']['output']>;
  /** @deprecated To be removed, use legalDetails instead */
  progressiveNumber?: Maybe<Scalars['String']['output']>;
  seatLabels?: Maybe<SeatLabel>;
  /** @deprecated Use seatLabels instead */
  seatName?: Maybe<Scalars['String']['output']>;
};

export type DoorSalesRefundInput = {
  clientMutationId: Scalars['String']['input'];
  ticketIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type DoorSalesRefundPayload = {
  __typename?: 'DoorSalesRefundPayload';
  activity: DoorSalesActivity;
  clientMutationId: Scalars['String']['output'];
};

export type DoorSalesReprintInput = {
  activityId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
};

export type DoorSalesReprintPayload = {
  __typename?: 'DoorSalesReprintPayload';
  clientMutationId: Scalars['String']['output'];
  printableTickets?: Maybe<Array<Maybe<DoorSalesPrintableTicket>>>;
};

export type DoorSalesReservation = {
  __typename?: 'DoorSalesReservation';
  expiresAt: Scalars['Time']['output'];
  tickets?: Maybe<Array<Maybe<DoorSalesReservedTicket>>>;
};

export type DoorSalesReserveInput = {
  amount: Scalars['Int']['input'];
  channelIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  ticketTypeId: Scalars['ID']['input'];
};

export type DoorSalesReservePayload = {
  __typename?: 'DoorSalesReservePayload';
  clientMutationId: Scalars['String']['output'];
  reservation: DoorSalesReservation;
};

export type DoorSalesReservedTicket = {
  __typename?: 'DoorSalesReservedTicket';
  id: Scalars['ID']['output'];
  price: Scalars['Int']['output'];
};

export type DoorSalesRevenue = {
  __typename?: 'DoorSalesRevenue';
  byPaymentType: Array<Maybe<DoorSalesRevenueItem>>;
  total: Scalars['Int']['output'];
};

export type DoorSalesRevenueItem = {
  __typename?: 'DoorSalesRevenueItem';
  paymentType: DoorSalesPaymentType;
  value: Scalars['Int']['output'];
};

export type DoorSalesSendReceiptInput = {
  activityId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
  phoneNumber: Scalars['PhoneNumber']['input'];
};

export type DoorSalesSendReceiptPayload = {
  __typename?: 'DoorSalesSendReceiptPayload';
  clientMutationId: Scalars['String']['output'];
  receiptUrl?: Maybe<Scalars['String']['output']>;
};

export type DoorSalesStats = {
  __typename?: 'DoorSalesStats';
  posRevenue: Scalars['Int']['output'];
  posSold: Scalars['Int']['output'];
  soldOrReserved: Scalars['Int']['output'];
};

export enum DoorSalesTicketState {
  ACTIVE = 'ACTIVE',
  REFUNDED = 'REFUNDED'
}

export type DoorSalesUnreserveInput = {
  clientMutationId: Scalars['String']['input'];
  ticketIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type DoorSalesUnreservePayload = {
  __typename?: 'DoorSalesUnreservePayload';
  clientMutationId: Scalars['String']['output'];
  ok?: Maybe<Scalars['Boolean']['output']>;
};

export type DoorSalesValidateFanInput = {
  amount: Scalars['Int']['input'];
  clientMutationId: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
  ticketTypeId: Scalars['ID']['input'];
};

export type DoorSalesValidateFanPayload = {
  __typename?: 'DoorSalesValidateFanPayload';
  clientMutationId: Scalars['String']['output'];
  smsCode?: Maybe<Scalars['String']['output']>;
};

export type DoorSalesWarning = {
  __typename?: 'DoorSalesWarning';
  message?: Maybe<Scalars['String']['output']>;
  ticketCodes?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export enum DoorlistDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

export type DoorlistEntry = {
  __typename?: 'DoorlistEntry';
  address?: Maybe<FanAddress>;
  chargeKinds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  checkId?: Maybe<Scalars['String']['output']>;
  codes?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  fan?: Maybe<Fan>;
  holders?: Maybe<Array<Maybe<TicketHolder>>>;
  itemType: DoorlistEntryItemType;
  legalDetails?: Maybe<Array<Maybe<PrintableTicketLegalDetails>>>;
  lineItemIds?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  numberOfTickets: Scalars['Int']['output'];
  prices?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  product?: Maybe<Product>;
  purchaseId: Scalars['Int']['output'];
  purchasedAt: Scalars['Time']['output'];
  seatLabels?: Maybe<Array<Maybe<SeatLabel>>>;
  /** @deprecated Use seatLabels instead */
  seatNames?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  ticketIds?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  ticketType: TicketType;
  variant?: Maybe<Variant>;
};

export type DoorlistEntryConnection = {
  __typename?: 'DoorlistEntryConnection';
  edges?: Maybe<Array<Maybe<DoorlistEntryEdge>>>;
  pageInfo: PageInfo;
};

export type DoorlistEntryEdge = {
  __typename?: 'DoorlistEntryEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<DoorlistEntry>;
};

export enum DoorlistEntryItemType {
  EXTRAS = 'EXTRAS',
  MERCH = 'MERCH',
  TICKETS = 'TICKETS'
}

export enum DoorlistType {
  ALL = 'ALL',
  EXTRAS = 'EXTRAS',
  MERCH = 'MERCH',
  TICKETS = 'TICKETS'
}

export type DropHoldPayoutsInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type DropHoldPayoutsPayload = {
  __typename?: 'DropHoldPayoutsPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type DuplicateEventInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type DuplicateEventPayload = {
  __typename?: 'DuplicateEventPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
  sourceEvent?: Maybe<Event>;
};

export type Email = {
  __typename?: 'Email';
  email: Scalars['String']['output'];
  status: EmailStatus;
  updatedAt: Scalars['Time']['output'];
  uuid: Scalars['String']['output'];
};

export enum EmailStatus {
  FAILED = 'FAILED',
  READY = 'READY',
  SENT = 'SENT',
  WAITING = 'WAITING'
}

export type EnableFanSurveyInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
};

export type EnableFanSurveyPayload = {
  __typename?: 'EnableFanSurveyPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Event>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type EnabledPwlInput = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
  deadline?: InputMaybe<Scalars['Time']['input']>;
  increaseAmount?: InputMaybe<Scalars['Int']['input']>;
};

export type EndEventPromotionInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type EndEventPromotionPayload = {
  __typename?: 'EndEventPromotionPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventPromotion>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export enum EnumAccountDisabledReason {
  INACTIVITY = 'INACTIVITY',
  INACTIVITY_AD_LEAD = 'INACTIVITY_AD_LEAD',
  TERMINATION = 'TERMINATION'
}

export enum EnumAccountUserInvitaionStatus {
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  PENDING = 'PENDING'
}

export enum EnumFeeTarget {
  extras = 'extras',
  merch = 'merch',
  tickets = 'tickets'
}

export enum EnumTypeOfOrganizer {
  AGENT = 'AGENT',
  ARTIST = 'ARTIST',
  ARTIST_MANAGEMENT = 'ARTIST_MANAGEMENT',
  BRAND = 'BRAND',
  LABEL = 'LABEL',
  PROMOTER = 'PROMOTER',
  VENUE = 'VENUE'
}

/** Gig event */
export type Event = ChecklistsNode & Fees & Name & Node & TimeStamps & VenueAddress & {
  __typename?: 'Event';
  /** @deprecated To be removed, please use viewer.activities instead */
  activities?: Maybe<ActivitiesConnection>;
  additionalArtists?: Maybe<Array<Maybe<AdditionalArtist>>>;
  additionalInfos?: Maybe<Array<Maybe<EventAdditionalInfo>>>;
  addressCapacity?: Maybe<Scalars['Int']['output']>;
  addressCountry?: Maybe<Scalars['String']['output']>;
  addressLocality?: Maybe<Scalars['String']['output']>;
  addressRegion?: Maybe<Scalars['String']['output']>;
  addressSiaeCode?: Maybe<Scalars['String']['output']>;
  addressState?: Maybe<Scalars['String']['output']>;
  adjustmentsStats?: Maybe<AdjustmentsStats>;
  admissionConfigs?: Maybe<Array<Maybe<AdmissionConfig>>>;
  ageLimit?: Maybe<Scalars['String']['output']>;
  allocation: Scalars['Int']['output'];
  allowedActions?: Maybe<AllowedActions>;
  allowedLifecycleUpdates?: Maybe<AllowedLifecycleUpdates>;
  announceDate?: Maybe<Scalars['Time']['output']>;
  /** @deprecated Legacy */
  artists?: Maybe<Array<Maybe<Artist>>>;
  attractiveErrors?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  attractiveFields?: Maybe<AttractiveFields>;
  attractiveStatus?: Maybe<IntegrationStatus>;
  automatedUserComms?: Maybe<Scalars['Boolean']['output']>;
  autopaymentFailed?: Maybe<Scalars['Boolean']['output']>;
  balance?: Maybe<EventBalance>;
  barcodeType?: Maybe<Scalars['String']['output']>;
  basePriceFees?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  billingNotes?: Maybe<Scalars['String']['output']>;
  billingPromoter?: Maybe<Promoter>;
  boxOfficeLocationId?: Maybe<Scalars['String']['output']>;
  boxOfficeTerminalSupported?: Maybe<Scalars['Boolean']['output']>;
  boxOfficeTerminalToken?: Maybe<Scalars['String']['output']>;
  bundles?: Maybe<Array<Maybe<Bundle>>>;
  cancelledAt?: Maybe<Scalars['Time']['output']>;
  characteristics?: Maybe<Array<Maybe<Characteristic>>>;
  charityEvent?: Maybe<Scalars['Boolean']['output']>;
  charityId?: Maybe<Scalars['String']['output']>;
  checklists: Array<Maybe<Checklist>>;
  cities?: Maybe<Array<Maybe<City>>>;
  closeEventDate?: Maybe<Scalars['Time']['output']>;
  codeLocks?: Maybe<CodeLockConnection>;
  colour?: Maybe<Scalars['Map']['output']>;
  completedSteps?: Maybe<Scalars['Int']['output']>;
  contacts?: Maybe<Scalars['Map']['output']>;
  costAmount?: Maybe<Scalars['Int']['output']>;
  costCurrency?: Maybe<EventCostCurrency>;
  countryCode?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['Time']['output']>;
  createdBy?: Maybe<ThinUserProfile>;
  /** @deprecated Please use cumulative_products_purchases instead */
  cumulativeExtrasPurchases?: Maybe<Array<Maybe<CumulativeExtrasPurchaseItem>>>;
  cumulativeProductsPurchases?: Maybe<Array<Maybe<CumulativeProductsPurchaseItem>>>;
  cumulativePurchasesJson?: Maybe<Scalars['CumulativePurchasesAsJson']['output']>;
  date?: Maybe<Scalars['Time']['output']>;
  deeplinkUrl?: Maybe<Scalars['String']['output']>;
  defaultEventTimings?: Maybe<Array<Maybe<EventTiming>>>;
  description?: Maybe<Scalars['String']['output']>;
  diceStatusNotes?: Maybe<Scalars['String']['output']>;
  diceStreamDuration?: Maybe<Scalars['Int']['output']>;
  diceStreamDvrEnabled?: Maybe<Scalars['Boolean']['output']>;
  diceStreamRewatchEnabledUntil?: Maybe<Scalars['Time']['output']>;
  /** @deprecated unuseds */
  diceTv?: Maybe<Scalars['Boolean']['output']>;
  diceTvPlatform?: Maybe<TvPlatform>;
  disableUsTax?: Maybe<Scalars['Boolean']['output']>;
  doorSalesActivities?: Maybe<DoorSalesActivityConnection>;
  doorSalesRevenue?: Maybe<DoorSalesRevenue>;
  doorlist?: Maybe<DoorlistEntryConnection>;
  doorlistAdditionalRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  doorlistJson?: Maybe<Scalars['Map']['output']>;
  doorlistRecipients?: Maybe<Array<Maybe<User>>>;
  doorlistSendAt?: Maybe<Scalars['Time']['output']>;
  doorlistSendStatus?: Maybe<Scalars['String']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  eventArtists?: Maybe<Array<Maybe<EventArtist>>>;
  eventCompareLive?: Maybe<EventCompare>;
  eventComparePreview?: Maybe<EventCompare>;
  eventIdLive?: Maybe<Scalars['String']['output']>;
  eventIdPreview?: Maybe<Scalars['String']['output']>;
  eventImages?: Maybe<Array<Maybe<EventImage>>>;
  eventLive?: Maybe<BackendEvent>;
  eventLoadPredictions?: Maybe<Array<Maybe<EventLoadPrediction>>>;
  eventPreview?: Maybe<BackendEvent>;
  eventPromotions?: Maybe<Array<Maybe<EventPromotion>>>;
  eventReview?: Maybe<EventReview>;
  eventRules?: Maybe<EventRules>;
  eventSeatingChart?: Maybe<EventSeatingChart>;
  eventSerialised?: Maybe<Scalars['Map']['output']>;
  eventSharingObjects?: Maybe<Array<Maybe<EventSharingObject>>>;
  eventType?: Maybe<EventType>;
  extraNotes?: Maybe<Scalars['String']['output']>;
  facebookEventId?: Maybe<Scalars['String']['output']>;
  fanFacingPromoters?: Maybe<Array<Maybe<Promoter>>>;
  fanQuestions?: Maybe<Array<Maybe<FanQuestion>>>;
  fanSupportNotes?: Maybe<FanSupportNotes>;
  fansStats?: Maybe<FansStats>;
  faqs?: Maybe<Array<Maybe<Faq>>>;
  featuredAreas?: Maybe<Array<Maybe<FeaturedArea>>>;
  fees?: Maybe<Array<Maybe<Fee>>>;
  feesBehaviour?: Maybe<FeesBehaviour>;
  flags?: Maybe<EventFlags>;
  freeEvent?: Maybe<Scalars['Boolean']['output']>;
  fullAddress?: Maybe<Scalars['String']['output']>;
  guestList?: Maybe<GuestList>;
  hierarchicalTags?: Maybe<Array<Maybe<HierarchicalTag>>>;
  holdPayouts?: Maybe<Scalars['Boolean']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  isAttractive: Scalars['Boolean']['output'];
  isTest?: Maybe<Scalars['Boolean']['output']>;
  isTicketAvailableAtDoor?: Maybe<Scalars['Boolean']['output']>;
  labels?: Maybe<Array<Maybe<Label>>>;
  latitude?: Maybe<Scalars['Float']['output']>;
  licenseNumber?: Maybe<Scalars['String']['output']>;
  lineup?: Maybe<Array<Maybe<Scalars['Map']['output']>>>;
  linkStats?: Maybe<Array<Maybe<LinkStats>>>;
  links: Array<Maybe<Link>>;
  locationStats: LocationStats;
  lockVersion: Scalars['Int']['output'];
  longitude?: Maybe<Scalars['Float']['output']>;
  manualValidationEnabled?: Maybe<Scalars['Boolean']['output']>;
  marketeers?: Maybe<Array<Maybe<Marketeer>>>;
  marketingLinks?: Maybe<Array<Maybe<SocialLink>>>;
  maxTicketsLimit?: Maybe<Scalars['Int']['output']>;
  media?: Maybe<Array<Maybe<MediaItem>>>;
  name?: Maybe<Scalars['String']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  notificationBatches?: Maybe<Array<Maybe<NotificationBatch>>>;
  notifyCancelOn?: Maybe<Scalars['Time']['output']>;
  offSaleDate?: Maybe<Scalars['Time']['output']>;
  offSaleSentAt?: Maybe<Scalars['Time']['output']>;
  offSaleSentStatus?: Maybe<Scalars['String']['output']>;
  onSaleDate?: Maybe<Scalars['Time']['output']>;
  onSaleNotification?: Maybe<Scalars['Boolean']['output']>;
  onSaleNotificationAt?: Maybe<Scalars['Time']['output']>;
  /** @deprecated unused */
  onSaleNotificationSmsContent?: Maybe<Scalars['String']['output']>;
  onSaleNotificationStatus?: Maybe<Scalars['Boolean']['output']>;
  onSaleSmsReminders: Scalars['Int']['output'];
  organicSocialLink?: Maybe<Scalars['String']['output']>;
  overriddenPromoterName?: Maybe<Scalars['String']['output']>;
  /** @deprecated Legacy */
  overrideFees?: Maybe<Scalars['Boolean']['output']>;
  payout?: Maybe<EventPayout>;
  permName?: Maybe<Scalars['String']['output']>;
  platformAccountCode?: Maybe<PlatformAccountCode>;
  postFanPriceFees?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  postOfficeBoxNumber?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  presentedBy?: Maybe<Scalars['String']['output']>;
  previewToken?: Maybe<Scalars['String']['output']>;
  /** @deprecated Not used */
  price?: Maybe<Price>;
  prices?: Maybe<PriceRange>;
  primaryVenue?: Maybe<Venue>;
  printedTicketFormat?: Maybe<PrintedTicketFormat>;
  products?: Maybe<Array<Maybe<Product>>>;
  productsSales?: Maybe<ProductsSales>;
  promoterStatusNotes?: Maybe<Scalars['String']['output']>;
  promoters?: Maybe<Array<Maybe<Promoter>>>;
  pwlWindow?: Maybe<Scalars['Int']['output']>;
  qflowId?: Maybe<Scalars['String']['output']>;
  qflowLastIntegratedAt?: Maybe<Scalars['Time']['output']>;
  readAccessEmails?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  recurrentEventSchedule?: Maybe<RecurrentEventsScheduleConfig>;
  recurrentEventsGroup?: Maybe<Array<Maybe<Event>>>;
  relatedEvents?: Maybe<Array<Maybe<Event>>>;
  repsRequired?: Maybe<Scalars['String']['output']>;
  requiresBoxOfficeTicketNomination?: Maybe<Scalars['Boolean']['output']>;
  requiresTicketNomination?: Maybe<Scalars['Boolean']['output']>;
  restrictCountries?: Maybe<Array<Maybe<CountryCode>>>;
  restrictCountriesKind?: Maybe<RestrictionKind>;
  sales?: Maybe<Sales>;
  salesforceContract?: Maybe<SalesforceContract>;
  savedCount: Scalars['Int']['output'];
  scheduleStatus?: Maybe<ScheduleStatus>;
  scheduleStatusUpdatedAt?: Maybe<Scalars['Time']['output']>;
  seatingChannels?: Maybe<Array<Maybe<SeatsIoChannelWithObjects>>>;
  selfPayoutBlockers?: Maybe<Scalars['Map']['output']>;
  sendReceiptViaSms?: Maybe<Scalars['Boolean']['output']>;
  showArtistDescription?: Maybe<ShowArtistDescription>;
  socialLinks?: Maybe<Array<Maybe<SocialLink>>>;
  soldToday: Scalars['Int']['output'];
  specialConsiderations?: Maybe<Scalars['String']['output']>;
  stages?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  state?: Maybe<EventState>;
  statusAsOfNow?: Maybe<Scalars['String']['output']>;
  streamDetails?: Maybe<StreamDetails>;
  streamStats?: Maybe<StreamStats>;
  streetAddress?: Maybe<Scalars['String']['output']>;
  stripeAccountId?: Maybe<Scalars['String']['output']>;
  submittedAt?: Maybe<Scalars['Time']['output']>;
  tags?: Maybe<Array<Maybe<Tag>>>;
  taxRates?: Maybe<Array<Maybe<TaxRate>>>;
  taxSettings?: Maybe<TaxSettings>;
  thirdPartySettings?: Maybe<ThirdPartySettings>;
  ticketNominationFields?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  ticketPools?: Maybe<Array<Maybe<TicketPool>>>;
  ticketType?: Maybe<Scalars['String']['output']>;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  timezoneName?: Maybe<Scalars['String']['output']>;
  tokens?: Maybe<TokensList>;
  totalTickets?: Maybe<Scalars['Int']['output']>;
  updatedAt?: Maybe<Scalars['Time']['output']>;
  venue?: Maybe<Scalars['String']['output']>;
  venueConfiguration?: Maybe<VenueConfiguration>;
  venueName?: Maybe<Scalars['String']['output']>;
  venueSchedules?: Maybe<Array<Maybe<VenueSchedule>>>;
  venueSpace?: Maybe<VenueSpace>;
  venues?: Maybe<Array<Maybe<Venue>>>;
  waitingList?: Maybe<Array<Maybe<WaitingListEntry>>>;
  waitingListExchangeWindows?: Maybe<Array<Maybe<WaitingListExchangeWindow>>>;
};


/** Gig event */
export type EventActivitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<ActivityWhereInput>;
};


/** Gig event */
export type EventCodeLocksArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  code?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<CodeLockStatus>;
  ticketTypesIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  validFrom?: InputMaybe<Scalars['Time']['input']>;
  validTo?: InputMaybe<Scalars['Time']['input']>;
};


/** Gig event */
export type EventCumulativeProductsPurchasesArgs = {
  rootType?: InputMaybe<ProductRootType>;
};


/** Gig event */
export type EventDoorSalesActivitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  onlyMy?: InputMaybe<Scalars['Boolean']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


/** Gig event */
export type EventDoorlistArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  doorlistDirection?: InputMaybe<DoorlistDirection>;
  doorlistType?: InputMaybe<DoorlistType>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
};


/** Gig event */
export type EventStreamStatsArgs = {
  streamType: StreamType;
};


/** Gig event */
export type EventTicketTypesArgs = {
  doorSalesOnly?: InputMaybe<Scalars['Boolean']['input']>;
  includeArchived?: InputMaybe<Scalars['Boolean']['input']>;
};

export type EventAdditionalInfo = {
  __typename?: 'EventAdditionalInfo';
  content?: Maybe<Scalars['String']['output']>;
  ctaLabel?: Maybe<Scalars['String']['output']>;
  ctaLink?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  includeOnPurchaseEmail?: Maybe<Scalars['Boolean']['output']>;
  includeOnReminderEmail?: Maybe<Scalars['Boolean']['output']>;
};

export type EventAdditionalInfoInput = {
  content?: InputMaybe<Scalars['String']['input']>;
  ctaLabel?: InputMaybe<Scalars['String']['input']>;
  ctaLink?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  includeOnPurchaseEmail?: InputMaybe<Scalars['Boolean']['input']>;
  includeOnReminderEmail?: InputMaybe<Scalars['Boolean']['input']>;
};

export type EventArtist = {
  __typename?: 'EventArtist';
  artist?: Maybe<Artist>;
  description?: Maybe<Scalars['String']['output']>;
  headliner?: Maybe<Scalars['Boolean']['output']>;
  orderNumber?: Maybe<Scalars['Int']['output']>;
};

export type EventArtistInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  headliner?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  musicbrainzArtistId?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type EventBalance = {
  __typename?: 'EventBalance';
  account?: Maybe<AccountBalance>;
  amountPromoterOwed: Scalars['Int']['output'];
  amountPromoterTotal: Scalars['Int']['output'];
  availability: EventBalanceAvailability;
  credits?: Maybe<Array<Maybe<EventCredit>>>;
  feesSum: Array<Maybe<FeesSum>>;
  isProcessing: Scalars['Boolean']['output'];
  payouts?: Maybe<Array<Maybe<Payout>>>;
  payoutsTotal: Scalars['Int']['output'];
  productBalance?: Maybe<ProductBalance>;
  splitSum?: Maybe<Array<Maybe<PriceSplit>>>;
  tiers?: Maybe<Array<Maybe<EventBalanceTier>>>;
  totalSold?: Maybe<Scalars['Int']['output']>;
  totalSum?: Maybe<Scalars['Int']['output']>;
  totalsByMethod?: Maybe<Array<Maybe<MethodPair>>>;
};

export enum EventBalanceAvailability {
  AVAILABLE = 'AVAILABLE',
  INSUFFICIENT = 'INSUFFICIENT',
  PENDING = 'PENDING'
}

export type EventBalanceTier = {
  __typename?: 'EventBalanceTier';
  count?: Maybe<Scalars['Int']['output']>;
  faceValue?: Maybe<Scalars['Int']['output']>;
  paymentMethod?: Maybe<PaymentMethod>;
};

export type EventChangedNotificationChangeset = {
  date?: InputMaybe<Scalars['Time']['input']>;
  eventVenues?: InputMaybe<Array<InputMaybe<EventVenues>>>;
  flags?: InputMaybe<EventFlagsInput>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  lineup?: InputMaybe<Array<InputMaybe<LineupInput>>>;
  scheduleStatus?: InputMaybe<ScheduleStatus>;
};

export type EventCompare = {
  __typename?: 'EventCompare';
  backendEvent?: Maybe<BackendEvent>;
  backendEventId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<EventCompareStatus>;
  values?: Maybe<Array<Maybe<EventCompateResultValue>>>;
};

export enum EventCompareStatus {
  approx = 'approx',
  broken = 'broken',
  eq = 'eq',
  notEq = 'notEq',
  notFound = 'notFound',
  unset = 'unset'
}

export type EventCompateResultValue = {
  __typename?: 'EventCompateResultValue';
  key: Scalars['String']['output'];
  source?: Maybe<Scalars['Map']['output']>;
  status: EventCompareStatus;
  target?: Maybe<Scalars['Map']['output']>;
};

export type EventConnection = {
  __typename?: 'EventConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<EventEdge>>>;
  pageInfo: PageInfo;
};

export enum EventCostCurrency {
  AED = 'AED',
  AFN = 'AFN',
  ALL = 'ALL',
  AMD = 'AMD',
  AOA = 'AOA',
  ARS = 'ARS',
  AUD = 'AUD',
  AWG = 'AWG',
  AZN = 'AZN',
  BAM = 'BAM',
  BBD = 'BBD',
  BDT = 'BDT',
  BGN = 'BGN',
  BHD = 'BHD',
  BIF = 'BIF',
  BMD = 'BMD',
  BND = 'BND',
  BOB = 'BOB',
  BRL = 'BRL',
  BWP = 'BWP',
  BYR = 'BYR',
  BZD = 'BZD',
  CAD = 'CAD',
  CDF = 'CDF',
  CHF = 'CHF',
  CLP = 'CLP',
  CNY = 'CNY',
  COP = 'COP',
  CRC = 'CRC',
  CVE = 'CVE',
  CZK = 'CZK',
  DJF = 'DJF',
  DKK = 'DKK',
  DOP = 'DOP',
  DZD = 'DZD',
  EGP = 'EGP',
  ERN = 'ERN',
  ETB = 'ETB',
  EUR = 'EUR',
  GBP = 'GBP',
  GEL = 'GEL',
  GHS = 'GHS',
  GNF = 'GNF',
  GTQ = 'GTQ',
  GYD = 'GYD',
  HKD = 'HKD',
  HNL = 'HNL',
  HRK = 'HRK',
  HUF = 'HUF',
  IDR = 'IDR',
  ILS = 'ILS',
  INR = 'INR',
  IQD = 'IQD',
  IRR = 'IRR',
  ISK = 'ISK',
  JMD = 'JMD',
  JOD = 'JOD',
  JPY = 'JPY',
  KES = 'KES',
  KHR = 'KHR',
  KMF = 'KMF',
  KRW = 'KRW',
  KWD = 'KWD',
  KZT = 'KZT',
  LBP = 'LBP',
  LKR = 'LKR',
  LRD = 'LRD',
  LTL = 'LTL',
  LVL = 'LVL',
  LYD = 'LYD',
  MAD = 'MAD',
  MDL = 'MDL',
  MGA = 'MGA',
  MKD = 'MKD',
  MMK = 'MMK',
  MOP = 'MOP',
  MUR = 'MUR',
  MXN = 'MXN',
  MYR = 'MYR',
  MZN = 'MZN',
  NAD = 'NAD',
  NGN = 'NGN',
  NIO = 'NIO',
  NOK = 'NOK',
  NPR = 'NPR',
  NZD = 'NZD',
  OMR = 'OMR',
  PAB = 'PAB',
  PEN = 'PEN',
  PHP = 'PHP',
  PKR = 'PKR',
  PLN = 'PLN',
  PYG = 'PYG',
  QAR = 'QAR',
  RON = 'RON',
  RSD = 'RSD',
  RUB = 'RUB',
  RWF = 'RWF',
  SAR = 'SAR',
  SDG = 'SDG',
  SEK = 'SEK',
  SGD = 'SGD',
  SOS = 'SOS',
  STD = 'STD',
  SYP = 'SYP',
  THB = 'THB',
  TND = 'TND',
  TOP = 'TOP',
  TRY = 'TRY',
  TTD = 'TTD',
  TWD = 'TWD',
  TZS = 'TZS',
  UAH = 'UAH',
  UGX = 'UGX',
  USD = 'USD',
  UYU = 'UYU',
  UZS = 'UZS',
  VEF = 'VEF',
  VND = 'VND',
  XAF = 'XAF',
  XOF = 'XOF',
  YER = 'YER',
  ZAR = 'ZAR',
  ZMK = 'ZMK'
}

export type EventCredit = {
  __typename?: 'EventCredit';
  promotionId?: Maybe<Scalars['Int']['output']>;
  promotionName?: Maybe<Scalars['String']['output']>;
  spent: Scalars['Int']['output'];
  sponsorName?: Maybe<Scalars['String']['output']>;
};

export type EventDefaults = {
  __typename?: 'EventDefaults';
  addArtistsToEvent?: Maybe<Scalars['Boolean']['output']>;
  disableAttractiveIntegration?: Maybe<Scalars['Boolean']['output']>;
  disableDayOfEventComms?: Maybe<Scalars['Boolean']['output']>;
  hideFromDiscovery?: Maybe<Scalars['Boolean']['output']>;
  manualValidationEnabled?: Maybe<Scalars['Boolean']['output']>;
  printedTicketFormat?: Maybe<PrintedTicketFormat>;
  requiresBoxOfficeTicketNomination?: Maybe<Scalars['Boolean']['output']>;
  restrictCountries?: Maybe<Array<Maybe<CountryCode>>>;
  restrictCountriesKind?: Maybe<RestrictionKind>;
  taxFree?: Maybe<Scalars['Boolean']['output']>;
  ticketTransfer?: Maybe<Scalars['Boolean']['output']>;
  waitingList?: Maybe<Scalars['Boolean']['output']>;
};

export type EventDefaultsInput = {
  /** The name of the object type currently being queried. */
  addArtistsToEvent?: InputMaybe<Scalars['Boolean']['input']>;
  disableAttractiveIntegration?: InputMaybe<Scalars['Boolean']['input']>;
  disableDayOfEventComms?: InputMaybe<Scalars['Boolean']['input']>;
  hideFromDiscovery?: InputMaybe<Scalars['Boolean']['input']>;
  manualValidationEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  printedTicketFormat?: InputMaybe<PrintedTicketFormat>;
  requiresBoxOfficeTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  restrictCountries?: InputMaybe<Array<InputMaybe<CountryCode>>>;
  restrictCountriesKind?: InputMaybe<RestrictionKind>;
  taxFree?: InputMaybe<Scalars['Boolean']['input']>;
  ticketTransfer?: InputMaybe<Scalars['Boolean']['input']>;
  waitingList?: InputMaybe<Scalars['Boolean']['input']>;
};

export type EventEdge = {
  __typename?: 'EventEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Event>;
};

export type EventFlags = {
  __typename?: 'EventFlags';
  alcoholFree?: Maybe<Scalars['Map']['output']>;
  autoRescheduledEventRefundsDeadline?: Maybe<Scalars['Map']['output']>;
  branded?: Maybe<Scalars['Map']['output']>;
  claimTickets?: Maybe<Scalars['Map']['output']>;
  codeLocked?: Maybe<Scalars['Map']['output']>;
  competition?: Maybe<Scalars['Map']['output']>;
  coolingOffPeriod?: Maybe<Scalars['Map']['output']>;
  disableDayOfEventComms?: Maybe<Scalars['Map']['output']>;
  enabledPwl?: Maybe<Scalars['Map']['output']>;
  fanPickSeat?: Maybe<Scalars['Map']['output']>;
  fanSurvey?: Maybe<Scalars['Map']['output']>;
  featured?: Maybe<Scalars['Map']['output']>;
  generateNewCodeOnTransfer?: Maybe<Scalars['Map']['output']>;
  grouped?: Maybe<Scalars['Map']['output']>;
  guestlist?: Maybe<Scalars['Map']['output']>;
  hidden?: Maybe<Scalars['Map']['output']>;
  hideFromDiscovery?: Maybe<Scalars['Map']['output']>;
  mbwayEnabled?: Maybe<Scalars['Map']['output']>;
  night?: Maybe<Scalars['Map']['output']>;
  paperTicket?: Maybe<Scalars['Map']['output']>;
  seated?: Maybe<Scalars['Map']['output']>;
  shoppingCart?: Maybe<Scalars['Map']['output']>;
  ticketTransfer?: Maybe<Scalars['Map']['output']>;
  ticketTypes?: Maybe<Scalars['Map']['output']>;
  unicorn?: Maybe<Scalars['Map']['output']>;
  waitingList?: Maybe<Scalars['Map']['output']>;
};

export type EventFlagsInput = {
  alcoholFree?: InputMaybe<FlagValue>;
  autoRescheduledEventRefundsDeadline?: InputMaybe<DeadlineInput>;
  branded?: InputMaybe<FlagValue>;
  claimTickets?: InputMaybe<FlagValue>;
  codeLocked?: InputMaybe<FlagValue>;
  competition?: InputMaybe<FlagValue>;
  coolingOffPeriod?: InputMaybe<CoolingOffPeriodInput>;
  disableDayOfEventComms?: InputMaybe<FlagValue>;
  enabledPwl?: InputMaybe<EnabledPwlInput>;
  fanPickSeat?: InputMaybe<FanPickSeatInput>;
  fanSurvey?: InputMaybe<FlagValue>;
  featured?: InputMaybe<FlagValue>;
  generateNewCodeOnTransfer?: InputMaybe<FlagValue>;
  grouped?: InputMaybe<FlagValue>;
  guestlist?: InputMaybe<FlagValue>;
  hidden?: InputMaybe<FlagValue>;
  hideFromDiscovery?: InputMaybe<FlagValue>;
  mbwayEnabled?: InputMaybe<FlagValue>;
  night?: InputMaybe<FlagValue>;
  paperTicket?: InputMaybe<FlagValue>;
  returns?: InputMaybe<FlagValue>;
  seated?: InputMaybe<FlagValue>;
  shoppingCart?: InputMaybe<FlagValue>;
  ticketTransfer?: InputMaybe<FlagValue>;
  ticketTypes?: InputMaybe<FlagValue>;
  unicorn?: InputMaybe<FlagValue>;
  waitingList?: InputMaybe<FlagValue>;
};

export type EventImage = Node & {
  __typename?: 'EventImage';
  attachment?: Maybe<Attachment>;
  cdnUrl: Scalars['String']['output'];
  cropRegion?: Maybe<CropRegion>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  type?: Maybe<Scalars['String']['output']>;
};

export type EventImageInput = {
  attachmentId?: InputMaybe<Scalars['ID']['input']>;
  cropRegion?: InputMaybe<CropRegionInput>;
  id?: InputMaybe<Scalars['ID']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export enum EventLifeCycleState {
  DRAFT = 'DRAFT',
  LIVE = 'LIVE',
  PAST = 'PAST',
  PAST_EXCLUDING_ARCHIVED = 'PAST_EXCLUDING_ARCHIVED'
}

export type EventLoadPrediction = Node & {
  __typename?: 'EventLoadPrediction';
  expectedRequestsPerMinute?: Maybe<Scalars['Int']['output']>;
  expectedStartTime?: Maybe<Scalars['Time']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
};

export type EventLoadPredictionInput = {
  expectedRequestsPerMinute: Scalars['Int']['input'];
  expectedStartTime: Scalars['Time']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
};

export type EventPayout = {
  __typename?: 'EventPayout';
  availability: EventBalanceAvailability;
  totalPaid?: Maybe<Scalars['Int']['output']>;
  totalPending?: Maybe<Scalars['Int']['output']>;
};

export type EventPromoter = {
  billingPromoter?: InputMaybe<Scalars['Boolean']['input']>;
  promoterId?: InputMaybe<Scalars['ID']['input']>;
};

export type EventPromotion = Node & {
  __typename?: 'EventPromotion';
  accessType: PromotionAccessType;
  code?: Maybe<Scalars['String']['output']>;
  codeLocks?: Maybe<CodeLockConnection>;
  discount?: Maybe<Scalars['Int']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  event?: Maybe<Event>;
  exportTokens: EventPromotionExportTokens;
  fanFacingName?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  isEnded: Scalars['Boolean']['output'];
  maxRedemptions?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  promotionType: PromotionType;
  seatingChannel?: Maybe<SeatingChannel>;
  startDate?: Maybe<Scalars['Time']['output']>;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  timesRedeemed: Scalars['Int']['output'];
  timesUsed: Scalars['Int']['output'];
  timesUsedUnique: Scalars['Int']['output'];
  unlockAfterExpired: Scalars['Boolean']['output'];
  usages?: Maybe<CodeUsageConnection>;
};


export type EventPromotionCodeLocksArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<CodeLockFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


export type EventPromotionUsagesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};

export type EventPromotionExportTokens = {
  __typename?: 'EventPromotionExportTokens';
  codesToken: Scalars['String']['output'];
  usagesToken: Scalars['String']['output'];
};

export type EventPromotionTicketPrice = {
  __typename?: 'EventPromotionTicketPrice';
  discountedFaceValue: Scalars['Int']['output'];
  discountedPrice: Scalars['Int']['output'];
  discountedPriceWithPwl?: Maybe<Scalars['Int']['output']>;
  discountedPriceWithoutPwl?: Maybe<Scalars['Int']['output']>;
  faceValue: Scalars['Int']['output'];
  fees: Scalars['Int']['output'];
  price: Scalars['Int']['output'];
  priceTier?: Maybe<PriceTier>;
  priceWithPwl?: Maybe<Scalars['Int']['output']>;
  priceWithoutPwl?: Maybe<Scalars['Int']['output']>;
};

export type EventPromotionTicketTypePrices = {
  __typename?: 'EventPromotionTicketTypePrices';
  prices: Array<Maybe<EventPromotionTicketPrice>>;
  ticketType: TicketType;
};

export type EventReview = Node & {
  __typename?: 'EventReview';
  assignee?: Maybe<User>;
  event?: Maybe<Event>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  priority?: Maybe<Scalars['Boolean']['output']>;
  status?: Maybe<EventReviewStatus>;
};

export type EventReviewConnection = {
  __typename?: 'EventReviewConnection';
  edges?: Maybe<Array<Maybe<EventReviewEdge>>>;
  pageInfo: PageInfo;
};

export type EventReviewEdge = {
  __typename?: 'EventReviewEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<EventReview>;
};

export enum EventReviewStatus {
  ESCALATED = 'ESCALATED',
  ON_HOLD = 'ON_HOLD'
}

export type EventReviewWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<EventReviewWhereInput>>>;
  assigneeId?: InputMaybe<OperatorsIdEqInput>;
  event?: InputMaybe<EventWhereInput>;
  id?: InputMaybe<OperatorsIdEqInput>;
  status?: InputMaybe<OperatorsEnumEventReviewStatus>;
};

export type EventRules = {
  __typename?: 'EventRules';
  covidPcr?: Maybe<Scalars['Boolean']['output']>;
  covidPcrValidHours?: Maybe<Scalars['Int']['output']>;
  covidPolicyUrl?: Maybe<Scalars['String']['output']>;
  covidRecovery?: Maybe<Scalars['Boolean']['output']>;
  covidVaccination?: Maybe<Scalars['Boolean']['output']>;
  maskRequired?: Maybe<Scalars['Boolean']['output']>;
  proofOfBeingHealthy?: Maybe<Scalars['Boolean']['output']>;
  socialDistancing?: Maybe<Scalars['Boolean']['output']>;
};

export type EventRulesInput = {
  covidPcr?: InputMaybe<Scalars['Boolean']['input']>;
  covidPcrValidHours?: InputMaybe<Scalars['Int']['input']>;
  covidPolicyUrl?: InputMaybe<Scalars['String']['input']>;
  covidRecovery?: InputMaybe<Scalars['Boolean']['input']>;
  covidVaccination?: InputMaybe<Scalars['Boolean']['input']>;
  maskRequired?: InputMaybe<Scalars['Boolean']['input']>;
  proofOfBeingHealthy?: InputMaybe<Scalars['Boolean']['input']>;
  socialDistancing?: InputMaybe<Scalars['Boolean']['input']>;
};

export type EventSchedule = {
  __typename?: 'EventSchedule';
  announceDate?: Maybe<Scalars['Time']['output']>;
  date?: Maybe<Scalars['Time']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  offSaleDate?: Maybe<Scalars['Time']['output']>;
  onSaleDate?: Maybe<Scalars['Time']['output']>;
};

export type EventScopesInput = {
  eventState?: InputMaybe<Array<InputMaybe<EventStates>>>;
  eventType?: InputMaybe<Array<InputMaybe<EventTypes>>>;
  lifeCycleState?: InputMaybe<Array<InputMaybe<EventLifeCycleState>>>;
  streamingUrlState?: InputMaybe<Array<InputMaybe<StreamingUrlState>>>;
};

export type EventSeatingChart = Node & {
  __typename?: 'EventSeatingChart';
  chartManagerCredentials: ChartManagerCredentials;
  chartThumbnail?: Maybe<Scalars['String']['output']>;
  event?: Maybe<Event>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  seatingChannels?: Maybe<Array<Maybe<SeatingChannel>>>;
  seatsIoChart?: Maybe<SeatsIoChart>;
  seatsIoChartReport?: Maybe<Scalars['Map']['output']>;
  seatsIoEvent?: Maybe<SeatsIoEvent>;
  seatsIoEventId: Scalars['String']['output'];
  seatsIoEventReport?: Maybe<Scalars['Map']['output']>;
  venueChart: SeatingChart;
};


export type EventSeatingChartSeatsIoEventReportArgs = {
  group: SeatsIoEventReportBy;
};

export type EventSharingObject = {
  __typename?: 'EventSharingObject';
  email: Scalars['String']['output'];
  id?: Maybe<Scalars['ID']['output']>;
  permissionProfile: PermissionProfile;
};

export type EventSharingObjectInput = {
  email: Scalars['String']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  permissionProfileId: Scalars['ID']['input'];
};

export enum EventSpecialFilters {
  onSaleToday = 'onSaleToday',
  runningLow = 'runningLow',
  soldOut = 'soldOut',
  toBePaid = 'toBePaid',
  today = 'today'
}

export enum EventState {
  APPROVED = 'APPROVED',
  ARCHIVED = 'ARCHIVED',
  CANCELLED = 'CANCELLED',
  DECLINED = 'DECLINED',
  DRAFT = 'DRAFT',
  REVIEW = 'REVIEW',
  SUBMITTED = 'SUBMITTED'
}

export enum EventStates {
  ANNOUNCED = 'ANNOUNCED',
  ARCHIVED = 'ARCHIVED',
  NOT_COMPLETE = 'NOT_COMPLETE',
  ON_SALE = 'ON_SALE',
  READY_TO_SUBMIT = 'READY_TO_SUBMIT',
  RUNNING_LOW = 'RUNNING_LOW',
  SOLD_OUT = 'SOLD_OUT',
  TO_BE_APPROVED = 'TO_BE_APPROVED'
}

export type EventTiming = {
  __typename?: 'EventTiming';
  offset?: Maybe<Scalars['Int']['output']>;
  sourceField?: Maybe<EventTimingField>;
  targetField?: Maybe<EventTimingField>;
  type?: Maybe<EventTimingType>;
};

export enum EventTimingField {
  ANNOUNCE_DATE = 'ANNOUNCE_DATE',
  DATE = 'DATE',
  END_DATE = 'END_DATE',
  LINE_UP_DOORS_OPEN = 'LINE_UP_DOORS_OPEN',
  OFF_SALE_DATE = 'OFF_SALE_DATE',
  ON_SALE_DATE = 'ON_SALE_DATE',
  ON_SALE_REMINDER_TIME = 'ON_SALE_REMINDER_TIME',
  RETURN_DEADLINE = 'RETURN_DEADLINE',
  TICKET_TYPE_ANNOUNCE_DATE = 'TICKET_TYPE_ANNOUNCE_DATE',
  TICKET_TYPE_OFF_SALE_DATE = 'TICKET_TYPE_OFF_SALE_DATE',
  TICKET_TYPE_ON_SALE_DATE = 'TICKET_TYPE_ON_SALE_DATE',
  TRANSFER_DEADLINE = 'TRANSFER_DEADLINE'
}

export type EventTimingInput = {
  offset?: InputMaybe<Scalars['Int']['input']>;
  sourceField?: InputMaybe<EventTimingField>;
  targetField?: InputMaybe<EventTimingField>;
};

export enum EventTimingType {
  DEFAULT = 'DEFAULT',
  PROMOTER = 'PROMOTER'
}

export enum EventType {
  HYBRID = 'HYBRID',
  LIVE = 'LIVE',
  STREAM = 'STREAM'
}

export enum EventTypes {
  BOX_OFFICE = 'BOX_OFFICE',
  RECURRING_EVENT = 'RECURRING_EVENT',
  REGULAR_EVENT = 'REGULAR_EVENT',
  STREAM_EVENT = 'STREAM_EVENT'
}

export type EventVenues = {
  primary?: InputMaybe<Scalars['Boolean']['input']>;
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export type EventWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<EventWhereInput>>>;
  accountId?: InputMaybe<OperatorsIdEqInput>;
  accountManagerId?: InputMaybe<OperatorsIdEqInput>;
  addressCountry?: InputMaybe<OperatorsString>;
  addressRegion?: InputMaybe<OperatorsString>;
  addressState?: InputMaybe<OperatorsString>;
  announceDate?: InputMaybe<OperatorsDateInput>;
  artistIds?: InputMaybe<OperatorsIdEqInput>;
  artistName?: InputMaybe<OperatorsString>;
  billingPromoter?: InputMaybe<OperatorsJsonInput>;
  billingPromoterName?: InputMaybe<OperatorsString>;
  bundleIds?: InputMaybe<OperatorsIdEqInput>;
  cityIds?: InputMaybe<OperatorsListOfStringInput>;
  closeEventDate?: InputMaybe<OperatorsDateInput>;
  countryCode?: InputMaybe<OperatorsEnumCountryCode>;
  createdAt?: InputMaybe<OperatorsDateInput>;
  date?: InputMaybe<OperatorsDateInput>;
  description?: InputMaybe<OperatorsString>;
  endDate?: InputMaybe<OperatorsDateInput>;
  endSaleDate?: InputMaybe<OperatorsDateInput>;
  eventIdLive?: InputMaybe<OperatorsString>;
  eventIdPreview?: InputMaybe<OperatorsString>;
  eventStatus?: InputMaybe<OperatorsString>;
  eventType?: InputMaybe<OperatorsString>;
  flagNames?: InputMaybe<OperatorsListOfStringInput>;
  free?: InputMaybe<OperatorsBooleanEqInput>;
  fullAddress?: InputMaybe<OperatorsString>;
  hierarchicalTagNames?: InputMaybe<OperatorsListOfStringInput>;
  id?: InputMaybe<OperatorsIdEqInput>;
  isAbbonamento?: InputMaybe<OperatorsBooleanEqInput>;
  labelNames?: InputMaybe<OperatorsListOfStringInput>;
  name?: InputMaybe<OperatorsString>;
  offSaleDate?: InputMaybe<OperatorsDateInput>;
  onSaleDate?: InputMaybe<OperatorsDateInput>;
  permName?: InputMaybe<OperatorsString>;
  priority?: InputMaybe<OperatorsBooleanEqInput>;
  promoterName?: InputMaybe<OperatorsString>;
  promoterPayoutsEnabled?: InputMaybe<OperatorsBooleanEqInput>;
  promoterTier?: InputMaybe<OperatorsEnumPromoterTier>;
  promoterTypeOfOrganizer?: InputMaybe<OperatorsEnumTypeOfOrganizer>;
  recurring?: InputMaybe<OperatorsBooleanEqInput>;
  scheduleStatus?: InputMaybe<OperatorsString>;
  special?: InputMaybe<EventSpecialFilters>;
  stages?: InputMaybe<OperatorsListOfStringInput>;
  state?: InputMaybe<OperatorsString>;
  stripeAccountId?: InputMaybe<OperatorsString>;
  stripeVerified?: InputMaybe<OperatorsBooleanEqInput>;
  submittedAt?: InputMaybe<OperatorsDateInput>;
  tagNames?: InputMaybe<OperatorsListOfStringInput>;
  venue?: InputMaybe<OperatorsString>;
  venueIds?: InputMaybe<OperatorsIdEqInput>;
  venueSeatingChartIds?: InputMaybe<OperatorsIdEqInput>;
  venueTier?: InputMaybe<OperatorsString>;
};

export type EventsApiToken = {
  __typename?: 'EventsApiToken';
  partnerId?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
};

export enum EventsConnectionOrder {
  announceDateASC = 'announceDateASC',
  announceDateDESC = 'announceDateDESC',
  cancelledAtASC = 'cancelledAtASC',
  cancelledAtDESC = 'cancelledAtDESC',
  createdAtASC = 'createdAtASC',
  createdAtDESC = 'createdAtDESC',
  dateASC = 'dateASC',
  dateDESC = 'dateDESC',
  endDateASC = 'endDateASC',
  endDateDESC = 'endDateDESC',
  nameASC = 'nameASC',
  nameDESC = 'nameDESC',
  offSaleDateASC = 'offSaleDateASC',
  offSaleDateDESC = 'offSaleDateDESC',
  onSaleDateASC = 'onSaleDateASC',
  onSaleDateDESC = 'onSaleDateDESC',
  submittedAtASC = 'submittedAtASC',
  submittedAtDESC = 'submittedAtDESC',
  ticketsSoldASC = 'ticketsSoldASC',
  ticketsSoldDESC = 'ticketsSoldDESC',
  totalFaceValueASC = 'totalFaceValueASC',
  totalFaceValueDESC = 'totalFaceValueDESC',
  venueASC = 'venueASC',
  venueDESC = 'venueDESC'
}

export type ExternalEntity = {
  __typename?: 'ExternalEntity';
  isEntertainment: Scalars['Boolean']['output'];
  name?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type ExternalLinks = {
  __typename?: 'ExternalLinks';
  facebook?: Maybe<Scalars['String']['output']>;
  instagram?: Maybe<Scalars['String']['output']>;
  tiktok?: Maybe<Scalars['String']['output']>;
  twitter?: Maybe<Scalars['String']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type ExternalLinksInput = {
  facebook?: InputMaybe<Scalars['String']['input']>;
  instagram?: InputMaybe<Scalars['String']['input']>;
  tiktok?: InputMaybe<Scalars['String']['input']>;
  twitter?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type ExtrasRevenueReportItem = {
  __typename?: 'ExtrasRevenueReportItem';
  faceValue: Scalars['Int']['output'];
  rebate: Scalars['Int']['output'];
  revenue: Scalars['Int']['output'];
  soldExtras: Scalars['Int']['output'];
  time: Scalars['Time']['output'];
};

export type FaceValueInterface = {
  faceValue: Scalars['Int']['output'];
};

export type FacebookPage = {
  __typename?: 'FacebookPage';
  autoCreateEvents: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  pageId: Scalars['ID']['output'];
  publishToLive: Scalars['Boolean']['output'];
};

export type FacebookPageInput = {
  autoCreateEvents: Scalars['Boolean']['input'];
  name: Scalars['String']['input'];
  pageId: Scalars['ID']['input'];
  publishToLive: Scalars['Boolean']['input'];
};

export type Fan = Node & {
  __typename?: 'Fan';
  blocked?: Maybe<Scalars['Boolean']['output']>;
  creditBalances?: Maybe<Array<Maybe<CreditBalance>>>;
  dob?: Maybe<Scalars['Date']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  fanActivities?: Maybe<FanActivityConnection>;
  firstName?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  pastEvents?: Maybe<Array<Maybe<FanEvent>>>;
  phone?: Maybe<Scalars['String']['output']>;
  upcomingEvents?: Maybe<Array<Maybe<FanEvent>>>;
};


export type FanFanActivitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<FanActivityWhereInput>;
};

export type FanAccountActivityPayload = {
  __typename?: 'FanAccountActivityPayload';
  changes?: Maybe<Array<Maybe<Scalars['Map']['output']>>>;
};

export type FanActivity = Node & {
  __typename?: 'FanActivity';
  date?: Maybe<Scalars['Time']['output']>;
  id: Scalars['ID']['output'];
  payload?: Maybe<FanActivityPayload>;
  type: FanActivityType;
};

export type FanActivityConnection = {
  __typename?: 'FanActivityConnection';
  edges?: Maybe<Array<Maybe<FanActivityEdge>>>;
  pageInfo: PageInfo;
};

export type FanActivityEdge = {
  __typename?: 'FanActivityEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<FanActivity>;
};

export type FanActivityLineItem = {
  __typename?: 'FanActivityLineItem';
  lineItemId?: Maybe<Scalars['Int']['output']>;
  product?: Maybe<Product>;
};

export type FanActivityNotificationPayload = {
  __typename?: 'FanActivityNotificationPayload';
  event?: Maybe<Event>;
};

export enum FanActivityOperationLogAction {
  ASSIGN_TICKETS = 'ASSIGN_TICKETS',
  CANCEL_RETURN_REQUEST = 'CANCEL_RETURN_REQUEST',
  REQUEST_RETURN = 'REQUEST_RETURN',
  REVOKE_SEATS = 'REVOKE_SEATS',
  USER_MERGE = 'USER_MERGE',
  WL_BUMP = 'WL_BUMP'
}

export type FanActivityOperationLogPayload = {
  __typename?: 'FanActivityOperationLogPayload';
  action?: Maybe<FanActivityOperationLogAction>;
  event?: Maybe<Event>;
  mergeFanId?: Maybe<Scalars['String']['output']>;
  operatorId?: Maybe<Scalars['String']['output']>;
  seatName?: Maybe<Scalars['String']['output']>;
  ticketType?: Maybe<TicketType>;
};

export type FanActivityPayload = FanAccountActivityPayload | FanActivityNotificationPayload | FanActivityOperationLogPayload | FanActivityProductPayload | FanActivityTicketPayload;

export enum FanActivityProductAction {
  PRICE_CORRECTION = 'PRICE_CORRECTION',
  PURCHASE = 'PURCHASE',
  REFUND = 'REFUND'
}

export enum FanActivityProductActionType {
  ACCESSIBILITY_REQUEST = 'ACCESSIBILITY_REQUEST',
  ACCIDENTAL_PURCHASE = 'ACCIDENTAL_PURCHASE',
  APP_TECH_ISSUE = 'APP_TECH_ISSUE',
  AUTOREFUND = 'AUTOREFUND',
  DISPUTED_TRANSACTION = 'DISPUTED_TRANSACTION',
  EVENT_BUILD_ERROR_DICE = 'EVENT_BUILD_ERROR_DICE',
  EVENT_BUILD_ERROR_PARTNER = 'EVENT_BUILD_ERROR_PARTNER',
  EVENT_CANCELLED = 'EVENT_CANCELLED',
  EVENT_COMPLAINT = 'EVENT_COMPLAINT',
  EVENT_POSTPONED_RESCHEDULED = 'EVENT_POSTPONED_RESCHEDULED',
  FRAUD_PREVENTION = 'FRAUD_PREVENTION',
  GOOD_WILL = 'GOOD_WILL',
  NO_VALID_ID = 'NO_VALID_ID',
  OTHER = 'OTHER',
  PROMOTER_REQUEST = 'PROMOTER_REQUEST',
  REJECTED_AT_THE_DOOR = 'REJECTED_AT_THE_DOOR',
  SUPPORT_DISCRETION = 'SUPPORT_DISCRETION',
  SUSPECTED_RESELLER = 'SUSPECTED_RESELLER',
  SWAP_REQUEST = 'SWAP_REQUEST',
  TICKET_RESOLD = 'TICKET_RESOLD',
  VENUE_COMPLAINT = 'VENUE_COMPLAINT'
}

export type FanActivityProductPayload = {
  __typename?: 'FanActivityProductPayload';
  action?: Maybe<FanActivityProductAction>;
  actionType?: Maybe<FanActivityProductActionType>;
  actioner?: Maybe<User>;
  event?: Maybe<Event>;
  lineItems?: Maybe<Array<Maybe<FanActivityLineItem>>>;
  purchaseId?: Maybe<Scalars['Int']['output']>;
  stripeLinks?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export enum FanActivityTicketAction {
  PRICE_CORRECTION = 'PRICE_CORRECTION',
  PURCHASE = 'PURCHASE',
  REFUND = 'REFUND',
  REMOVE_FROM_WL = 'REMOVE_FROM_WL',
  RESOLD_FROM_WL = 'RESOLD_FROM_WL',
  RESTORED_FROM_WL = 'RESTORED_FROM_WL',
  RETURN_TO_WL = 'RETURN_TO_WL',
  SWAP = 'SWAP',
  TRANSFER = 'TRANSFER'
}

export type FanActivityTicketPayload = {
  __typename?: 'FanActivityTicketPayload';
  action: FanActivityTicketAction;
  actioner?: Maybe<User>;
  charge?: Maybe<Charge>;
  event?: Maybe<Event>;
  lineItemIds?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  newEvent?: Maybe<Event>;
  newTicketIds?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  newTicketType?: Maybe<TicketType>;
  product?: Maybe<Product>;
  recipient?: Maybe<Fan>;
  ticketIds?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  ticketType?: Maybe<TicketType>;
};

export enum FanActivityType {
  ACCOUNT_ACTIVITY = 'ACCOUNT_ACTIVITY',
  COMMS = 'COMMS',
  NOTES = 'NOTES',
  NOTIFICATION = 'NOTIFICATION',
  OPERATION_LOG = 'OPERATION_LOG',
  PRODUCT = 'PRODUCT',
  REFUND = 'REFUND',
  TICKET = 'TICKET',
  WAITING_LIST = 'WAITING_LIST'
}

export type FanActivityWhereInput = {
  date?: InputMaybe<OperatorsDateInput>;
  type?: InputMaybe<OperatorsString>;
};

export type FanAddress = {
  __typename?: 'FanAddress';
  countryCode?: Maybe<Scalars['String']['output']>;
  county?: Maybe<Scalars['String']['output']>;
  line1?: Maybe<Scalars['String']['output']>;
  line2?: Maybe<Scalars['String']['output']>;
  postCode?: Maybe<Scalars['String']['output']>;
  town?: Maybe<Scalars['String']['output']>;
};

export type FanConnect = Node & {
  __typename?: 'FanConnect';
  event?: Maybe<Event>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  insertedAt?: Maybe<Scalars['Time']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  onBehalfOfUser?: Maybe<User>;
  scheduledAt?: Maybe<Scalars['Time']['output']>;
  sendMeACopy?: Maybe<Scalars['Boolean']['output']>;
  status?: Maybe<FanConnectStatus>;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  title?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['Time']['output']>;
  user?: Maybe<User>;
};

export type FanConnectConnection = {
  __typename?: 'FanConnectConnection';
  edges?: Maybe<Array<Maybe<FanConnectEdge>>>;
  pageInfo: PageInfo;
};

export type FanConnectEdge = {
  __typename?: 'FanConnectEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<FanConnect>;
};

export enum FanConnectOrder {
  INSERTED_AT_ASC = 'INSERTED_AT_ASC',
  INSERTED_AT_DESC = 'INSERTED_AT_DESC',
  SCHEDULED_AT_ASC = 'SCHEDULED_AT_ASC',
  SCHEDULED_AT_DESC = 'SCHEDULED_AT_DESC'
}

export enum FanConnectStatus {
  ARCHIVED = 'ARCHIVED',
  ERROR = 'ERROR',
  PENDING = 'PENDING',
  PROCESSED = 'PROCESSED',
  PROCESSING = 'PROCESSING',
  SCHEDULED = 'SCHEDULED'
}

export type FanConnectWhereInput = {
  eventId?: InputMaybe<OperatorsIdEqInput>;
  insertedAt?: InputMaybe<OperatorsDateInput>;
  onBehalfOfUserId?: InputMaybe<OperatorsIdEqInput>;
  promoterId?: InputMaybe<OperatorsIdEqInput>;
  scheduledAt?: InputMaybe<OperatorsDateInput>;
  status?: InputMaybe<OperatorsFanConnectStatusInput>;
};

export type FanConnection = {
  __typename?: 'FanConnection';
  edges?: Maybe<Array<Maybe<FanEdge>>>;
  pageInfo: PageInfo;
};

export type FanEdge = {
  __typename?: 'FanEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Fan>;
};

export type FanEvent = {
  __typename?: 'FanEvent';
  event?: Maybe<Event>;
  extraCount?: Maybe<Scalars['Int']['output']>;
  ticketCount?: Maybe<Scalars['Int']['output']>;
};

/** filter fans */
export type FanFilterInput = {
  email?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['PhoneNumber']['input']>;
};

export type FanPickSeatInput = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
  hours?: InputMaybe<Scalars['Int']['input']>;
};

export type FanQuestion = {
  __typename?: 'FanQuestion';
  enabled?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  required?: Maybe<Scalars['Boolean']['output']>;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type FanQuestionInput = {
  enabled?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['ID']['input'];
  required?: InputMaybe<Scalars['Boolean']['input']>;
  ticketTypeIds: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type FanSupportNotes = {
  __typename?: 'FanSupportNotes';
  body?: Maybe<Scalars['String']['output']>;
};

export type FanSupportNotesInput = {
  body?: InputMaybe<Scalars['String']['input']>;
};

export type FanWhereInput = {
  id?: InputMaybe<OperatorsIdEqInput>;
};

export type FansStats = {
  __typename?: 'FansStats';
  ageBreakdown?: Maybe<Array<Maybe<AgeBreakdownItem>>>;
  averageAge?: Maybe<Scalars['Int']['output']>;
  female?: Maybe<Scalars['Float']['output']>;
  male?: Maybe<Scalars['Float']['output']>;
  other?: Maybe<Scalars['Float']['output']>;
};

export type Faq = Node & {
  __typename?: 'Faq';
  body?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  order: Scalars['Int']['output'];
  title?: Maybe<Scalars['String']['output']>;
};

export type FaqInput = {
  body: Scalars['String']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  order: Scalars['Int']['input'];
  title: Scalars['String']['input'];
};

export type FeatureFlags = {
  __typename?: 'FeatureFlags';
  /** Enable access to artist inventory */
  artistInventory?: Maybe<Scalars['Boolean']['output']>;
  /** Allows user to enable Unicorn Purchase Flow or Cart UI */
  devSettings?: Maybe<Scalars['Boolean']['output']>;
  /** Disable Amex in EU depending on Experiements variable */
  disablingAmex?: Maybe<Scalars['Boolean']['output']>;
  /** Enable access to the new event overview page */
  eventOverviewV2?: Maybe<Scalars['Boolean']['output']>;
  /** Enable access to Events Collection page */
  eventsCollection?: Maybe<Scalars['Boolean']['output']>;
  /** Allows user to work with extras */
  extras?: Maybe<Scalars['Boolean']['output']>;
  /**
   * Allows user to see UI for creating extras separate barcodes
   * @deprecated To be removed: all users who can see extras should see the separate code UI
   */
  extrasSeparateAccessBarcodes?: Maybe<Scalars['Boolean']['output']>;
  /** Enable access to holds */
  holds?: Maybe<Scalars['Boolean']['output']>;
  /** Allows DICE staff to define promoter level defaults for event level settings */
  promoterDefaultEventSettings?: Maybe<Scalars['Boolean']['output']>;
  /** Allows to define which seat categories are on hold */
  seatsHold?: Maybe<Scalars['Boolean']['output']>;
  /** Allows to test new ideas on Social links */
  socialLinkExperiment?: Maybe<Scalars['Boolean']['output']>;
  /** Dictates whether or not ticket pools should be used for new events */
  ticketPools?: Maybe<Scalars['Boolean']['output']>;
  /** Allows user to enable Box Office on a Unicorn event */
  unicornBoxOffice?: Maybe<Scalars['Boolean']['output']>;
  /** Allows user to enable Promotions/Codelocks for Unicorn events */
  unicornPromotions?: Maybe<Scalars['Boolean']['output']>;
};

export type FeaturedArea = Node & {
  __typename?: 'FeaturedArea';
  countryCode?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  endDate: Scalars['Time']['output'];
  event?: Maybe<Event>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  locationLat?: Maybe<Scalars['Float']['output']>;
  locationLng?: Maybe<Scalars['Float']['output']>;
  /** Radius expressed in user-facing 'units' - km or miles */
  locationRadius?: Maybe<Scalars['Float']['output']>;
  locationString?: Maybe<Scalars['String']['output']>;
  locationUnits?: Maybe<DistanceUnits>;
  mode: TargetingMode;
  startDate: Scalars['Time']['output'];
  weight: Scalars['Int']['output'];
};

export type FeaturedAreaEstimate = {
  __typename?: 'FeaturedAreaEstimate';
  featuredArea?: Maybe<FeaturedArea>;
  /** Weekly Average Users that will see this promo in a given position */
  wauByLayer?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
};

export type FeaturedAreaInput = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  endDate: Scalars['Time']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  locationLat?: InputMaybe<Scalars['Float']['input']>;
  locationLng?: InputMaybe<Scalars['Float']['input']>;
  locationRadius?: InputMaybe<Scalars['Float']['input']>;
  locationString?: InputMaybe<Scalars['String']['input']>;
  locationUnits?: InputMaybe<DistanceUnits>;
  mode: TargetingMode;
  startDate: Scalars['Time']['input'];
  weight: Scalars['Int']['input'];
};

export type Fee = {
  __typename?: 'Fee';
  /** @deprecated No longer active */
  active?: Maybe<Scalars['Boolean']['output']>;
  amount?: Maybe<Scalars['Float']['output']>;
  applicable?: Maybe<Scalars['Boolean']['output']>;
  split?: Maybe<Array<Maybe<FeeSplit>>>;
  type: FeeType;
  unit: FeeUnit;
};

export enum FeeDestination {
  billingPromoter = 'billingPromoter',
  keep = 'keep'
}

export type FeeInput = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
  amount: Scalars['Float']['input'];
  split: Array<InputMaybe<FeeSplitInput>>;
  type: FeeType;
  unit: FeeUnit;
};

export type FeeOutput = {
  __typename?: 'FeeOutput';
  /** @deprecated No longer active */
  active?: Maybe<Scalars['Boolean']['output']>;
  amount?: Maybe<Scalars['Float']['output']>;
  applicable?: Maybe<Scalars['Boolean']['output']>;
  computed: Scalars['Int']['output'];
  split: Array<Maybe<FeeSplitOutput>>;
  type: FeeType;
  unit: FeeUnit;
};

export type FeeRange = {
  __typename?: 'FeeRange';
  fees?: Maybe<Array<Maybe<Fee>>>;
  fromBasePrice: Scalars['Int']['output'];
  id: Scalars['ID']['output'];
};

export type FeeRangeInput = {
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  fromBasePrice?: InputMaybe<Scalars['Int']['input']>;
};

export type FeeSplit = {
  __typename?: 'FeeSplit';
  amount?: Maybe<Scalars['Float']['output']>;
  destination?: Maybe<FeeDestination>;
  unit: FeeUnit;
};

export type FeeSplitInput = {
  amount: Scalars['Float']['input'];
  destination: FeeDestination;
  unit: FeeUnit;
};

export type FeeSplitOutput = {
  __typename?: 'FeeSplitOutput';
  amount?: Maybe<Scalars['Float']['output']>;
  computed: Scalars['Int']['output'];
  destination?: Maybe<FeeDestination>;
  unit: FeeUnit;
};

export enum FeeType {
  additionalPromoterFee = 'additionalPromoterFee',
  booking = 'booking',
  boxOfficeFee = 'boxOfficeFee',
  charityDonation = 'charityDonation',
  deposit = 'deposit',
  extraCharge = 'extraCharge',
  facilityFee = 'facilityFee',
  paidWaitingList = 'paidWaitingList',
  postal = 'postal',
  presale = 'presale',
  processing = 'processing',
  salesTax = 'salesTax',
  tierDiff = 'tierDiff',
  vendor = 'vendor',
  venueFee = 'venueFee',
  venueLevy = 'venueLevy'
}

export enum FeeUnit {
  fixed = 'fixed',
  percentage = 'percentage'
}

export type Fees = {
  fees?: Maybe<Array<Maybe<Fee>>>;
};

export type FeesApplicationRule = {
  __typename?: 'FeesApplicationRule';
  feeRanges?: Maybe<Array<Maybe<FeeRange>>>;
  feeTarget?: Maybe<EnumFeeTarget>;
  feeTargetCategories?: Maybe<Array<Maybe<Category>>>;
  feeTargetParentCategory?: Maybe<Category>;
  id: Scalars['ID']['output'];
};

export type FeesApplicationRuleInput = {
  feeRanges?: InputMaybe<Array<InputMaybe<FeeRangeInput>>>;
  feeTarget?: InputMaybe<EnumFeeTarget>;
  feeTargetCategoryIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export enum FeesBehaviour {
  APPEND_TO_CONTRACT = 'APPEND_TO_CONTRACT',
  OVERRIDE = 'OVERRIDE',
  USE_CONTRACT = 'USE_CONTRACT'
}

export type FeesConfiguration = Node & {
  __typename?: 'FeesConfiguration';
  allowedAdhocFeeTypes?: Maybe<Array<Maybe<Fee>>>;
  basePriceFees?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  basePriceMode: BasePriceMode;
  boxOfficeFee?: Maybe<Scalars['Int']['output']>;
  effectiveDate?: Maybe<Scalars['Time']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  feesApplicationRules?: Maybe<Array<Maybe<FeesApplicationRule>>>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  postFanPriceFees?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  taxExempt: Scalars['Boolean']['output'];
  taxId?: Maybe<Scalars['String']['output']>;
};

export type FeesRates = {
  __typename?: 'FeesRates';
  additionalPromoterFee?: Maybe<Scalars['Float']['output']>;
  bookingFee?: Maybe<Scalars['Float']['output']>;
  charityFee?: Maybe<Scalars['Float']['output']>;
  extraCharge?: Maybe<Scalars['Float']['output']>;
  facilityFee?: Maybe<Scalars['Float']['output']>;
  foodAndBeverage?: Maybe<Scalars['Float']['output']>;
  fulfillment?: Maybe<Scalars['Float']['output']>;
  meetAndGreet?: Maybe<Scalars['Float']['output']>;
  presale?: Maybe<Scalars['Float']['output']>;
  processingFee?: Maybe<Scalars['Float']['output']>;
  pwlFee?: Maybe<Scalars['Float']['output']>;
  tierDiff?: Maybe<Scalars['Float']['output']>;
  vendor?: Maybe<Scalars['Float']['output']>;
  venueFee?: Maybe<Scalars['Float']['output']>;
  venueLevy?: Maybe<Scalars['Float']['output']>;
};

export type FeesSum = {
  __typename?: 'FeesSum';
  computed: Scalars['Int']['output'];
  split: Array<Maybe<PriceSplit>>;
  type: FeeType;
};

export type FinTran = {
  __typename?: 'FinTran';
  accruals?: Maybe<Array<Maybe<StripeAccrual>>>;
  causedById: Scalars['Int']['output'];
  causedByType: FinTranCausationType;
  committedAt?: Maybe<Scalars['Time']['output']>;
  disposedAt?: Maybe<Scalars['Time']['output']>;
  finTranItems: Array<Maybe<FinTranItem>>;
  kind: FinTranKind;
  paymentIntent?: Maybe<StripePaymentIntent>;
  purchaseId: Scalars['Int']['output'];
  refunds?: Maybe<Array<Maybe<StripeRefund>>>;
  txn: Scalars['String']['output'];
};

export enum FinTranAccountType {
  DICE_PLATFORM = 'DICE_PLATFORM',
  FAN_CASH = 'FAN_CASH',
  FAN_CREDIT = 'FAN_CREDIT',
  PARTNER = 'PARTNER',
  SWAP = 'SWAP'
}

export enum FinTranCausationType {
  FAN_ACTION = 'FAN_ACTION',
  PAYMENT = 'PAYMENT',
  PURCHASE = 'PURCHASE',
  SUPPORT_ACTION = 'SUPPORT_ACTION'
}

export type FinTranItem = {
  __typename?: 'FinTranItem';
  accountType: FinTranAccountType;
  balanceChange: Scalars['Int']['output'];
  billingDestination?: Maybe<BillingDestination>;
  lineItemId?: Maybe<Scalars['Int']['output']>;
  priceComponent?: Maybe<Scalars['String']['output']>;
};

export enum FinTranKind {
  ADJUSTMENT = 'ADJUSTMENT',
  CUSTOM = 'CUSTOM',
  PAYMENT = 'PAYMENT',
  RETURN = 'RETURN',
  SWAP_IN = 'SWAP_IN',
  SWAP_OUT = 'SWAP_OUT'
}

export type FinishStripeOnboardingInput = {
  clientMutationId: Scalars['String']['input'];
  onboardingCode: Scalars['String']['input'];
  state: Scalars['String']['input'];
};

export type FinishStripeOnboardingPayload = {
  __typename?: 'FinishStripeOnboardingPayload';
  clientMutationId: Scalars['String']['output'];
  promoter?: Maybe<Promoter>;
};

export type FlagValue = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
};

export type ForceMarkbackAllocationInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type ForceMarkbackAllocationPayload = {
  __typename?: 'ForceMarkbackAllocationPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type GuestList = {
  __typename?: 'GuestList';
  entries?: Maybe<GuestListEntryConnection>;
  guestsCount?: Maybe<Scalars['Int']['output']>;
  ticketsCount?: Maybe<Scalars['Int']['output']>;
};


export type GuestListEntriesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Array<InputMaybe<GuestListOrder>>>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
};

export type GuestListEntry = Node & {
  __typename?: 'GuestListEntry';
  email?: Maybe<Scalars['EmailAddress']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  quantity?: Maybe<Scalars['Int']['output']>;
  state?: Maybe<GuestListEntryState>;
  ticketType?: Maybe<TicketType>;
};

export type GuestListEntryConnection = {
  __typename?: 'GuestListEntryConnection';
  edges?: Maybe<Array<Maybe<GuestListEntryEdge>>>;
  pageInfo: PageInfo;
};

export type GuestListEntryEdge = {
  __typename?: 'GuestListEntryEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<GuestListEntry>;
};

export enum GuestListEntryState {
  ACTIVATED = 'ACTIVATED',
  ASSIGNED = 'ASSIGNED',
  ERROR = 'ERROR',
  INVITED = 'INVITED',
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING'
}

export enum GuestListOrder {
  INSERTED_AT_ASC = 'INSERTED_AT_ASC',
  INSERTED_AT_DESC = 'INSERTED_AT_DESC'
}

export type HierarchicalTag = Name & Node & {
  __typename?: 'HierarchicalTag';
  createdAt: Scalars['Time']['output'];
  deprecated: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  kind?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  parent?: Maybe<HierarchicalTag>;
  parentId?: Maybe<Scalars['ID']['output']>;
  updatedAt: Scalars['Time']['output'];
};

export enum HierarchicalTagOrder {
  NAME = 'NAME'
}

export type HierarchicalTagWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<HierarchicalTagWhereInput>>>;
  deprecated?: InputMaybe<OperatorsBooleanEqInput>;
  kind?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<OperatorsString>;
  parentId?: InputMaybe<OperatorsIdEqInput>;
};

export type HierarchicalTagsConnection = {
  __typename?: 'HierarchicalTagsConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<HierarchicalTagsEdge>>>;
  pageInfo: PageInfo;
};

export type HierarchicalTagsEdge = {
  __typename?: 'HierarchicalTagsEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<HierarchicalTag>;
};

export type Hold = {
  __typename?: 'Hold';
  allocation?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type HoldInput = {
  allocation?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type HolderName = {
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  idNumber?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
};

export type ImpersonateUserInput = {
  accountId?: InputMaybe<Scalars['ID']['input']>;
  clientMutationId: Scalars['String']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  reason: Scalars['String']['input'];
};

export type ImpersonateUserPayload = {
  __typename?: 'ImpersonateUserPayload';
  clientMutationId: Scalars['String']['output'];
  token?: Maybe<Scalars['String']['output']>;
};

export type ImportPayoutsInput = {
  clientMutationId: Scalars['String']['input'];
  importType?: InputMaybe<PayoutImportType>;
  storagePath?: InputMaybe<Scalars['String']['input']>;
};

export type ImportPayoutsPayload = {
  __typename?: 'ImportPayoutsPayload';
  clientMutationId: Scalars['String']['output'];
  status?: Maybe<Scalars['String']['output']>;
};

export type IntegrationStatus = {
  __typename?: 'IntegrationStatus';
  externalId?: Maybe<Scalars['String']['output']>;
  insertedAt?: Maybe<Scalars['Time']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type IntegrationToken = {
  __typename?: 'IntegrationToken';
  event?: Maybe<Event>;
  fullAccess?: Maybe<Scalars['Boolean']['output']>;
  insertedAt?: Maybe<Scalars['Time']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  user?: Maybe<User>;
  venue?: Maybe<Venue>;
};

export type Inventory = Node & {
  __typename?: 'Inventory';
  currency?: Maybe<EventCostCurrency>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  products?: Maybe<Array<Maybe<Product>>>;
  shopifyStore?: Maybe<ShopifyStore>;
  tokens?: Maybe<InventoryTokenList>;
};

export type InventoryProductInput = {
  id: Scalars['ID']['input'];
  purchaseConfirmationMessage?: InputMaybe<Scalars['String']['input']>;
};

export type InventoryTokenList = {
  __typename?: 'InventoryTokenList';
  reconciliationReportExportToken?: Maybe<Scalars['String']['output']>;
};

export type InviteGuestListInput = {
  clientMutationId: Scalars['String']['input'];
  emails: Array<InputMaybe<Scalars['EmailAddress']['input']>>;
  quantity: Scalars['Int']['input'];
  ticketTypeId: Scalars['ID']['input'];
};

export type InviteGuestListPayload = {
  __typename?: 'InviteGuestListPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<GuestList>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type InviteUserInput = {
  accountId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
  email: Scalars['String']['input'];
  permissionProfileId: Scalars['ID']['input'];
};

export type InviteUserPayload = {
  __typename?: 'InviteUserPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<AccountUserInvitation>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type Label = ChecklistsNode & Node & {
  __typename?: 'Label';
  checklists: Array<Maybe<Checklist>>;
  description: Scalars['String']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type LabelWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<LabelWhereInput>>>;
  name?: InputMaybe<OperatorsString>;
};

export type LabelsConnection = {
  __typename?: 'LabelsConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<LabelsEdge>>>;
  pageInfo: PageInfo;
};

export type LabelsEdge = {
  __typename?: 'LabelsEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Label>;
};

export enum Language {
  CA = 'CA',
  DE = 'DE',
  EN_CA = 'EN_CA',
  EN_GB = 'EN_GB',
  EN_IN = 'EN_IN',
  EN_US = 'EN_US',
  ES = 'ES',
  FR = 'FR',
  IT = 'IT',
  PT = 'PT'
}

export enum LegalEntityType {
  COMPANY = 'COMPANY',
  INDIVIDUAL = 'INDIVIDUAL'
}

export type LineupInput = {
  details?: InputMaybe<Scalars['String']['input']>;
  time?: InputMaybe<Scalars['String']['input']>;
};

export type Link = {
  __typename?: 'Link';
  name?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type LinkInput = {
  name?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
};

export type LinkStats = {
  __typename?: 'LinkStats';
  amount: Scalars['Int']['output'];
  campaign: Scalars['String']['output'];
  channel: Scalars['String']['output'];
  id?: Maybe<Scalars['ID']['output']>;
  postType: SocialLinkPostType;
  priceTier?: Maybe<PriceTier>;
  socialLink?: Maybe<SocialLink>;
  statsType: LinkStatsType;
  ticketType?: Maybe<TicketType>;
};

export enum LinkStatsType {
  clicks = 'clicks',
  faceValue = 'faceValue',
  purchases = 'purchases',
  ticketsSold = 'ticketsSold'
}

/** Linkout */
export type Linkout = Node & {
  __typename?: 'Linkout';
  /** Primary event artist */
  artist?: Maybe<Artist>;
  /** DICE cities for the event */
  cities?: Maybe<Array<Maybe<City>>>;
  /** Ticket currency */
  currency?: Maybe<EventCostCurrency>;
  /** Event start date */
  date: Scalars['Time']['output'];
  /** DICE event, required if the linkout_type is internal */
  destinationEvent?: Maybe<Event>;
  /** Event end date */
  endDate: Scalars['Time']['output'];
  /** External URL, required if linkout_type is external */
  externalUrl?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  /** Event image */
  imageAttachment?: Maybe<Attachment>;
  /** Event image crop region */
  imageCropRegion?: Maybe<CropRegion>;
  /** Linkout type */
  linkoutType: LinkoutType;
  /** Event name */
  name: Scalars['String']['output'];
  /** Event off-sale date */
  offSaleDate?: Maybe<Scalars['Time']['output']>;
  /** Event on-sale date */
  onSaleDate?: Maybe<Scalars['Time']['output']>;
  /** Representative price for the event */
  price?: Maybe<Scalars['Int']['output']>;
  /** Event promoter */
  promoter?: Maybe<Promoter>;
  /** Event tags */
  tags?: Maybe<Array<Maybe<Tag>>>;
  /** Event timezone */
  timezone: Scalars['String']['output'];
  /** Event venue */
  venue?: Maybe<Venue>;
};

export type LinkoutConnection = {
  __typename?: 'LinkoutConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<LinkoutEdge>>>;
  pageInfo: PageInfo;
};

export type LinkoutEdge = {
  __typename?: 'LinkoutEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Linkout>;
};

/** The type of linkout, with internal being DICE events, and external being non-DICE events. */
export enum LinkoutType {
  EXTERNAL = 'EXTERNAL',
  INTERNAL = 'INTERNAL'
}

export type List = {
  __typename?: 'List';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type Location = {
  addressCountry?: Maybe<Scalars['String']['output']>;
  addressLocality?: Maybe<Scalars['String']['output']>;
  addressRegion?: Maybe<Scalars['String']['output']>;
  addressState?: Maybe<Scalars['String']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  fullAddress?: Maybe<Scalars['String']['output']>;
  latitude?: Maybe<Scalars['Float']['output']>;
  longitude?: Maybe<Scalars['Float']['output']>;
  postOfficeBoxNumber?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  streetAddress?: Maybe<Scalars['String']['output']>;
  timezoneName?: Maybe<Scalars['String']['output']>;
};

export type LocationStats = {
  __typename?: 'LocationStats';
  itemsByCity: Array<LocationStatsItem>;
  itemsByCountry: Array<LocationStatsItem>;
};

export type LocationStatsItem = {
  __typename?: 'LocationStatsItem';
  amount: Scalars['Int']['output'];
  city: Scalars['String']['output'];
  country: Scalars['String']['output'];
};

export type Mailchimp = {
  __typename?: 'Mailchimp';
  audienceLists?: Maybe<Array<Maybe<List>>>;
  authorizeUrl?: Maybe<Scalars['String']['output']>;
  settings?: Maybe<MailchimpSettings>;
};

export type MailchimpSettings = {
  __typename?: 'MailchimpSettings';
  connectedAt?: Maybe<Scalars['Time']['output']>;
  mailchimpListId?: Maybe<Scalars['String']['output']>;
  mailchimpStoreId?: Maybe<Scalars['String']['output']>;
  syncOnlyOptIn?: Maybe<Scalars['Boolean']['output']>;
};

export type MarkBackAllocationsInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type MarkBackAllocationsPayload = {
  __typename?: 'MarkBackAllocationsPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type Marketeer = Name & Node & {
  __typename?: 'Marketeer';
  accessToken?: Maybe<Scalars['String']['output']>;
  appOptInEnabled?: Maybe<Scalars['Boolean']['output']>;
  contacts?: Maybe<Array<Maybe<User>>>;
  fbAccessToken?: Maybe<Scalars['String']['output']>;
  fbPixelId?: Maybe<Scalars['String']['output']>;
  gaTrackingId?: Maybe<Scalars['String']['output']>;
  googleAdsConversionId?: Maybe<Scalars['String']['output']>;
  googleAdsPurchaseConversionLabel?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  privacyPolicyLink?: Maybe<Scalars['String']['output']>;
  tiktokPixelId?: Maybe<Scalars['String']['output']>;
  twitterCheckoutInitiatedPixelId?: Maybe<Scalars['String']['output']>;
  twitterPixelId?: Maybe<Scalars['String']['output']>;
  twitterPurchasePixelId?: Maybe<Scalars['String']['output']>;
  webOptInEnabled?: Maybe<Scalars['Boolean']['output']>;
};

export type MarketeerWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<MarketeerWhereInput>>>;
  id?: InputMaybe<OperatorsIdEqInput>;
  name?: InputMaybe<OperatorsString>;
};

export type MarketeersConnection = {
  __typename?: 'MarketeersConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<MarketeersEdge>>>;
  pageInfo: PageInfo;
};

export type MarketeersEdge = {
  __typename?: 'MarketeersEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Marketeer>;
};

export type MediaItem = {
  __typename?: 'MediaItem';
  id: Scalars['ID']['output'];
  type: MediaItemTypes;
  values?: Maybe<Scalars['Json']['output']>;
};

export type MediaItemInputObject = {
  id?: InputMaybe<Scalars['ID']['input']>;
  type: MediaItemTypes;
  values: MediaValuesInput;
};

export enum MediaItemTypes {
  appleMusicTrack = 'appleMusicTrack',
  spotifyArtist = 'spotifyArtist',
  spotifyTrack = 'spotifyTrack',
  trailer = 'trailer'
}

export type MediaValuesInput = {
  artistId?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  mediaUri?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  openUrl?: InputMaybe<Scalars['String']['input']>;
  previewUrl?: InputMaybe<Scalars['String']['input']>;
  video?: InputMaybe<Scalars['String']['input']>;
};

export type MethodPair = {
  __typename?: 'MethodPair';
  method: PaymentMethod;
  value: Scalars['Int']['output'];
};

export type MinorAttractiveFieldsInput = {
  author?: InputMaybe<Scalars['String']['input']>;
  compatibilityAe?: InputMaybe<CompatibilityAe>;
  distributor?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  nationality?: InputMaybe<Scalars['String']['input']>;
  performer?: InputMaybe<Scalars['String']['input']>;
  producer?: InputMaybe<Scalars['String']['input']>;
};

export type MinorEventImageInput = {
  attachmentId?: InputMaybe<Scalars['ID']['input']>;
  cropRegion?: InputMaybe<CropRegionInput>;
  id?: InputMaybe<Scalars['ID']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type MinorPriceTierInput = {
  allocation?: InputMaybe<Scalars['Int']['input']>;
  attractivePriceType?: InputMaybe<Scalars['String']['input']>;
  doorSalesPrice?: InputMaybe<Scalars['Int']['input']>;
  faceValue?: InputMaybe<Scalars['Int']['input']>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  time?: InputMaybe<Scalars['Time']['input']>;
};

export type MinorTicketPoolInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  maxAllocation?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type MinorTicketTypeInput = {
  activateCodeDateOffset?: InputMaybe<Scalars['Int']['input']>;
  additionalPaymentMethods?: InputMaybe<Array<InputMaybe<PaymentMethods>>>;
  allocation?: InputMaybe<Scalars['Int']['input']>;
  allowSeatChange?: InputMaybe<Scalars['Boolean']['input']>;
  announceDate?: InputMaybe<Scalars['Time']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  attractivePriceType?: InputMaybe<Scalars['String']['input']>;
  attractiveSeatingAreaType?: InputMaybe<Scalars['String']['input']>;
  attractiveTaxFree?: InputMaybe<Scalars['Boolean']['input']>;
  codeLocked?: InputMaybe<Scalars['Boolean']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  doorSalesEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  doorSalesPrice?: InputMaybe<Scalars['Int']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  externalSkus?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  faceValue?: InputMaybe<Scalars['Int']['input']>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  hidden?: InputMaybe<Scalars['Boolean']['input']>;
  icon?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  increment?: InputMaybe<Scalars['Int']['input']>;
  isStream?: InputMaybe<Scalars['Boolean']['input']>;
  maximumIncrements?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  priceGrade?: InputMaybe<Scalars['String']['input']>;
  priceHidden?: InputMaybe<Scalars['Boolean']['input']>;
  priceTierType?: InputMaybe<PriceTierTypes>;
  priceTiers?: InputMaybe<Array<InputMaybe<MinorPriceTierInput>>>;
  productIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  requiresAddress?: InputMaybe<Scalars['Boolean']['input']>;
  reservedForGuestList?: InputMaybe<Scalars['Boolean']['input']>;
  reservedSeating?: InputMaybe<Scalars['Boolean']['input']>;
  reservedSeatingType?: InputMaybe<ReservedSeatingTypes>;
  salesLimit?: InputMaybe<Scalars['Int']['input']>;
  seatCategories?: InputMaybe<Array<InputMaybe<SeatCategoriesInput>>>;
  seatmapUrl?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
  streamEmbedCode?: InputMaybe<Scalars['String']['input']>;
  streamLink?: InputMaybe<Scalars['String']['input']>;
  ticketPoolId?: InputMaybe<Scalars['String']['input']>;
  venueScheduleId?: InputMaybe<Scalars['ID']['input']>;
  venueScheduleIndex?: InputMaybe<Scalars['Int']['input']>;
};

export type MinorUpdateEventInput = {
  /** The name of the object type currently being queried. */
  additionalArtists?: InputMaybe<Array<InputMaybe<AdditionalArtistInput>>>;
  additionalInfos?: InputMaybe<Array<InputMaybe<EventAdditionalInfoInput>>>;
  addressCapacity?: InputMaybe<Scalars['Int']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressSiaeCode?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  ageLimit?: InputMaybe<Scalars['String']['input']>;
  announceDate?: InputMaybe<Scalars['Time']['input']>;
  artistIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  artists?: InputMaybe<Array<InputMaybe<EventArtistInput>>>;
  attractiveFields?: InputMaybe<MinorAttractiveFieldsInput>;
  bundleIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  characteristicIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  closeEventDate?: InputMaybe<Scalars['Time']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  diceStreamDuration?: InputMaybe<Scalars['Int']['input']>;
  diceStreamDvrEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  diceStreamRewatchEnabledUntil?: InputMaybe<Scalars['Time']['input']>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  doorlistAdditionalRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventImages?: InputMaybe<Array<InputMaybe<MinorEventImageInput>>>;
  eventLoadPredictions?: InputMaybe<Array<InputMaybe<EventLoadPredictionInput>>>;
  eventRules?: InputMaybe<EventRulesInput>;
  eventSharingObjects?: InputMaybe<Array<InputMaybe<EventSharingObjectInput>>>;
  eventVenues?: InputMaybe<Array<InputMaybe<EventVenues>>>;
  extraNotes?: InputMaybe<Scalars['String']['input']>;
  fanSupportNotes?: InputMaybe<FanSupportNotesInput>;
  faqs?: InputMaybe<Array<InputMaybe<FaqInput>>>;
  featuredAreas?: InputMaybe<Array<InputMaybe<FeaturedAreaInput>>>;
  flags?: InputMaybe<EventFlagsInput>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  id: Scalars['ID']['input'];
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  isTicketAvailableAtDoor?: InputMaybe<Scalars['Boolean']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  lineup?: InputMaybe<Array<InputMaybe<LineupInput>>>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  lockVersion: Scalars['Int']['input'];
  longitude?: InputMaybe<Scalars['Float']['input']>;
  manualValidationEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  marketeerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  maxTicketsLimit?: InputMaybe<Scalars['Int']['input']>;
  media?: InputMaybe<Array<InputMaybe<MediaItemInputObject>>>;
  musicbrainzArtists?: InputMaybe<Array<InputMaybe<MusicbrainzArtists>>>;
  name?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotification?: InputMaybe<Scalars['Boolean']['input']>;
  onSaleNotificationAt?: InputMaybe<Scalars['Time']['input']>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  presentedBy?: InputMaybe<Scalars['String']['input']>;
  products?: InputMaybe<Array<InputMaybe<ProductInput>>>;
  pwlWindow?: InputMaybe<Scalars['Int']['input']>;
  readAccessEmails?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  reason?: InputMaybe<Scalars['String']['input']>;
  requiresBoxOfficeTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  requiresTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  restrictCountries?: InputMaybe<Array<InputMaybe<CountryCode>>>;
  restrictCountriesKind?: InputMaybe<RestrictionKind>;
  scheduleStatus?: InputMaybe<ScheduleStatus>;
  seatingChannels?: InputMaybe<Array<InputMaybe<SeatingChannelInput>>>;
  sendReceiptViaSms?: InputMaybe<Scalars['Boolean']['input']>;
  showArtistDescription?: InputMaybe<ShowArtistDescription>;
  socialDistancingRulesetKey?: InputMaybe<Scalars['String']['input']>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  thirdPartySettingsId?: InputMaybe<Scalars['ID']['input']>;
  ticketPools?: InputMaybe<Array<InputMaybe<MinorTicketPoolInput>>>;
  ticketTypes?: InputMaybe<Array<InputMaybe<MinorTicketTypeInput>>>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  venueConfigurationId?: InputMaybe<Scalars['ID']['input']>;
  venueSpaceId?: InputMaybe<Scalars['ID']['input']>;
  waitingListExchangeWindows?: InputMaybe<Array<InputMaybe<WaitingListExchangeWindowInput>>>;
};

export type MinorUpdateEventPayload = {
  __typename?: 'MinorUpdateEventPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type MusicbrainzArtist = Name & Node & {
  __typename?: 'MusicbrainzArtist';
  disambiguation?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
};

export type MusicbrainzArtists = {
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type Name = {
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
};

export type NewProductInput = {
  allTicketTypes?: InputMaybe<Scalars['Boolean']['input']>;
  allocation?: InputMaybe<Scalars['Int']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  categoryId?: InputMaybe<Scalars['ID']['input']>;
  customCover?: InputMaybe<Scalars['Boolean']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  excludedFromCrmAutomation?: InputMaybe<Scalars['Boolean']['input']>;
  faceValue?: InputMaybe<Scalars['Int']['input']>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  fulfilledBy?: InputMaybe<Scalars['String']['input']>;
  hasSeparateAccessBarcodes?: InputMaybe<Scalars['Boolean']['input']>;
  hasVariants?: InputMaybe<Scalars['Boolean']['input']>;
  locationNote?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  optionType?: InputMaybe<ProductOptionType>;
  productImages?: InputMaybe<Array<InputMaybe<ProductImageInput>>>;
  productType?: InputMaybe<ProductType>;
  purchaseConfirmationMessage?: InputMaybe<Scalars['String']['input']>;
  sellingPoints?: InputMaybe<Array<InputMaybe<SellingPointInput>>>;
  sku?: InputMaybe<Scalars['String']['input']>;
  variants?: InputMaybe<Array<InputMaybe<VariantInput>>>;
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export type Node = {
  /** The ID of the object. */
  id: Scalars['ID']['output'];
};

export type NotificationBatch = {
  __typename?: 'NotificationBatch';
  id?: Maybe<Scalars['ID']['output']>;
  message: Scalars['String']['output'];
  notifyOn: Scalars['Time']['output'];
  reason?: Maybe<NotificationReason>;
  state?: Maybe<NotificationBatchState>;
};

export enum NotificationBatchState {
  DISABLED = 'DISABLED',
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENT = 'SENT'
}

export type NotificationPermissions = {
  __typename?: 'NotificationPermissions';
  allowEventApprovedNotification?: Maybe<Scalars['Boolean']['output']>;
  allowEventSubmittedNotification?: Maybe<Scalars['Boolean']['output']>;
  allowNewDraftNotification?: Maybe<Scalars['Boolean']['output']>;
};

export type NotificationPermissionsInput = {
  allowEventApprovedNotification?: InputMaybe<Scalars['Boolean']['input']>;
  allowEventSubmittedNotification?: InputMaybe<Scalars['Boolean']['input']>;
  allowNewDraftNotification?: InputMaybe<Scalars['Boolean']['input']>;
};

export enum NotificationReason {
  CANCELLED = 'CANCELLED',
  RESCHEDULED = 'RESCHEDULED'
}

export type OperatorsBooleanEqInput = {
  eq?: InputMaybe<Scalars['Boolean']['input']>;
  ne?: InputMaybe<Scalars['Boolean']['input']>;
  neOrNull?: InputMaybe<Scalars['Boolean']['input']>;
};

export type OperatorsDateInput = {
  between?: InputMaybe<Array<InputMaybe<Scalars['Time']['input']>>>;
  gt?: InputMaybe<Scalars['Time']['input']>;
  gte?: InputMaybe<Scalars['Time']['input']>;
  lt?: InputMaybe<Scalars['Time']['input']>;
  lte?: InputMaybe<Scalars['Time']['input']>;
  notBetween?: InputMaybe<Array<InputMaybe<Scalars['Time']['input']>>>;
  null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type OperatorsEnumCountryCode = {
  eq?: InputMaybe<CountryCode>;
  in?: InputMaybe<Array<InputMaybe<CountryCode>>>;
};

export type OperatorsEnumEventReviewStatus = {
  eq?: InputMaybe<EventReviewStatus>;
  in?: InputMaybe<Array<InputMaybe<EventReviewStatus>>>;
};

export type OperatorsEnumPromoterTier = {
  eq?: InputMaybe<PromoterTier>;
  in?: InputMaybe<Array<InputMaybe<PromoterTier>>>;
};

export type OperatorsEnumStripeRestrictionStatus = {
  eq?: InputMaybe<StripeRestrictionStatus>;
  in?: InputMaybe<Array<InputMaybe<StripeRestrictionStatus>>>;
};

export type OperatorsEnumTypeOfOrganizer = {
  eq?: InputMaybe<EnumTypeOfOrganizer>;
  in?: InputMaybe<Array<InputMaybe<EnumTypeOfOrganizer>>>;
};

export type OperatorsFanConnectStatusInput = {
  eq?: InputMaybe<FanConnectStatus>;
  in?: InputMaybe<Array<InputMaybe<FanConnectStatus>>>;
  ne?: InputMaybe<FanConnectStatus>;
  notIn?: InputMaybe<Array<InputMaybe<FanConnectStatus>>>;
};

export type OperatorsIdEqInput = {
  eq?: InputMaybe<Scalars['ID']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  ne?: InputMaybe<Scalars['ID']['input']>;
  neOrNull?: InputMaybe<Scalars['ID']['input']>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type OperatorsInteger = {
  between?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  eq?: InputMaybe<Scalars['Int']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  ne?: InputMaybe<Scalars['Int']['input']>;
  neOrNull?: InputMaybe<Scalars['Int']['input']>;
  notBetween?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};

export type OperatorsJsonInput = {
  eq?: InputMaybe<Scalars['Map']['input']>;
  ne?: InputMaybe<Scalars['Map']['input']>;
  neOrNull?: InputMaybe<Scalars['Map']['input']>;
};

export type OperatorsListOfStringInput = {
  contains?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  eq?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  ne?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  notContains?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OperatorsString = {
  eq?: InputMaybe<Scalars['String']['input']>;
  iLike?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  ne?: InputMaybe<Scalars['String']['input']>;
  neOrNull?: InputMaybe<Scalars['String']['input']>;
  notLike?: InputMaybe<Scalars['String']['input']>;
  null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type PageInfo = {
  __typename?: 'PageInfo';
  /** When paginating forwards, the cursor to continue. */
  endCursor?: Maybe<Scalars['String']['output']>;
  /** When paginating forwards, are there more items? */
  hasNextPage: Scalars['Boolean']['output'];
  /** When paginating backwards, are there more items? */
  hasPreviousPage: Scalars['Boolean']['output'];
  /** When paginating backwards, the cursor to continue. */
  startCursor?: Maybe<Scalars['String']['output']>;
};

export type PamUser = {
  __typename?: 'PamUser';
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type ParentCategoryBreakdown = {
  __typename?: 'ParentCategoryBreakdown';
  category?: Maybe<Category>;
  categoryBreakdown?: Maybe<Array<Maybe<CategoryBreakdown>>>;
};

export type PartnerPermissionProfileOverride = {
  __typename?: 'PartnerPermissionProfileOverride';
  permissionProfile: PermissionProfile;
  subjects: Array<Maybe<Subject>>;
};

export enum PartnerRefundReason {
  ACCESSIBILITY_REQUEST = 'ACCESSIBILITY_REQUEST',
  ACCIDENTAL_PURCHASE = 'ACCIDENTAL_PURCHASE',
  EVENT_BUILD_ERROR_PARTNER = 'EVENT_BUILD_ERROR_PARTNER',
  EVENT_COMPLAINT = 'EVENT_COMPLAINT',
  EVENT_POSTPONED_RESCHEDULED = 'EVENT_POSTPONED_RESCHEDULED',
  FRAUD_PREVENTION = 'FRAUD_PREVENTION',
  GOOD_WILL = 'GOOD_WILL',
  NO_VALID_ID = 'NO_VALID_ID',
  OTHER = 'OTHER',
  REJECTED_AT_THE_DOOR = 'REJECTED_AT_THE_DOOR',
  SUSPECTED_RESELLER = 'SUSPECTED_RESELLER',
  VENUE_COMPLAINT = 'VENUE_COMPLAINT'
}

export type PauseAllocationInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type PauseAllocationPayload = {
  __typename?: 'PauseAllocationPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export enum PaymentMethod {
  APP = 'APP',
  CARD = 'CARD',
  POS = 'POS'
}

export enum PaymentMethods {
  AFTERPAY_CLEARPAY = 'AFTERPAY_CLEARPAY',
  DICE_SPLIT = 'DICE_SPLIT',
  GIROPAY = 'GIROPAY'
}

export type Payout = {
  __typename?: 'Payout';
  amount: Scalars['Float']['output'];
  arrivalDate: Scalars['Time']['output'];
  created: Scalars['Time']['output'];
  currency?: Maybe<Scalars['String']['output']>;
  details?: Maybe<Scalars['String']['output']>;
  /** @deprecated Use events to get list of events instead */
  event?: Maybe<Event>;
  events?: Maybe<Array<Maybe<Event>>>;
  failure?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  isAdvance: Scalars['Boolean']['output'];
  isManual: Scalars['Boolean']['output'];
  means: PayoutMeans;
  payoutId?: Maybe<Scalars['String']['output']>;
  reason: PayoutReason;
  status: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type PayoutConnection = {
  __typename?: 'PayoutConnection';
  edges?: Maybe<Array<Maybe<PayoutEdge>>>;
  pageInfo: PageInfo;
};

export type PayoutEdge = {
  __typename?: 'PayoutEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Payout>;
};

export enum PayoutImportType {
  /** Deductions format requried for this import type */
  DEDUCTIONS = 'DEDUCTIONS',
  /** @deprecated Deductions format become unified format */
  PAYOUTS = 'PAYOUTS'
}

export enum PayoutMeans {
  AUTO_BY_DICE = 'AUTO_BY_DICE',
  AUTO_BY_PROMOTER = 'AUTO_BY_PROMOTER',
  AUTO_BY_SYSTEM = 'AUTO_BY_SYSTEM',
  BALANCE_ADJUSTMENT = 'BALANCE_ADJUSTMENT',
  /** @deprecated replaced by balance adjustment */
  CREDIT = 'CREDIT',
  MANUAL_BANK_TRANSFER = 'MANUAL_BANK_TRANSFER',
  MANUAL_VIA_STRIPE_UI = 'MANUAL_VIA_STRIPE_UI',
  ROLLING = 'ROLLING'
}

export enum PayoutReason {
  /** @deprecated Use MISC_ADJUSTMENT instead */
  ADJUSTMENT = 'ADJUSTMENT',
  ADVANCE_RECOUPMENT = 'ADVANCE_RECOUPMENT',
  CHARGEBACK_DEDUCTION = 'CHARGEBACK_DEDUCTION',
  /** @deprecated Use CHARGEBACK_DEDUCTION instead */
  CHARGEBACK_RECOUPMENT = 'CHARGEBACK_RECOUPMENT',
  MISC_ADJUSTMENT = 'MISC_ADJUSTMENT',
  PAYMENT = 'PAYMENT',
  ROLLING_PAYMENTS_ADJUSTMENT = 'ROLLING_PAYMENTS_ADJUSTMENT',
  WITHDRAWAL = 'WITHDRAWAL',
  /** @deprecated Use WITHDRAWAL instead */
  WITHDRAWALS = 'WITHDRAWALS'
}

export enum PayoutsExportKind {
  CSV = 'CSV',
  RWQ = 'RWQ'
}

export enum PayoutsOrder {
  ACCOUNT_NAME_ASC = 'ACCOUNT_NAME_ASC',
  ACCOUNT_NAME_DESC = 'ACCOUNT_NAME_DESC',
  AMOUNT_ASC = 'AMOUNT_ASC',
  AMOUNT_DESC = 'AMOUNT_DESC',
  ARRIVAL_DATE_ASC = 'ARRIVAL_DATE_ASC',
  ARRIVAL_DATE_DESC = 'ARRIVAL_DATE_DESC',
  CREATED_ASC = 'CREATED_ASC',
  CREATED_DESC = 'CREATED_DESC',
  EVENT_NAME_ASC = 'EVENT_NAME_ASC',
  EVENT_NAME_DESC = 'EVENT_NAME_DESC'
}

export type PayoutsWhereInput = {
  accountId?: InputMaybe<OperatorsIdEqInput>;
  amount?: InputMaybe<OperatorsInteger>;
  arrivalDate?: InputMaybe<OperatorsDateInput>;
  created?: InputMaybe<OperatorsDateInput>;
  eventId?: InputMaybe<OperatorsIdEqInput>;
  status?: InputMaybe<OperatorsString>;
};

export type PerformPayoutForAccountInput = {
  amount: Scalars['Int']['input'];
  clientMutationId: Scalars['String']['input'];
  currency: EventCostCurrency;
  id: Scalars['ID']['input'];
};

export type PerformPayoutForAccountPayload = {
  __typename?: 'PerformPayoutForAccountPayload';
  clientMutationId: Scalars['String']['output'];
  payoutId: Scalars['ID']['output'];
};

export enum PerformerType {
  ARTIST = 'ARTIST',
  COMEDIAN = 'COMEDIAN'
}

export type PermissionProfile = Node & {
  __typename?: 'PermissionProfile';
  caption: Scalars['String']['output'];
  diceStaff: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  insertedAt: Scalars['Time']['output'];
  roleName: Scalars['String']['output'];
  source: PermissionProfileSource;
  subjects?: Maybe<Array<Maybe<Subject>>>;
  updatedAt: Scalars['Time']['output'];
  usedBy: Scalars['Int']['output'];
};

export type PermissionProfileOverride = {
  __typename?: 'PermissionProfileOverride';
  id: Scalars['ID']['output'];
  permissionProfile: PermissionProfile;
  subjects: Array<Maybe<Subject>>;
};

export type PermissionProfileOverrideInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  permissionProfileId?: InputMaybe<Scalars['ID']['input']>;
  subjects?: InputMaybe<Array<InputMaybe<SubjectInput>>>;
};

export enum PermissionProfileSource {
  ACCOUNT = 'ACCOUNT',
  SYSTEM = 'SYSTEM'
}

export type PermissionProfileStructure = {
  __typename?: 'PermissionProfileStructure';
  subjects?: Maybe<Array<Maybe<Subject>>>;
};

export enum PlatformAccountCode {
  AU = 'AU',
  CA = 'CA',
  DE = 'DE',
  ES = 'ES',
  FR = 'FR',
  GB = 'GB',
  IN = 'IN',
  IT = 'IT',
  PT = 'PT',
  US = 'US'
}

export type Price = {
  __typename?: 'Price';
  breakdown: Array<Maybe<FeeOutput>>;
  faceValue: Scalars['Int']['output'];
  fees: Scalars['Int']['output'];
  friendlyFaceValue?: Maybe<Scalars['Int']['output']>;
  friendlyPrice?: Maybe<Scalars['Int']['output']>;
  salesTax: Scalars['Int']['output'];
  split: Array<Maybe<PriceSplit>>;
  total: Scalars['Int']['output'];
  totalWithPwl?: Maybe<Scalars['Int']['output']>;
  totalWithoutPwl?: Maybe<Scalars['Int']['output']>;
  vatAmount: Scalars['Int']['output'];
  vatRate: Scalars['Float']['output'];
};

export type PriceBreakdownInterface = {
  price?: Maybe<Scalars['Int']['output']>;
  priceBreakdown?: Maybe<Price>;
  rebate?: Maybe<Scalars['Int']['output']>;
};

export type PriceComponent = {
  __typename?: 'PriceComponent';
  dice?: Maybe<Scalars['Int']['output']>;
  partner?: Maybe<Scalars['Int']['output']>;
  total?: Maybe<Scalars['Int']['output']>;
  type?: Maybe<PriceComponentType>;
};

export enum PriceComponentType {
  ADDITIONALPROMOTERFEE = 'ADDITIONALPROMOTERFEE',
  ADJUSTMENTLOSS = 'ADJUSTMENTLOSS',
  BOOKING = 'BOOKING',
  BOXOFFICEFEE = 'BOXOFFICEFEE',
  CHARITYDONATION = 'CHARITYDONATION',
  DEPOSIT = 'DEPOSIT',
  DICECOST = 'DICECOST',
  DISCOUNT = 'DISCOUNT',
  EXTRACHARGE = 'EXTRACHARGE',
  FACEVALUE = 'FACEVALUE',
  FACILITYFEE = 'FACILITYFEE',
  OPERATIONALCOST = 'OPERATIONALCOST',
  OPPORTUNITYCOST = 'OPPORTUNITYCOST',
  PAIDWAITINGLIST = 'PAIDWAITINGLIST',
  POSTAL = 'POSTAL',
  PRESALE = 'PRESALE',
  PROCESSING = 'PROCESSING',
  SALESTAX = 'SALESTAX',
  TAXCOST = 'TAXCOST',
  TIERDIFF = 'TIERDIFF',
  VENDOR = 'VENDOR',
  VENUEFEE = 'VENUEFEE',
  VENUELEVY = 'VENUELEVY'
}

export type PriceRange = {
  __typename?: 'PriceRange';
  from: Scalars['Int']['output'];
  to: Scalars['Int']['output'];
};

export type PriceSplit = {
  __typename?: 'PriceSplit';
  computed: Scalars['Int']['output'];
  destination: FeeDestination;
};

export type PriceTier = FaceValueInterface & Fees & Node & PriceBreakdownInterface & {
  __typename?: 'PriceTier';
  allocation?: Maybe<Scalars['Int']['output']>;
  attractivePriceType?: Maybe<Scalars['String']['output']>;
  doorSalesPrice?: Maybe<Scalars['Int']['output']>;
  doorSalesPriceTaxed?: Maybe<Scalars['Int']['output']>;
  faceValue: Scalars['Int']['output'];
  fees?: Maybe<Array<Maybe<Fee>>>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  order: Scalars['Int']['output'];
  price?: Maybe<Scalars['Int']['output']>;
  priceBreakdown?: Maybe<Price>;
  rebate?: Maybe<Scalars['Int']['output']>;
  ticketTypeId?: Maybe<Scalars['Int']['output']>;
  time?: Maybe<Scalars['Time']['output']>;
};

export type PriceTierBreakdown = {
  __typename?: 'PriceTierBreakdown';
  appSold: Scalars['Int']['output'];
  digitalValue: Scalars['Int']['output'];
  fees: Array<Maybe<SalesFees>>;
  order: Scalars['Int']['output'];
  payoutValue: Scalars['Int']['output'];
  posSold: Scalars['Int']['output'];
  priceTier: PriceTier;
  promotionsBreakdown: Array<Maybe<PromotionBreakdown>>;
  rebate: Scalars['Int']['output'];
  reserved: Scalars['Int']['output'];
  sold: Scalars['Int']['output'];
  terminalSold: Scalars['Int']['output'];
  value: Scalars['Int']['output'];
};

export enum PriceTierTypes {
  allocation = 'allocation',
  time = 'time'
}

export type PriceTiersInput = {
  allocation?: InputMaybe<Scalars['Int']['input']>;
  attractivePriceType?: InputMaybe<Scalars['String']['input']>;
  doorSalesPrice?: InputMaybe<Scalars['Int']['input']>;
  faceValue?: InputMaybe<Scalars['Int']['input']>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  time?: InputMaybe<Scalars['Time']['input']>;
};

export type PrintableTicketLegalDetails = {
  __typename?: 'PrintableTicketLegalDetails';
  activationCard?: Maybe<Scalars['String']['output']>;
  emissionDate?: Maybe<Scalars['String']['output']>;
  eventGenre?: Maybe<Scalars['String']['output']>;
  faceValue?: Maybe<Scalars['String']['output']>;
  fiscalSeal?: Maybe<Scalars['String']['output']>;
  mediaType?: Maybe<Scalars['String']['output']>;
  piHolder?: Maybe<Scalars['String']['output']>;
  piOrganiser?: Maybe<Scalars['String']['output']>;
  presaleFee?: Maybe<Scalars['String']['output']>;
  priceType?: Maybe<Scalars['String']['output']>;
  progressiveNumber?: Maybe<Scalars['String']['output']>;
  seatingArea?: Maybe<Scalars['String']['output']>;
  system?: Maybe<Scalars['String']['output']>;
};

export enum PrintedTicketFormat {
  BOCA_6X3_NO_LOGO = 'BOCA_6X3_NO_LOGO',
  BOCA_55X2 = 'BOCA_55X2',
  NO_PRINTER = 'NO_PRINTER',
  STAR_RECEIPT = 'STAR_RECEIPT',
  STAR_RECEIPT_ETICKET = 'STAR_RECEIPT_ETICKET'
}

export type Product = Fees & Node & PriceBreakdownInterface & {
  __typename?: 'Product';
  allTicketTypes: Scalars['Boolean']['output'];
  allocation: Scalars['Int']['output'];
  archived: Scalars['Boolean']['output'];
  artistEvents?: Maybe<Array<Maybe<ArtistEvent>>>;
  category: Category;
  customCover?: Maybe<Scalars['Boolean']['output']>;
  date?: Maybe<Scalars['Time']['output']>;
  description: Scalars['String']['output'];
  endDate?: Maybe<Scalars['Time']['output']>;
  event?: Maybe<Event>;
  excludedFromCrmAutomation: Scalars['Boolean']['output'];
  faceValue: Scalars['Int']['output'];
  fees?: Maybe<Array<Maybe<Fee>>>;
  fulfilledBy?: Maybe<Scalars['String']['output']>;
  hasSeparateAccessBarcodes: Scalars['Boolean']['output'];
  hasVariants: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  linkedLiveEventsCount: Scalars['Int']['output'];
  locationNote?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  offSaleDate?: Maybe<Scalars['Time']['output']>;
  onSaleDate?: Maybe<Scalars['Time']['output']>;
  optionType?: Maybe<ProductOptionType>;
  price?: Maybe<Scalars['Int']['output']>;
  priceBreakdown?: Maybe<Price>;
  productImages?: Maybe<Array<Maybe<ProductImage>>>;
  productType: ProductType;
  purchaseConfirmationMessage?: Maybe<Scalars['String']['output']>;
  rebate?: Maybe<Scalars['Int']['output']>;
  rootType: ProductRootType;
  sellingPoints: Array<Maybe<SellingPoint>>;
  sku?: Maybe<Scalars['String']['output']>;
  ticketTypes: Array<Maybe<TicketType>>;
  variants: Array<Maybe<Variant>>;
  venue?: Maybe<Venue>;
};

export type ProductBalance = {
  __typename?: 'ProductBalance';
  diceFees?: Maybe<Scalars['Int']['output']>;
  diceSalesTax?: Maybe<Scalars['Int']['output']>;
  partnerPayout?: Maybe<Scalars['Int']['output']>;
  total?: Maybe<Scalars['Int']['output']>;
};

export type ProductBreakdownItem = {
  __typename?: 'ProductBreakdownItem';
  product: Product;
  productId: Scalars['ID']['output'];
  totalAppSold: Scalars['Int']['output'];
  totalDigitalValue: Scalars['Int']['output'];
  totalFaceValue: Scalars['Int']['output'];
  totalSold: Scalars['Int']['output'];
  variantBreakdown?: Maybe<Array<Maybe<VariantBreakdownItem>>>;
};

export type ProductImage = Node & {
  __typename?: 'ProductImage';
  attachment?: Maybe<Attachment>;
  cdnUrl: Scalars['String']['output'];
  cropRegion?: Maybe<CropRegion>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
};

export type ProductImageInput = {
  attachmentId?: InputMaybe<Scalars['ID']['input']>;
  cropRegion?: InputMaybe<CropRegionInput>;
  id?: InputMaybe<Scalars['ID']['input']>;
};

export type ProductInput = {
  allTicketTypes?: InputMaybe<Scalars['Boolean']['input']>;
  allocation?: InputMaybe<Scalars['Int']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  categoryId?: InputMaybe<Scalars['ID']['input']>;
  customCover?: InputMaybe<Scalars['Boolean']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventId?: InputMaybe<Scalars['ID']['input']>;
  excludedFromCrmAutomation?: InputMaybe<Scalars['Boolean']['input']>;
  faceValue?: InputMaybe<Scalars['Int']['input']>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  fulfilledBy?: InputMaybe<Scalars['String']['input']>;
  hasSeparateAccessBarcodes?: InputMaybe<Scalars['Boolean']['input']>;
  hasVariants?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  locationNote?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  optionType?: InputMaybe<ProductOptionType>;
  productImages?: InputMaybe<Array<InputMaybe<ProductImageInput>>>;
  productType?: InputMaybe<ProductType>;
  purchaseConfirmationMessage?: InputMaybe<Scalars['String']['input']>;
  sellingPoints?: InputMaybe<Array<InputMaybe<SellingPointInput>>>;
  sku?: InputMaybe<Scalars['String']['input']>;
  ticketTypeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  variants?: InputMaybe<Array<InputMaybe<VariantInput>>>;
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export enum ProductOptionType {
  CUSTOM = 'CUSTOM',
  SIZE = 'SIZE'
}

export enum ProductRootType {
  EXTRAS = 'EXTRAS',
  MERCH = 'MERCH'
}

export type ProductStats = {
  __typename?: 'ProductStats';
  priceComponents?: Maybe<Array<Maybe<PriceComponent>>>;
  product: Product;
  sold?: Maybe<Scalars['Int']['output']>;
};

export type ProductToEventsInput = {
  eventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  productId: Scalars['ID']['input'];
};

export enum ProductType {
  ADDON = 'ADDON'
}

export type ProductsList = {
  __typename?: 'ProductsList';
  products?: Maybe<Array<Maybe<Product>>>;
};

export type ProductsRevenueReportItem = {
  __typename?: 'ProductsRevenueReportItem';
  faceValue: Scalars['Int']['output'];
  rebate: Scalars['Int']['output'];
  revenue: Scalars['Int']['output'];
  soldItems: Scalars['Int']['output'];
  time: Scalars['Time']['output'];
};

export type ProductsSales = {
  __typename?: 'ProductsSales';
  parentCategoryBreakdown?: Maybe<Array<Maybe<ParentCategoryBreakdown>>>;
  productBreakdown?: Maybe<Array<Maybe<ProductBreakdownItem>>>;
};


export type ProductsSalesProductBreakdownArgs = {
  rootType?: InputMaybe<ProductRootType>;
};

export type ProfileDetails = {
  __typename?: 'ProfileDetails';
  description?: Maybe<Scalars['String']['output']>;
  imageAttachment?: Maybe<Attachment>;
  imageCropRegion?: Maybe<CropRegion>;
};

export type ProfileDetailsInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  imageAttachmentId?: InputMaybe<Scalars['ID']['input']>;
  imageCropRegion?: InputMaybe<CropRegionInput>;
};

export type Promoter = Contracts & Fees & Location & Name & Node & TimeStamps & {
  __typename?: 'Promoter';
  accountBalance?: Maybe<AccountBalance>;
  accountIban: Scalars['String']['output'];
  accountId: Scalars['ID']['output'];
  /** @deprecated To be removed, use account_managers instead */
  accountManager?: Maybe<User>;
  accountManagers?: Maybe<Array<Maybe<User>>>;
  accountName: Scalars['String']['output'];
  accountNumber: Scalars['String']['output'];
  accountPermissionProfiles?: Maybe<Array<Maybe<PermissionProfile>>>;
  accountSortCode: Scalars['String']['output'];
  accountType: Scalars['String']['output'];
  accountVatNumber: Scalars['String']['output'];
  /** @deprecated To be removed, please use viewer.activities instead */
  activities?: Maybe<ActivitiesConnection>;
  addressCountry?: Maybe<Scalars['String']['output']>;
  addressLocality?: Maybe<Scalars['String']['output']>;
  addressRegion?: Maybe<Scalars['String']['output']>;
  addressState?: Maybe<Scalars['String']['output']>;
  admissionConfigs?: Maybe<Array<Maybe<AdmissionConfig>>>;
  allowSkipReview?: Maybe<Scalars['Boolean']['output']>;
  allowedLifecycleUpdates?: Maybe<PromoterAllowedLifecycleUpdates>;
  apiToken?: Maybe<Scalars['String']['output']>;
  apiTokenExpiryDate?: Maybe<Scalars['Time']['output']>;
  associatedMarketeers?: Maybe<Array<Maybe<Marketeer>>>;
  associatedVenues?: Maybe<Array<Maybe<Venue>>>;
  autoRescheduledEventRefunds?: Maybe<AutoRescheduledEventRefunds>;
  automaticRollingPaymentsConfiguration?: Maybe<AutomaticRollingPaymentsConfiguration>;
  bankAddress?: Maybe<Scalars['String']['output']>;
  bankName?: Maybe<Scalars['String']['output']>;
  billingNotes?: Maybe<Scalars['String']['output']>;
  chargesEnabled?: Maybe<Scalars['Boolean']['output']>;
  charityTaxFree?: Maybe<Scalars['Boolean']['output']>;
  clientSuccessManagers?: Maybe<Array<Maybe<User>>>;
  connectedAccount?: Maybe<ConnectedAccount>;
  contacts?: Maybe<Array<Maybe<User>>>;
  contracts?: Maybe<Array<Maybe<FeesConfiguration>>>;
  coolingOffPeriod?: Maybe<Scalars['Boolean']['output']>;
  coolingOffPeriodHours?: Maybe<Scalars['Int']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['Time']['output']>;
  curatedBundle?: Maybe<Bundle>;
  defaultEventTimings?: Maybe<Array<Maybe<EventTiming>>>;
  dicePartner?: Maybe<Scalars['Boolean']['output']>;
  disableUsTax?: Maybe<Scalars['Boolean']['output']>;
  disabledReason?: Maybe<EnumAccountDisabledReason>;
  displayName?: Maybe<Scalars['String']['output']>;
  eventDefaults?: Maybe<EventDefaults>;
  extrasEnabled?: Maybe<Scalars['Boolean']['output']>;
  fanSupportNotes?: Maybe<FanSupportNotes>;
  fansStats?: Maybe<FansStats>;
  fees?: Maybe<Array<Maybe<Fee>>>;
  forbidSelfPayouts?: Maybe<Scalars['Boolean']['output']>;
  fullAddress?: Maybe<Scalars['String']['output']>;
  holdPayouts?: Maybe<Scalars['Boolean']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  isDisabled?: Maybe<Scalars['Boolean']['output']>;
  isTest?: Maybe<Scalars['Boolean']['output']>;
  labels?: Maybe<Array<Maybe<Label>>>;
  latitude?: Maybe<Scalars['Float']['output']>;
  legalEntity?: Maybe<Scalars['String']['output']>;
  licenseNumber: Scalars['String']['output'];
  links?: Maybe<Array<Maybe<Scalars['Map']['output']>>>;
  liveEventsCount?: Maybe<Scalars['Int']['output']>;
  locationStats: LocationStats;
  longitude?: Maybe<Scalars['Float']['output']>;
  merchEnabled?: Maybe<Scalars['Boolean']['output']>;
  missingStripeFields?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  name?: Maybe<Scalars['String']['output']>;
  notes: Scalars['String']['output'];
  partnerPermissionProfileOverrides?: Maybe<Array<Maybe<PartnerPermissionProfileOverride>>>;
  payoutsEnabled?: Maybe<Scalars['Boolean']['output']>;
  permName?: Maybe<Scalars['String']['output']>;
  permissionProfileOverrides?: Maybe<Array<Maybe<PermissionProfileOverride>>>;
  platformAccountCode?: Maybe<PlatformAccountCode>;
  postOfficeBoxNumber?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  profileActive?: Maybe<Scalars['Boolean']['output']>;
  profileDetails?: Maybe<ProfileDetails>;
  promoterTaxSettings?: Maybe<PromoterTaxSettings>;
  qflowEnabled?: Maybe<Scalars['Boolean']['output']>;
  remittanceRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  resoldEnabled?: Maybe<Scalars['Boolean']['output']>;
  routingNumber: Scalars['String']['output'];
  salesforceContracts?: Maybe<Array<Maybe<SalesforceContract>>>;
  salesforceFields?: Maybe<SalesforcePromoterFields>;
  sendReceiptViaSms?: Maybe<Scalars['Boolean']['output']>;
  showPriceBreakdown: Scalars['Boolean']['output'];
  showPriceSuggestions: Scalars['Boolean']['output'];
  source?: Maybe<UserSource>;
  statusNotes?: Maybe<Scalars['String']['output']>;
  streetAddress?: Maybe<Scalars['String']['output']>;
  stripeAccountCurrencies?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  stripeAccountId?: Maybe<Scalars['String']['output']>;
  stripeAccountType?: Maybe<Scalars['String']['output']>;
  /**
   * The reason for the stripe account to be disabled.
   * Possible values are described in Stripe docs:
   * https://stripe.com/docs/api/accounts/object#account_object-requirements-disabled_reason
   *
   */
  stripeDisabledReason?: Maybe<Scalars['String']['output']>;
  stripeDocumentId?: Maybe<Scalars['String']['output']>;
  stripeFallbackAccountId?: Maybe<Scalars['String']['output']>;
  stripeFallbackPlatformCode?: Maybe<PlatformAccountCode>;
  stripeLocationId?: Maybe<Scalars['String']['output']>;
  stripeLoginUrl?: Maybe<Scalars['String']['output']>;
  stripeOauthUrl: Scalars['String']['output'];
  stripeOnboardingUrl: Scalars['String']['output'];
  stripeRestrictionStatus?: Maybe<StripeRestrictionStatus>;
  stripeSetupInitiated?: Maybe<Scalars['Boolean']['output']>;
  stripeVerified?: Maybe<Scalars['Boolean']['output']>;
  swiftCode: Scalars['String']['output'];
  tags?: Maybe<Array<Maybe<Tag>>>;
  taxCode?: Maybe<Scalars['String']['output']>;
  ticketAgreementComplete?: Maybe<Scalars['Boolean']['output']>;
  tier?: Maybe<Scalars['String']['output']>;
  timezoneName?: Maybe<Scalars['String']['output']>;
  typeOfOrganizer?: Maybe<EnumTypeOfOrganizer>;
  updatedAt?: Maybe<Scalars['Time']['output']>;
  verificationDataSubmitted?: Maybe<Scalars['Boolean']['output']>;
  verificationDueBy?: Maybe<Scalars['Time']['output']>;
};


export type PromoterActivitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};

export type PromoterAllowedLifecycleUpdates = {
  __typename?: 'PromoterAllowedLifecycleUpdates';
  accountManagers?: Maybe<AllowedLifecycleUpdateBase>;
  clientSuccessManagers?: Maybe<AllowedLifecycleUpdateBase>;
};

export type PromoterTaxSettings = {
  __typename?: 'PromoterTaxSettings';
  conferenceTradeshow?: Maybe<Scalars['Boolean']['output']>;
};

export type PromoterTaxSettingsInput = {
  conferenceTradeshow?: InputMaybe<Scalars['Boolean']['input']>;
};

export enum PromoterTier {
  CORE = 'CORE',
  MAX = 'MAX',
  PLUS = 'PLUS',
  PRO = 'PRO',
  WHALE = 'WHALE'
}

export type PromoterWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<PromoterWhereInput>>>;
  accountManagerId?: InputMaybe<OperatorsIdEqInput>;
  chargesEnabled?: InputMaybe<OperatorsBooleanEqInput>;
  countryCode?: InputMaybe<OperatorsString>;
  destinationFallback?: InputMaybe<OperatorsBooleanEqInput>;
  dicePartner?: InputMaybe<OperatorsBooleanEqInput>;
  id?: InputMaybe<OperatorsIdEqInput>;
  name?: InputMaybe<OperatorsString>;
  payoutsEnabled?: InputMaybe<OperatorsBooleanEqInput>;
  profileActive?: InputMaybe<OperatorsBooleanEqInput>;
  stripeRestrictionStatus?: InputMaybe<OperatorsEnumStripeRestrictionStatus>;
  typeOfOrganizer?: InputMaybe<OperatorsEnumTypeOfOrganizer>;
  verificationDueBy?: InputMaybe<OperatorsDateInput>;
};

export type PromotersBundles = {
  owner?: InputMaybe<Scalars['Boolean']['input']>;
  promoterId?: InputMaybe<Scalars['ID']['input']>;
};

export type PromotersConnection = {
  __typename?: 'PromotersConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<PromotersEdge>>>;
  pageInfo: PageInfo;
};

export type PromotersEdge = {
  __typename?: 'PromotersEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Promoter>;
};

export enum PromotersOrder {
  live_events_count_asc = 'live_events_count_asc',
  live_events_count_desc = 'live_events_count_desc'
}

export enum PromotionAccessType {
  GENERAL_CODE = 'GENERAL_CODE',
  UNIQUE_CODE = 'UNIQUE_CODE'
}

export type PromotionBreakdown = {
  __typename?: 'PromotionBreakdown';
  appSold: Scalars['Int']['output'];
  eventPromotion?: Maybe<EventPromotion>;
  faceValue: Scalars['Int']['output'];
  fees: Array<Maybe<SalesFees>>;
  payoutValue: Scalars['Int']['output'];
  posSold: Scalars['Int']['output'];
  price: Scalars['Int']['output'];
  priceWithPwl?: Maybe<Scalars['Int']['output']>;
  priceWithoutPwl?: Maybe<Scalars['Int']['output']>;
  rebate: Scalars['Int']['output'];
  reserved: Scalars['Int']['output'];
  sold: Scalars['Int']['output'];
  terminalSold: Scalars['Int']['output'];
  value: Scalars['Int']['output'];
};

export enum PromotionType {
  CODE_LOCK = 'CODE_LOCK',
  DISCOUNT = 'DISCOUNT'
}

export type PublishSeatingChartInput = {
  clientMutationId: Scalars['String']['input'];
  seatingChartId: Scalars['ID']['input'];
};

export type PublishSeatingChartPayload = {
  __typename?: 'PublishSeatingChartPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<SeatingChart>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type PutAllEventsOffSaleInput = {
  clientMutationId: Scalars['String']['input'];
  promoterId: Scalars['String']['input'];
};

export type PutAllEventsOffSalePayload = {
  __typename?: 'PutAllEventsOffSalePayload';
  clientMutationId: Scalars['String']['output'];
  promoter?: Maybe<Promoter>;
};

export type Recurrence = {
  __typename?: 'Recurrence';
  /** Only used if type is MONTHLY_DOW */
  domRepeat?: Maybe<Array<Maybe<DayOfMonth>>>;
  /** "Every X" (days/weeks/months) */
  frequency: Scalars['Int']['output'];
  /** Unused by DAILY and MONTHLY_DOW types.  Weekly, this is the DOW (0-7) to repeat on, monthly, this is the day-of-the-month (1-31) */
  repeatOn?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  type: RecurrenceType;
};

export type RecurrenceInput = {
  domRepeat?: InputMaybe<Array<InputMaybe<DayOfMonthInput>>>;
  frequency: Scalars['Int']['input'];
  repeatOn: Array<InputMaybe<Scalars['Int']['input']>>;
  type: RecurrenceType;
};

export enum RecurrenceType {
  DAILY = 'DAILY',
  MONTHLY_DAY = 'MONTHLY_DAY',
  MONTHLY_DOW = 'MONTHLY_DOW',
  WEEKLY = 'WEEKLY'
}

export type RecurrentEventsScheduleConfig = {
  __typename?: 'RecurrentEventsScheduleConfig';
  frequency?: Maybe<RepeatFrequency>;
  occurrences?: Maybe<Scalars['Int']['output']>;
  repeatEnds?: Maybe<RepeatEnds>;
  repeatOn?: Maybe<RepeatOn>;
  until?: Maybe<Scalars['Time']['output']>;
};

export type RecurrentEventsScheduleInput = {
  frequency?: InputMaybe<RepeatFrequency>;
  occurrences?: InputMaybe<Scalars['Int']['input']>;
  repeatEnds?: InputMaybe<RepeatEnds>;
  repeatOn?: InputMaybe<RepeatOn>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  until?: InputMaybe<Scalars['Time']['input']>;
};

export type RefundFromDiceInput = {
  clientMutationId: Scalars['String']['input'];
  comment?: InputMaybe<Scalars['String']['input']>;
  lineItemIds: Array<InputMaybe<Scalars['Int']['input']>>;
  purchaseTicketIds: Array<InputMaybe<Scalars['Int']['input']>>;
  refundReason: DiceRefundReason;
  revokeMode: RevokeMode;
};

export type RefundFromDicePayload = {
  __typename?: 'RefundFromDicePayload';
  clientMutationId: Scalars['String']['output'];
  messages?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  result?: Maybe<Scalars['String']['output']>;
  successful?: Maybe<Scalars['Boolean']['output']>;
};

export type RefundFromPartnerInput = {
  clientMutationId: Scalars['String']['input'];
  comment?: InputMaybe<Scalars['String']['input']>;
  eventId: Scalars['ID']['input'];
  lineItemIds: Array<InputMaybe<Scalars['Int']['input']>>;
  purchaseTicketIds: Array<InputMaybe<Scalars['Int']['input']>>;
  refundReason: PartnerRefundReason;
  revokeMode: RevokeMode;
};

export type RefundFromPartnerPayload = {
  __typename?: 'RefundFromPartnerPayload';
  clientMutationId: Scalars['String']['output'];
  messages?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  result?: Maybe<Scalars['String']['output']>;
  successful?: Maybe<Scalars['Boolean']['output']>;
};

export type RefundTicketInput = {
  clientMutationId: Scalars['String']['input'];
  details?: InputMaybe<Scalars['String']['input']>;
  link?: InputMaybe<Scalars['String']['input']>;
  markback: Scalars['Boolean']['input'];
  reason?: InputMaybe<Scalars['String']['input']>;
  revoke: Scalars['Boolean']['input'];
  ticketIds: Array<InputMaybe<Scalars['Int']['input']>>;
};

export type RefundTicketPayload = {
  __typename?: 'RefundTicketPayload';
  clientMutationId: Scalars['String']['output'];
  failed?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  successful?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
};

export type RelatedEvent = {
  __typename?: 'RelatedEvent';
  distance: Scalars['Float']['output'];
  event?: Maybe<Event>;
};

export type RelatedEventViewer = {
  __typename?: 'RelatedEventViewer';
  getRelated?: Maybe<Array<Maybe<RelatedEvent>>>;
};


export type RelatedEventViewerGetRelatedArgs = {
  artistIds: Array<InputMaybe<Scalars['ID']['input']>>;
  city: Scalars['String']['input'];
  eventId?: InputMaybe<Scalars['ID']['input']>;
};

export type RemoveFanConnectInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type RemoveFanConnectPayload = {
  __typename?: 'RemoveFanConnectPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export enum RepeatEnds {
  OCCURRENCES = 'OCCURRENCES',
  UNTIL = 'UNTIL'
}

export enum RepeatFrequency {
  BI_WEEKLY = 'BI_WEEKLY',
  DAILY = 'DAILY',
  MONTHLY = 'MONTHLY',
  WEEKLY = 'WEEKLY'
}

export enum RepeatOn {
  LAST_WEEK_DAY = 'LAST_WEEK_DAY',
  SAME_DAY = 'SAME_DAY',
  SAME_WEEK_AND_DAY = 'SAME_WEEK_AND_DAY'
}

export type ReportOrSchedule = ReportSchedule | ScheduledReport;

/** Report Schedule or Scheduled Report */
export type ReportOrScheduleObject = ReportSchedule | ScheduledReport;

export type ReportOrScheduleObjectConnection = {
  __typename?: 'ReportOrScheduleObjectConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<ReportOrScheduleObjectEdge>>>;
  pageInfo: PageInfo;
};

export type ReportOrScheduleObjectEdge = {
  __typename?: 'ReportOrScheduleObjectEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<ReportOrScheduleObject>;
};

export type ReportSchedule = Node & {
  __typename?: 'ReportSchedule';
  archived: Scalars['Boolean']['output'];
  emailList?: Maybe<Array<Maybe<Scalars['EmailAddress']['output']>>>;
  endAt?: Maybe<Scalars['Time']['output']>;
  event?: Maybe<Event>;
  /** Whether any reports have a failed email */
  hasFailedEmails: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  insertedAt?: Maybe<Scalars['Time']['output']>;
  lastUpdatedBy?: Maybe<User>;
  locale?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  options?: Maybe<ScheduledReportOptions>;
  promoter?: Maybe<Promoter>;
  recurrence: Recurrence;
  reportType: ReportType;
  scheduledReports?: Maybe<ScheduledReportsConnection>;
  /** Count of sent reports for this schedule */
  sentCount: Scalars['Int']['output'];
  startAt: Scalars['Time']['output'];
  timezoneName: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['Time']['output']>;
  user?: Maybe<User>;
};


export type ReportScheduleScheduledReportsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<DateRangeInput>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};

export enum ReportType {
  BALANCE_REPORT = 'BALANCE_REPORT',
  EVENT_REPORT = 'EVENT_REPORT',
  EXTRA_TRANSACTION_REPORT = 'EXTRA_TRANSACTION_REPORT',
  PROMOTER_FANS_DATA_REPORT = 'PROMOTER_FANS_DATA_REPORT',
  SEATING_REPORT = 'SEATING_REPORT',
  TICKET_COUNT_REPORT = 'TICKET_COUNT_REPORT'
}

export type ReportsAndSchedulesFilter = {
  /** Ignored unless user is DICE Staff */
  accountId?: InputMaybe<Scalars['ID']['input']>;
  deliveryDateFrom?: InputMaybe<Scalars['Time']['input']>;
  deliveryDateTo?: InputMaybe<Scalars['Time']['input']>;
  frequency?: InputMaybe<Array<InputMaybe<ReportsAndSchedulesFrequency>>>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Array<InputMaybe<ReportsAndSchedulesStatus>>>;
};

export enum ReportsAndSchedulesFrequency {
  ANNUALLY = 'ANNUALLY',
  DAILY = 'DAILY',
  MONTHLY = 'MONTHLY',
  ONE_OFF = 'ONE_OFF',
  WEEKDAYS = 'WEEKDAYS',
  WEEKLY = 'WEEKLY'
}

export enum ReportsAndSchedulesStatus {
  ARCHIVED = 'ARCHIVED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
  SCHEDULED = 'SCHEDULED',
  SENT = 'SENT'
}

export type RequestMailchimpCredentialsInput = {
  clientMutationId: Scalars['String']['input'];
  code: Scalars['String']['input'];
};

export type RequestMailchimpCredentialsPayload = {
  __typename?: 'RequestMailchimpCredentialsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type ResendFanConnectInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['String']['input'];
};

export type ResendFanConnectPayload = {
  __typename?: 'ResendFanConnectPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<FanConnect>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type ResendInviteInstructionsInput = {
  accountId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
  invitationId: Scalars['ID']['input'];
};

export type ResendInviteInstructionsPayload = {
  __typename?: 'ResendInviteInstructionsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<AccountUserInvitation>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type ResendTicketsPurchasedNotificationInput = {
  clientMutationId: Scalars['String']['input'];
  purchaseId: Scalars['Int']['input'];
};

export type ResendTicketsPurchasedNotificationPayload = {
  __typename?: 'ResendTicketsPurchasedNotificationPayload';
  clientMutationId: Scalars['String']['output'];
  messages?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  result?: Maybe<Scalars['String']['output']>;
  successful?: Maybe<Scalars['Boolean']['output']>;
};

export enum ReservedSeatingTypes {
  assignBestSeat = 'assignBestSeat',
  selectSeat = 'selectSeat'
}

export type ResetDoorlistStatusInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type ResetDoorlistStatusPayload = {
  __typename?: 'ResetDoorlistStatusPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export enum RestrictionKind {
  ALLOW = 'ALLOW',
  DENY = 'DENY'
}

export type ResumeAllocationInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type ResumeAllocationPayload = {
  __typename?: 'ResumeAllocationPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type RetryFailedEmailsInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type RetryFailedEmailsPayload = {
  __typename?: 'RetryFailedEmailsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ScheduledReport>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type RevenueReportItem = {
  __typename?: 'RevenueReportItem';
  faceValue: Scalars['Int']['output'];
  rebate: Scalars['Int']['output'];
  revenue: Scalars['Int']['output'];
  soldTickets: Scalars['Int']['output'];
  ticketTypesBreakdown?: Maybe<Array<Maybe<RevenueTicketTypeBreakdown>>>;
  time: Scalars['Time']['output'];
};

export type RevenueTicketTypeBreakdown = {
  __typename?: 'RevenueTicketTypeBreakdown';
  faceValue: Scalars['Int']['output'];
  rebate: Scalars['Int']['output'];
  revenue: Scalars['Int']['output'];
  soldTickets: Scalars['Int']['output'];
  ticketTypeId: Scalars['ID']['output'];
};

export enum RevokeMode {
  KEEP_WITH_FAN = 'KEEP_WITH_FAN',
  REVOKE_AND_MARKBACK = 'REVOKE_AND_MARKBACK',
  REVOKE_AND_PUT_ON_SALE = 'REVOKE_AND_PUT_ON_SALE'
}

export type RevokeUserAccessInput = {
  accountId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
  userOrInvitationId: Scalars['ID']['input'];
};

export type RevokeUserAccessPayload = {
  __typename?: 'RevokeUserAccessPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<UserAccountObject>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type RootMutationType = {
  __typename?: 'RootMutationType';
  archiveFanConnect?: Maybe<ArchiveFanConnectPayload>;
  assignProductsToEvents?: Maybe<AssignProductsToEventsPayload>;
  boxOfficePayment?: Maybe<BoxOfficePaymentPayload>;
  bulkUpdateFanSurveyQuestions?: Maybe<BulkUpdateFanSurveyQuestionsPayload>;
  cancelReport?: Maybe<CancelReportPayload>;
  changeAllocation?: Maybe<ChangeAllocationPayload>;
  changeDates?: Maybe<ChangeDatesPayload>;
  cloneEvent?: Maybe<CloneEventPayload>;
  cloneEventPromotion?: Maybe<CloneEventPromotionPayload>;
  copySeatingChart?: Maybe<CopySeatingChartPayload>;
  /** create an admission config */
  createAdmissionConfig?: Maybe<CreateAdmissionConfigPayload>;
  createArtist?: Maybe<CreateArtistPayload>;
  createAttachment?: Maybe<CreateAttachmentPayload>;
  createBundle?: Maybe<CreateBundlePayload>;
  createCommentActivity?: Maybe<CreateCommentActivityPayload>;
  createDraftEvent?: Maybe<CreateDraftEventPayload>;
  createEvent?: Maybe<CreateEventPayload>;
  createEventChangedNotification?: Maybe<CreateEventChangedNotificationPayload>;
  createEventPromotion?: Maybe<CreateEventPromotionPayload>;
  createEventPromotionCodes?: Maybe<CreateEventPromotionCodesPayload>;
  createEventSeatingChannel?: Maybe<CreateEventSeatingChannelPayload>;
  createEventSeatingChart?: Maybe<CreateEventSeatingChartPayload>;
  createFacebookEvent?: Maybe<CreateFacebookEventPayload>;
  createFanConnect?: Maybe<CreateFanConnectPayload>;
  createFeeConfiguration?: Maybe<CreateFeeConfigurationPayload>;
  createHierarchicalTag?: Maybe<CreateHierarchicalTagPayload>;
  createIntegrationToken?: Maybe<CreateIntegrationTokenPayload>;
  createLabel?: Maybe<CreateLabelPayload>;
  createLinkout?: Maybe<CreateLinkoutPayload>;
  createManualPayout?: Maybe<CreateManualPayoutPayload>;
  createMarketeer?: Maybe<CreateMarketeerPayload>;
  createNotificationBatch?: Maybe<CreateNotificationBatchPayload>;
  createPayout?: Maybe<CreatePayoutPayload>;
  createPermissionProfile?: Maybe<CreatePermissionProfilePayload>;
  createProduct?: Maybe<CreateProductPayload>;
  createProductsList?: Maybe<CreateProductsListPayload>;
  createPromoter?: Maybe<CreatePromoterPayload>;
  createReportSchedule?: Maybe<CreateReportSchedulePayload>;
  createRestrictedIntegrationToken?: Maybe<CreateRestrictedIntegrationTokenPayload>;
  createScheduledReport?: Maybe<CreateScheduledReportPayload>;
  createSeatingChart?: Maybe<CreateSeatingChartPayload>;
  createSocialLink?: Maybe<CreateSocialLinkPayload>;
  createTag?: Maybe<CreateTagPayload>;
  createTempAttachment?: Maybe<CreateTempAttachmentPayload>;
  /**
   * Mutation to create third party APP settings.
   *
   */
  createThirdPartySettings?: Maybe<CreateThirdPartySettingsPayload>;
  createTicketPool?: Maybe<CreateTicketPoolPayload>;
  createUser?: Maybe<CreateUserPayload>;
  createVenue?: Maybe<CreateVenuePayload>;
  /**
   * delete an admission config
   * note: if a user tries to delete a config with existing scanned tickets the server returns `existing_admission_log`.
   * please suggest to the user to disable the config instead.
   *
   */
  deleteAdmissionConfig?: Maybe<DeleteAdmissionConfigPayload>;
  deleteEventPromotionCodes?: Maybe<DeleteEventPromotionCodesPayload>;
  deleteEventSeatingChannel?: Maybe<DeleteEventSeatingChannelPayload>;
  deleteFacebookEvent?: Maybe<DeleteFacebookEventPayload>;
  deleteIntegrationToken?: Maybe<DeleteIntegrationTokenPayload>;
  deleteMailchimpSettings?: Maybe<DeleteMailchimpSettingsPayload>;
  deleteManualPayout?: Maybe<DeleteManualPayoutPayload>;
  deleteNode?: Maybe<DeleteNodePayload>;
  deleteReportSchedule?: Maybe<DeleteReportSchedulePayload>;
  deleteScheduledReport?: Maybe<DeleteScheduledReportPayload>;
  deleteTicketPool?: Maybe<DeleteTicketPoolPayload>;
  disableFanSurvey?: Maybe<DisableFanSurveyPayload>;
  disableSeatingChart?: Maybe<DisableSeatingChartPayload>;
  doorSalesCharge?: Maybe<DoorSalesChargePayload>;
  doorSalesCreateReader?: Maybe<DoorSalesCreateReaderPayload>;
  doorSalesEdit?: Maybe<DoorSalesEditPayload>;
  doorSalesRefund?: Maybe<DoorSalesRefundPayload>;
  doorSalesReprint?: Maybe<DoorSalesReprintPayload>;
  doorSalesReserve?: Maybe<DoorSalesReservePayload>;
  doorSalesSendReceipt?: Maybe<DoorSalesSendReceiptPayload>;
  doorSalesUnreserve?: Maybe<DoorSalesUnreservePayload>;
  doorSalesValidateFan?: Maybe<DoorSalesValidateFanPayload>;
  dropHoldPayouts?: Maybe<DropHoldPayoutsPayload>;
  duplicateEvent?: Maybe<DuplicateEventPayload>;
  enableFanSurvey?: Maybe<EnableFanSurveyPayload>;
  endEventPromotion?: Maybe<EndEventPromotionPayload>;
  finishStripeOnboarding?: Maybe<FinishStripeOnboardingPayload>;
  forceMarkbackAllocation?: Maybe<ForceMarkbackAllocationPayload>;
  impersonateUser?: Maybe<ImpersonateUserPayload>;
  importPayouts?: Maybe<ImportPayoutsPayload>;
  inviteGuestList?: Maybe<InviteGuestListPayload>;
  inviteUser?: Maybe<InviteUserPayload>;
  markBackAllocations?: Maybe<MarkBackAllocationsPayload>;
  minorUpdateEvent?: Maybe<MinorUpdateEventPayload>;
  pauseAllocation?: Maybe<PauseAllocationPayload>;
  performPayoutForAccount?: Maybe<PerformPayoutForAccountPayload>;
  publishSeatingChart?: Maybe<PublishSeatingChartPayload>;
  putAllEventsOffSale?: Maybe<PutAllEventsOffSalePayload>;
  refundFromDice?: Maybe<RefundFromDicePayload>;
  refundFromPartner?: Maybe<RefundFromPartnerPayload>;
  /** @deprecated To be removed, use refund_from_partner mutation instead */
  refundTicket?: Maybe<RefundTicketPayload>;
  removeFanConnect?: Maybe<RemoveFanConnectPayload>;
  requestMailchimpCredentials?: Maybe<RequestMailchimpCredentialsPayload>;
  resendFanConnect?: Maybe<ResendFanConnectPayload>;
  resendInviteInstructions?: Maybe<ResendInviteInstructionsPayload>;
  resendTicketsPurchasedNotification?: Maybe<ResendTicketsPurchasedNotificationPayload>;
  resetDoorlistStatus?: Maybe<ResetDoorlistStatusPayload>;
  resumeAllocation?: Maybe<ResumeAllocationPayload>;
  retryFailedEmails?: Maybe<RetryFailedEmailsPayload>;
  revokeUserAccess?: Maybe<RevokeUserAccessPayload>;
  /** save a list of scanned tickets */
  saveScannedTickets?: Maybe<SaveScannedTicketsPayload>;
  /** scan a ticket */
  scanTicket?: Maybe<ScanTicketPayload>;
  sendReportAt?: Maybe<SendReportAtPayload>;
  setEventPromotionCodesEnabledState?: Maybe<SetEventPromotionCodesEnabledStatePayload>;
  setHoldPayouts?: Maybe<SetHoldPayoutsPayload>;
  submitStripeData?: Maybe<SubmitStripeDataPayload>;
  syncEvent?: Maybe<SyncEventPayload>;
  syncInventoryWithShopify?: Maybe<SyncInventoryWithShopifyPayload>;
  testEventChangedNotification?: Maybe<TestEventChangedNotificationPayload>;
  testFanConnect?: Maybe<TestFanConnectPayload>;
  uninviteGuestList?: Maybe<UninviteGuestListPayload>;
  updateAccount?: Maybe<UpdateAccountPayload>;
  updateAccountUser?: Maybe<UpdateAccountUserPayload>;
  /** update an admission config */
  updateAdmissionConfig?: Maybe<UpdateAdmissionConfigPayload>;
  updateArtist?: Maybe<UpdateArtistPayload>;
  updateBundle?: Maybe<UpdateBundlePayload>;
  updateDefaultEventTimings?: Maybe<UpdateDefaultEventTimingsPayload>;
  updateDraftEvent?: Maybe<UpdateDraftEventPayload>;
  updateEvent?: Maybe<UpdateEventPayload>;
  updateEventPromotion?: Maybe<UpdateEventPromotionPayload>;
  updateEventReview?: Maybe<UpdateEventReviewPayload>;
  updateEventSeatingChannel?: Maybe<UpdateEventSeatingChannelPayload>;
  updateEventSeatingChannelObjects?: Maybe<UpdateEventSeatingChannelObjectsPayload>;
  updateEventState?: Maybe<UpdateEventStatePayload>;
  updateFanConnect?: Maybe<UpdateFanConnectPayload>;
  updateFanSurveyQuestion?: Maybe<UpdateFanSurveyQuestionPayload>;
  updateGuestListEntry?: Maybe<UpdateGuestListEntryPayload>;
  updateHierarchicalTag?: Maybe<UpdateHierarchicalTagPayload>;
  updateInventoryProducts?: Maybe<UpdateInventoryProductsPayload>;
  updateLabel?: Maybe<UpdateLabelPayload>;
  updateLinkout?: Maybe<UpdateLinkoutPayload>;
  updateMailchimpSettings?: Maybe<UpdateMailchimpSettingsPayload>;
  updateMarketeer?: Maybe<UpdateMarketeerPayload>;
  updatePassword?: Maybe<UpdatePasswordPayload>;
  updatePermissionProfile?: Maybe<UpdatePermissionProfilePayload>;
  updatePreferredLanguage?: Maybe<UpdatePreferredLanguagePayload>;
  updateProfile?: Maybe<UpdateProfilePayload>;
  updatePromoter?: Maybe<UpdatePromoterPayload>;
  updateReportSchedule?: Maybe<UpdateReportSchedulePayload>;
  updateScheduledReport?: Maybe<UpdateScheduledReportPayload>;
  updateTag?: Maybe<UpdateTagPayload>;
  /**
   * Mutation to update third party APP settings.
   *
   */
  updateThirdPartySettings?: Maybe<UpdateThirdPartySettingsPayload>;
  updateTicketPool?: Maybe<UpdateTicketPoolPayload>;
  updateUser?: Maybe<UpdateUserPayload>;
  updateVenue?: Maybe<UpdateVenuePayload>;
  uploadEventPromotionCodes?: Maybe<UploadEventPromotionCodesPayload>;
  upsertArtistDestinationAccount?: Maybe<UpsertArtistDestinationAccountPayload>;
  upsertInventory?: Maybe<UpsertInventoryPayload>;
  validateDraftEvent?: Maybe<ValidateDraftEventPayload>;
};


export type RootMutationTypeArchiveFanConnectArgs = {
  input: ArchiveFanConnectInput;
};


export type RootMutationTypeAssignProductsToEventsArgs = {
  input: AssignProductsToEventsInput;
};


export type RootMutationTypeBoxOfficePaymentArgs = {
  input: BoxOfficePaymentInput;
};


export type RootMutationTypeBulkUpdateFanSurveyQuestionsArgs = {
  input: BulkUpdateFanSurveyQuestionsInput;
};


export type RootMutationTypeCancelReportArgs = {
  input: CancelReportInput;
};


export type RootMutationTypeChangeAllocationArgs = {
  input: ChangeAllocationInput;
};


export type RootMutationTypeChangeDatesArgs = {
  input: ChangeDatesInput;
};


export type RootMutationTypeCloneEventArgs = {
  input: CloneEventInput;
};


export type RootMutationTypeCloneEventPromotionArgs = {
  input: CloneEventPromotionInput;
};


export type RootMutationTypeCopySeatingChartArgs = {
  input: CopySeatingChartInput;
};


export type RootMutationTypeCreateAdmissionConfigArgs = {
  input: CreateAdmissionConfigInput;
};


export type RootMutationTypeCreateArtistArgs = {
  input: CreateArtistInput;
};


export type RootMutationTypeCreateAttachmentArgs = {
  input: CreateAttachmentInput;
};


export type RootMutationTypeCreateBundleArgs = {
  input: CreateBundleInput;
};


export type RootMutationTypeCreateCommentActivityArgs = {
  input: CreateCommentActivityInput;
};


export type RootMutationTypeCreateDraftEventArgs = {
  input: CreateDraftEventInput;
};


export type RootMutationTypeCreateEventArgs = {
  input: CreateEventInput;
};


export type RootMutationTypeCreateEventChangedNotificationArgs = {
  input: CreateEventChangedNotificationInput;
};


export type RootMutationTypeCreateEventPromotionArgs = {
  input: CreateEventPromotionInput;
};


export type RootMutationTypeCreateEventPromotionCodesArgs = {
  input: CreateEventPromotionCodesInput;
};


export type RootMutationTypeCreateEventSeatingChannelArgs = {
  input: CreateEventSeatingChannelInput;
};


export type RootMutationTypeCreateEventSeatingChartArgs = {
  input: CreateEventSeatingChartInput;
};


export type RootMutationTypeCreateFacebookEventArgs = {
  input: CreateFacebookEventInput;
};


export type RootMutationTypeCreateFanConnectArgs = {
  input: CreateFanConnectInput;
};


export type RootMutationTypeCreateFeeConfigurationArgs = {
  input: CreateFeeConfigurationInput;
};


export type RootMutationTypeCreateHierarchicalTagArgs = {
  input: CreateHierarchicalTagInput;
};


export type RootMutationTypeCreateIntegrationTokenArgs = {
  input: CreateIntegrationTokenInput;
};


export type RootMutationTypeCreateLabelArgs = {
  input: CreateLabelInput;
};


export type RootMutationTypeCreateLinkoutArgs = {
  input: CreateLinkoutInput;
};


export type RootMutationTypeCreateManualPayoutArgs = {
  input: CreateManualPayoutInput;
};


export type RootMutationTypeCreateMarketeerArgs = {
  input: CreateMarketeerInput;
};


export type RootMutationTypeCreateNotificationBatchArgs = {
  input: CreateNotificationBatchInput;
};


export type RootMutationTypeCreatePayoutArgs = {
  input: CreatePayoutInput;
};


export type RootMutationTypeCreatePermissionProfileArgs = {
  input: CreatePermissionProfileInput;
};


export type RootMutationTypeCreateProductArgs = {
  input: CreateProductInput;
};


export type RootMutationTypeCreateProductsListArgs = {
  input: CreateProductsListInput;
};


export type RootMutationTypeCreatePromoterArgs = {
  input: CreatePromoterInput;
};


export type RootMutationTypeCreateReportScheduleArgs = {
  input: CreateReportScheduleInput;
};


export type RootMutationTypeCreateRestrictedIntegrationTokenArgs = {
  input: CreateRestrictedIntegrationTokenInput;
};


export type RootMutationTypeCreateScheduledReportArgs = {
  input: CreateScheduledReportInput;
};


export type RootMutationTypeCreateSeatingChartArgs = {
  input: CreateSeatingChartInput;
};


export type RootMutationTypeCreateSocialLinkArgs = {
  input: CreateSocialLinkInput;
};


export type RootMutationTypeCreateTagArgs = {
  input: CreateTagInput;
};


export type RootMutationTypeCreateTempAttachmentArgs = {
  input: CreateTempAttachmentInput;
};


export type RootMutationTypeCreateThirdPartySettingsArgs = {
  input: CreateThirdPartySettingsInput;
};


export type RootMutationTypeCreateTicketPoolArgs = {
  input: CreateTicketPoolInput;
};


export type RootMutationTypeCreateUserArgs = {
  input: CreateUserInput;
};


export type RootMutationTypeCreateVenueArgs = {
  input: CreateVenueInput;
};


export type RootMutationTypeDeleteAdmissionConfigArgs = {
  input: DeleteAdmissionConfigInput;
};


export type RootMutationTypeDeleteEventPromotionCodesArgs = {
  input: DeleteEventPromotionCodesInput;
};


export type RootMutationTypeDeleteEventSeatingChannelArgs = {
  input: DeleteEventSeatingChannelInput;
};


export type RootMutationTypeDeleteFacebookEventArgs = {
  input: DeleteFacebookEventInput;
};


export type RootMutationTypeDeleteIntegrationTokenArgs = {
  input: DeleteIntegrationTokenInput;
};


export type RootMutationTypeDeleteMailchimpSettingsArgs = {
  input: DeleteMailchimpSettingsInput;
};


export type RootMutationTypeDeleteManualPayoutArgs = {
  input: DeleteManualPayoutInput;
};


export type RootMutationTypeDeleteNodeArgs = {
  input: DeleteNodeInput;
};


export type RootMutationTypeDeleteReportScheduleArgs = {
  input: DeleteReportScheduleInput;
};


export type RootMutationTypeDeleteScheduledReportArgs = {
  input: DeleteScheduledReportInput;
};


export type RootMutationTypeDeleteTicketPoolArgs = {
  input: DeleteTicketPoolInput;
};


export type RootMutationTypeDisableFanSurveyArgs = {
  input: DisableFanSurveyInput;
};


export type RootMutationTypeDisableSeatingChartArgs = {
  input: DisableSeatingChartInput;
};


export type RootMutationTypeDoorSalesChargeArgs = {
  input: DoorSalesChargeInput;
};


export type RootMutationTypeDoorSalesCreateReaderArgs = {
  input: DoorSalesCreateReaderInput;
};


export type RootMutationTypeDoorSalesEditArgs = {
  input: DoorSalesEditInput;
};


export type RootMutationTypeDoorSalesRefundArgs = {
  input: DoorSalesRefundInput;
};


export type RootMutationTypeDoorSalesReprintArgs = {
  input: DoorSalesReprintInput;
};


export type RootMutationTypeDoorSalesReserveArgs = {
  input: DoorSalesReserveInput;
};


export type RootMutationTypeDoorSalesSendReceiptArgs = {
  input: DoorSalesSendReceiptInput;
};


export type RootMutationTypeDoorSalesUnreserveArgs = {
  input: DoorSalesUnreserveInput;
};


export type RootMutationTypeDoorSalesValidateFanArgs = {
  input: DoorSalesValidateFanInput;
};


export type RootMutationTypeDropHoldPayoutsArgs = {
  input: DropHoldPayoutsInput;
};


export type RootMutationTypeDuplicateEventArgs = {
  input: DuplicateEventInput;
};


export type RootMutationTypeEnableFanSurveyArgs = {
  input: EnableFanSurveyInput;
};


export type RootMutationTypeEndEventPromotionArgs = {
  input: EndEventPromotionInput;
};


export type RootMutationTypeFinishStripeOnboardingArgs = {
  input: FinishStripeOnboardingInput;
};


export type RootMutationTypeForceMarkbackAllocationArgs = {
  input: ForceMarkbackAllocationInput;
};


export type RootMutationTypeImpersonateUserArgs = {
  input: ImpersonateUserInput;
};


export type RootMutationTypeImportPayoutsArgs = {
  input: ImportPayoutsInput;
};


export type RootMutationTypeInviteGuestListArgs = {
  input: InviteGuestListInput;
};


export type RootMutationTypeInviteUserArgs = {
  input: InviteUserInput;
};


export type RootMutationTypeMarkBackAllocationsArgs = {
  input: MarkBackAllocationsInput;
};


export type RootMutationTypeMinorUpdateEventArgs = {
  input: MinorUpdateEventInput;
};


export type RootMutationTypePauseAllocationArgs = {
  input: PauseAllocationInput;
};


export type RootMutationTypePerformPayoutForAccountArgs = {
  input: PerformPayoutForAccountInput;
};


export type RootMutationTypePublishSeatingChartArgs = {
  input: PublishSeatingChartInput;
};


export type RootMutationTypePutAllEventsOffSaleArgs = {
  input: PutAllEventsOffSaleInput;
};


export type RootMutationTypeRefundFromDiceArgs = {
  input: RefundFromDiceInput;
};


export type RootMutationTypeRefundFromPartnerArgs = {
  input: RefundFromPartnerInput;
};


export type RootMutationTypeRefundTicketArgs = {
  input: RefundTicketInput;
};


export type RootMutationTypeRemoveFanConnectArgs = {
  input: RemoveFanConnectInput;
};


export type RootMutationTypeRequestMailchimpCredentialsArgs = {
  input: RequestMailchimpCredentialsInput;
};


export type RootMutationTypeResendFanConnectArgs = {
  input: ResendFanConnectInput;
};


export type RootMutationTypeResendInviteInstructionsArgs = {
  input: ResendInviteInstructionsInput;
};


export type RootMutationTypeResendTicketsPurchasedNotificationArgs = {
  input: ResendTicketsPurchasedNotificationInput;
};


export type RootMutationTypeResetDoorlistStatusArgs = {
  input: ResetDoorlistStatusInput;
};


export type RootMutationTypeResumeAllocationArgs = {
  input: ResumeAllocationInput;
};


export type RootMutationTypeRetryFailedEmailsArgs = {
  input: RetryFailedEmailsInput;
};


export type RootMutationTypeRevokeUserAccessArgs = {
  input: RevokeUserAccessInput;
};


export type RootMutationTypeSaveScannedTicketsArgs = {
  input: SaveScannedTicketsInput;
};


export type RootMutationTypeScanTicketArgs = {
  input: ScanTicketInput;
};


export type RootMutationTypeSendReportAtArgs = {
  input: SendReportAtInput;
};


export type RootMutationTypeSetEventPromotionCodesEnabledStateArgs = {
  input: SetEventPromotionCodesEnabledStateInput;
};


export type RootMutationTypeSetHoldPayoutsArgs = {
  input: SetHoldPayoutsInput;
};


export type RootMutationTypeSubmitStripeDataArgs = {
  input: SubmitStripeDataInput;
};


export type RootMutationTypeSyncEventArgs = {
  input: SyncEventInput;
};


export type RootMutationTypeSyncInventoryWithShopifyArgs = {
  input: SyncInventoryWithShopifyInput;
};


export type RootMutationTypeTestEventChangedNotificationArgs = {
  input: TestEventChangedNotificationInput;
};


export type RootMutationTypeTestFanConnectArgs = {
  input: TestFanConnectInput;
};


export type RootMutationTypeUninviteGuestListArgs = {
  input: UninviteGuestListInput;
};


export type RootMutationTypeUpdateAccountArgs = {
  input: UpdateAccountInput;
};


export type RootMutationTypeUpdateAccountUserArgs = {
  input: UpdateAccountUserInput;
};


export type RootMutationTypeUpdateAdmissionConfigArgs = {
  input: UpdateAdmissionConfigInput;
};


export type RootMutationTypeUpdateArtistArgs = {
  input: UpdateArtistInput;
};


export type RootMutationTypeUpdateBundleArgs = {
  input: UpdateBundleInput;
};


export type RootMutationTypeUpdateDefaultEventTimingsArgs = {
  input: UpdateDefaultEventTimingsInput;
};


export type RootMutationTypeUpdateDraftEventArgs = {
  input: UpdateDraftEventInput;
};


export type RootMutationTypeUpdateEventArgs = {
  input: UpdateEventInput;
};


export type RootMutationTypeUpdateEventPromotionArgs = {
  input: UpdateEventPromotionInput;
};


export type RootMutationTypeUpdateEventReviewArgs = {
  input: UpdateEventReviewInput;
};


export type RootMutationTypeUpdateEventSeatingChannelArgs = {
  input: UpdateEventSeatingChannelInput;
};


export type RootMutationTypeUpdateEventSeatingChannelObjectsArgs = {
  input: UpdateEventSeatingChannelObjectsInput;
};


export type RootMutationTypeUpdateEventStateArgs = {
  input: UpdateEventStateInput;
};


export type RootMutationTypeUpdateFanConnectArgs = {
  input: UpdateFanConnectInput;
};


export type RootMutationTypeUpdateFanSurveyQuestionArgs = {
  input: UpdateFanSurveyQuestionInput;
};


export type RootMutationTypeUpdateGuestListEntryArgs = {
  input: UpdateGuestListEntryInput;
};


export type RootMutationTypeUpdateHierarchicalTagArgs = {
  input: UpdateHierarchicalTagInput;
};


export type RootMutationTypeUpdateInventoryProductsArgs = {
  input: UpdateInventoryProductsInput;
};


export type RootMutationTypeUpdateLabelArgs = {
  input: UpdateLabelInput;
};


export type RootMutationTypeUpdateLinkoutArgs = {
  input: UpdateLinkoutInput;
};


export type RootMutationTypeUpdateMailchimpSettingsArgs = {
  input: UpdateMailchimpSettingsInput;
};


export type RootMutationTypeUpdateMarketeerArgs = {
  input: UpdateMarketeerInput;
};


export type RootMutationTypeUpdatePasswordArgs = {
  input: UpdatePasswordInput;
};


export type RootMutationTypeUpdatePermissionProfileArgs = {
  input: UpdatePermissionProfileInput;
};


export type RootMutationTypeUpdatePreferredLanguageArgs = {
  input: UpdatePreferredLanguageInput;
};


export type RootMutationTypeUpdateProfileArgs = {
  input: UpdateProfileInput;
};


export type RootMutationTypeUpdatePromoterArgs = {
  input: UpdatePromoterInput;
};


export type RootMutationTypeUpdateReportScheduleArgs = {
  input: UpdateReportScheduleInput;
};


export type RootMutationTypeUpdateScheduledReportArgs = {
  input: UpdateScheduledReportInput;
};


export type RootMutationTypeUpdateTagArgs = {
  input: UpdateTagInput;
};


export type RootMutationTypeUpdateThirdPartySettingsArgs = {
  input: UpdateThirdPartySettingsInput;
};


export type RootMutationTypeUpdateTicketPoolArgs = {
  input: UpdateTicketPoolInput;
};


export type RootMutationTypeUpdateUserArgs = {
  input: UpdateUserInput;
};


export type RootMutationTypeUpdateVenueArgs = {
  input: UpdateVenueInput;
};


export type RootMutationTypeUploadEventPromotionCodesArgs = {
  input: UploadEventPromotionCodesInput;
};


export type RootMutationTypeUpsertArtistDestinationAccountArgs = {
  input: UpsertArtistDestinationAccountInput;
};


export type RootMutationTypeUpsertInventoryArgs = {
  input: UpsertInventoryInput;
};


export type RootMutationTypeValidateDraftEventArgs = {
  input: ValidateDraftEventInput;
};

export type RootQueryType = {
  __typename?: 'RootQueryType';
  appleMusic?: Maybe<AppleMusicViewer>;
  /** Given a list of emails, returns true if any are not part of the account. */
  externalEmails?: Maybe<Scalars['Boolean']['output']>;
  featureFlags?: Maybe<FeatureFlags>;
  node?: Maybe<Node>;
  relatedEvents?: Maybe<RelatedEventViewer>;
  spotify?: Maybe<SpotifyViewer>;
  viewer?: Maybe<Viewer>;
};


export type RootQueryTypeExternalEmailsArgs = {
  emails: Array<Scalars['EmailAddress']['input']>;
};


export type RootQueryTypeNodeArgs = {
  id: Scalars['ID']['input'];
};

export type Sales = {
  __typename?: 'Sales';
  ticketTypesBreakdown: Array<Maybe<TicketTypeBreakdown>>;
  totalAppSold: Scalars['Int']['output'];
  totalDigitalValue: Scalars['Int']['output'];
  totalFaceValue: Scalars['Int']['output'];
  totalPayoutValue: Scalars['Int']['output'];
  totalPosSold: Scalars['Int']['output'];
  totalPosValue: Scalars['Int']['output'];
  totalPromoterIncome: Scalars['Int']['output'];
  totalRebate: Scalars['Int']['output'];
  totalRefunded: Scalars['Int']['output'];
  totalRefundedValue: Scalars['Int']['output'];
  totalReserved: Scalars['Int']['output'];
  totalRevoked: Scalars['Int']['output'];
  totalSold: Scalars['Int']['output'];
  totalTerminalSold: Scalars['Int']['output'];
  totalTerminalValue: Scalars['Int']['output'];
  /** @deprecated Use total_face_value */
  totalValue: Scalars['Int']['output'];
  totalWlIndividuals: Scalars['Int']['output'];
  totalWlRequests: Scalars['Int']['output'];
};


export type SalesTotalFaceValueArgs = {
  useCache?: InputMaybe<Scalars['Boolean']['input']>;
};


export type SalesTotalPayoutValueArgs = {
  useCache?: InputMaybe<Scalars['Boolean']['input']>;
};


export type SalesTotalPromoterIncomeArgs = {
  useCache?: InputMaybe<Scalars['Boolean']['input']>;
};


export type SalesTotalRebateArgs = {
  useCache?: InputMaybe<Scalars['Boolean']['input']>;
};


export type SalesTotalSoldArgs = {
  useCache?: InputMaybe<Scalars['Boolean']['input']>;
};


export type SalesTotalValueArgs = {
  useCache?: InputMaybe<Scalars['Boolean']['input']>;
};

export type SalesFees = {
  __typename?: 'SalesFees';
  computed: Scalars['Int']['output'];
  keep: Scalars['Int']['output'];
  rebate: Scalars['Int']['output'];
  type: FeeType;
};

export enum SalesReportGrouping {
  EVENT = 'EVENT',
  TICKET_TYPE = 'TICKET_TYPE',
  TRANSACTION = 'TRANSACTION'
}

export type SalesforceContract = Node & {
  __typename?: 'SalesforceContract';
  endDate?: Maybe<Scalars['Date']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  num?: Maybe<Scalars['String']['output']>;
  opportunityDealType?: Maybe<Scalars['String']['output']>;
  opportunityId?: Maybe<Scalars['String']['output']>;
  opportunityName?: Maybe<Scalars['String']['output']>;
  opportunityType?: Maybe<Scalars['String']['output']>;
  sfAccountId?: Maybe<Scalars['String']['output']>;
  sfId?: Maybe<Scalars['String']['output']>;
  startDate?: Maybe<Scalars['Date']['output']>;
  status?: Maybe<SalesforceContractStatus>;
};

export enum SalesforceContractStatus {
  ACTIVATED = 'ACTIVATED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  SIGNED = 'SIGNED',
  SUPERSEDED = 'SUPERSEDED'
}

export type SalesforcePromoterFields = {
  __typename?: 'SalesforcePromoterFields';
  defaultContract?: Maybe<SalesforceContract>;
  ownerEmail?: Maybe<Scalars['String']['output']>;
  parentSalesforceId?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
};

export type SalesforcePromoterFieldsInput = {
  defaultContractId?: InputMaybe<Scalars['ID']['input']>;
};

export type SaveScannedTicketsInput = {
  admissionLogs: Array<InputMaybe<AdmissionLogInput>>;
  clientMutationId: Scalars['String']['input'];
};

export type SaveScannedTicketsPayload = {
  __typename?: 'SaveScannedTicketsPayload';
  clientMutationId: Scalars['String']['output'];
  isValid?: Maybe<Scalars['Boolean']['output']>;
};

export type ScanTicketInput = {
  clientMutationId: Scalars['String']['input'];
  code: Scalars['String']['input'];
  configIds: Array<InputMaybe<Scalars['ID']['input']>>;
  eventIds: Array<Scalars['ID']['input']>;
  scanningUser: Scalars['String']['input'];
};

export type ScanTicketPayload = {
  __typename?: 'ScanTicketPayload';
  /** the config the successfully scanned the ticket. Return null for the default config */
  admissionConfig?: Maybe<AdmissionConfig>;
  clientMutationId: Scalars['String']['output'];
};

export enum ScheduleStatus {
  POSTPONED = 'POSTPONED',
  RESCHEDULED = 'RESCHEDULED'
}

export type ScheduledReport = Node & {
  __typename?: 'ScheduledReport';
  archived: Scalars['Boolean']['output'];
  downloadUrl?: Maybe<Scalars['String']['output']>;
  emailList?: Maybe<Array<Maybe<Scalars['EmailAddress']['output']>>>;
  emails?: Maybe<Array<Maybe<Email>>>;
  event?: Maybe<Event>;
  hasFailedEmails: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  insertedAt?: Maybe<Scalars['Time']['output']>;
  lastUpdatedBy?: Maybe<User>;
  locale?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  options?: Maybe<ScheduledReportOptions>;
  promoter?: Maybe<Promoter>;
  reportSchedule?: Maybe<ReportSchedule>;
  reportType: ReportType;
  scheduledAt: Scalars['Time']['output'];
  status: ScheduledReportStatus;
  updatedAt?: Maybe<Scalars['Time']['output']>;
  user?: Maybe<User>;
  uuid: Scalars['String']['output'];
};

export type ScheduledReportOptions = {
  __typename?: 'ScheduledReportOptions';
  accountId?: Maybe<Scalars['ID']['output']>;
  accountIds?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
  countryCode?: Maybe<Scalars['String']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  eventIds?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
  fanCountries?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  fields?: Maybe<Scalars['Int']['output']>;
  hideTaxId?: Maybe<Scalars['Boolean']['output']>;
  legalEntity?: Maybe<Scalars['String']['output']>;
  marketeerId?: Maybe<Scalars['ID']['output']>;
  showPayoutSummary?: Maybe<Scalars['Boolean']['output']>;
  showProducts?: Maybe<Scalars['Boolean']['output']>;
  showTickets?: Maybe<Scalars['Boolean']['output']>;
  startDate?: Maybe<Scalars['Time']['output']>;
};

export type ScheduledReportOptionsInput = {
  /** The name of the object type currently being queried. */
  accountId?: InputMaybe<Scalars['ID']['input']>;
  accountIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  fanCountries?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  fields?: InputMaybe<Scalars['Int']['input']>;
  hideTaxId?: InputMaybe<Scalars['Boolean']['input']>;
  legalEntity?: InputMaybe<Scalars['String']['input']>;
  marketeerId?: InputMaybe<Scalars['ID']['input']>;
  showPayoutSummary?: InputMaybe<Scalars['Boolean']['input']>;
  showProducts?: InputMaybe<Scalars['Boolean']['input']>;
  showTickets?: InputMaybe<Scalars['Boolean']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
};

export enum ScheduledReportStatus {
  CANCELLED = 'CANCELLED',
  DONE = 'DONE',
  FAILED = 'FAILED',
  SCHEDULED = 'SCHEDULED'
}

export type ScheduledReportsConnection = {
  __typename?: 'ScheduledReportsConnection';
  edges?: Maybe<Array<Maybe<ScheduledReportsEdge>>>;
  pageInfo: PageInfo;
};

export type ScheduledReportsEdge = {
  __typename?: 'ScheduledReportsEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<ScheduledReport>;
};

export type SeatCategoriesInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  seatsIoKey?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['Int']['input']>;
};

export type SeatCategory = {
  __typename?: 'SeatCategory';
  id?: Maybe<Scalars['ID']['output']>;
  name: Scalars['String']['output'];
  seatsIoKey?: Maybe<Scalars['String']['output']>;
  ticketTypeId?: Maybe<Scalars['Int']['output']>;
  value: Scalars['Int']['output'];
};

export type SeatLabel = {
  __typename?: 'SeatLabel';
  entrance?: Maybe<Scalars['String']['output']>;
  own?: Maybe<SeatLabelType>;
  parent?: Maybe<SeatLabelType>;
  section?: Maybe<Scalars['String']['output']>;
};

export type SeatLabelType = {
  __typename?: 'SeatLabelType';
  label?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type SeatingAreaConfig = {
  __typename?: 'SeatingAreaConfig';
  capacity: Scalars['Int']['output'];
  seatingArea: Scalars['String']['output'];
};

export type SeatingAreaConfigInput = {
  capacity: Scalars['Int']['input'];
  seatingArea: Scalars['String']['input'];
};

export type SeatingChannel = {
  __typename?: 'SeatingChannel';
  channelType: ChannelType;
  name?: Maybe<Scalars['String']['output']>;
  seatsIoChannel: Scalars['String']['output'];
};

export type SeatingChannelInput = {
  channelType: ChannelType;
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  seatsIoChannel: Scalars['String']['input'];
};

export type SeatingChart = Node & {
  __typename?: 'SeatingChart';
  chartManagerCredentials: ChartManagerCredentials;
  disabled: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  inUse: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  published: Scalars['Boolean']['output'];
  seatsIoChart?: Maybe<SeatsIoChart>;
};

export type SeatingChartConnection = {
  __typename?: 'SeatingChartConnection';
  edges?: Maybe<Array<Maybe<SeatingChartEdge>>>;
  pageInfo: PageInfo;
};

export type SeatingChartEdge = {
  __typename?: 'SeatingChartEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<SeatingChart>;
};

export type SeatsIoChannel = {
  __typename?: 'SeatsIoChannel';
  color: Scalars['String']['output'];
  key: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type SeatsIoChannelWithObjects = {
  __typename?: 'SeatsIoChannelWithObjects';
  color: Scalars['String']['output'];
  key: Scalars['String']['output'];
  name: Scalars['String']['output'];
  objects: Array<Maybe<Scalars['String']['output']>>;
};

export type SeatsIoChart = {
  __typename?: 'SeatsIoChart';
  key?: Maybe<Scalars['String']['output']>;
  socialDistancingRulesets?: Maybe<Array<Maybe<SeatsIoSocialDistancingRuleset>>>;
};

export enum SeatsIoChartType {
  MIXED = 'MIXED',
  ROWS_WITH_SECTIONS = 'ROWS_WITH_SECTIONS'
}

export type SeatsIoEvent = {
  __typename?: 'SeatsIoEvent';
  channels?: Maybe<Array<Maybe<SeatsIoChannelWithObjects>>>;
  chartKey?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  key?: Maybe<Scalars['String']['output']>;
  socialDistancingRulesetKey?: Maybe<Scalars['String']['output']>;
};

export enum SeatsIoEventReportBy {
  BY_CATEGORY_KEY = 'BY_CATEGORY_KEY',
  BY_CATEGORY_LABEL = 'BY_CATEGORY_LABEL',
  BY_CHANNEL = 'BY_CHANNEL',
  BY_LABEL = 'BY_LABEL',
  BY_OBJECT_TYPE = 'BY_OBJECT_TYPE',
  BY_ORDER_ID = 'BY_ORDER_ID',
  BY_SECTION = 'BY_SECTION',
  BY_SELECTABILITY = 'BY_SELECTABILITY',
  BY_STATUS = 'BY_STATUS'
}

export type SeatsIoSocialDistancingRuleset = {
  __typename?: 'SeatsIoSocialDistancingRuleset';
  key?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type SellingPoint = {
  __typename?: 'SellingPoint';
  name: Scalars['String']['output'];
  order: Scalars['Int']['output'];
  product: Product;
};

export type SellingPointInput = {
  name: Scalars['String']['input'];
};

export type SendReportAtInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  scheduledAt: Scalars['Time']['input'];
};

export type SendReportAtPayload = {
  __typename?: 'SendReportAtPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ScheduledReport>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type SetEventPromotionCodesEnabledStateInput = {
  allCodes?: InputMaybe<Scalars['Boolean']['input']>;
  clientMutationId: Scalars['String']['input'];
  codes?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  enabledState: Scalars['Boolean']['input'];
  filter?: InputMaybe<CodeLockFilter>;
  id: Scalars['ID']['input'];
};

export type SetEventPromotionCodesEnabledStatePayload = {
  __typename?: 'SetEventPromotionCodesEnabledStatePayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventPromotion>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type SetHoldPayoutsInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type SetHoldPayoutsPayload = {
  __typename?: 'SetHoldPayoutsPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type ShopifyStore = Node & {
  __typename?: 'ShopifyStore';
  adminApiTokenIssued: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  shopifyOauthLink?: Maybe<Scalars['String']['output']>;
  storeName?: Maybe<Scalars['String']['output']>;
};

export type ShopifyStoreInput = {
  storeName?: InputMaybe<Scalars['String']['input']>;
};

export enum ShowArtistDescription {
  CUSTOM = 'CUSTOM',
  DICE = 'DICE',
  NONE = 'NONE'
}

export type SocialLink = {
  __typename?: 'SocialLink';
  artist?: Maybe<Artist>;
  campaign: Scalars['String']['output'];
  channel: Scalars['String']['output'];
  customUrl?: Maybe<Scalars['String']['output']>;
  deelsParams?: Maybe<Scalars['String']['output']>;
  diceLink?: Maybe<Scalars['Boolean']['output']>;
  event?: Maybe<Event>;
  id?: Maybe<Scalars['ID']['output']>;
  insertedAt: Scalars['Time']['output'];
  postType: SocialLinkPostType;
  promoter?: Maybe<Promoter>;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  /** e.g. https://link.dice.fm/AE6xPxPd7jb */
  url?: Maybe<Scalars['String']['output']>;
  venue?: Maybe<Venue>;
};

export enum SocialLinkPostType {
  organic = 'organic',
  paid = 'paid',
  unknown = 'unknown'
}

export type SpotifyArtistObject = {
  __typename?: 'SpotifyArtistObject';
  externalUrls?: Maybe<SpotifyExternalUrlObject>;
  href: Scalars['String']['output'];
  id: Scalars['String']['output'];
  images?: Maybe<Array<Maybe<SpotifyImagesObject>>>;
  name: Scalars['String']['output'];
  uri: Scalars['String']['output'];
};

export type SpotifyArtistsResponse = {
  __typename?: 'SpotifyArtistsResponse';
  href: Scalars['String']['output'];
  items: Array<Maybe<SpotifyArtistObject>>;
};

export type SpotifyExternalUrlObject = {
  __typename?: 'SpotifyExternalUrlObject';
  spotify: Scalars['String']['output'];
};

export type SpotifyImagesObject = {
  __typename?: 'SpotifyImagesObject';
  height?: Maybe<Scalars['Int']['output']>;
  url: Scalars['String']['output'];
  width?: Maybe<Scalars['Int']['output']>;
};

export type SpotifySearchResponse = {
  __typename?: 'SpotifySearchResponse';
  artists?: Maybe<SpotifyArtistsResponse>;
  tracks?: Maybe<SpotifyTracksResponse>;
};

export enum SpotifySearchTypes {
  album = 'album',
  artist = 'artist',
  playlist = 'playlist',
  track = 'track'
}

export type SpotifyTrackObject = {
  __typename?: 'SpotifyTrackObject';
  artists?: Maybe<Array<Maybe<SpotifyArtistObject>>>;
  externalUrls?: Maybe<SpotifyExternalUrlObject>;
  href: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  previewUrl?: Maybe<Scalars['String']['output']>;
  uri: Scalars['String']['output'];
};

export type SpotifyTracksResponse = {
  __typename?: 'SpotifyTracksResponse';
  href: Scalars['String']['output'];
  items: Array<Maybe<SpotifyTrackObject>>;
};

export type SpotifyViewer = {
  __typename?: 'SpotifyViewer';
  search?: Maybe<SpotifySearchResponse>;
};


export type SpotifyViewerSearchArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  q: Scalars['String']['input'];
  type?: InputMaybe<Array<InputMaybe<SpotifySearchTypes>>>;
};

export type StreamDetails = {
  __typename?: 'StreamDetails';
  castServerUrl?: Maybe<Scalars['String']['output']>;
  castStreamKey?: Maybe<Scalars['String']['output']>;
  muxStreamId?: Maybe<Scalars['String']['output']>;
  playbackUrl?: Maybe<Scalars['String']['output']>;
  streamOnline?: Maybe<Scalars['Boolean']['output']>;
};

export type StreamStats = {
  __typename?: 'StreamStats';
  avgWatchTime?: Maybe<Scalars['Int']['output']>;
  completionRatio?: Maybe<Scalars['Float']['output']>;
  exitsBeforeVideoStart?: Maybe<Scalars['Float']['output']>;
  peakTime?: Maybe<Scalars['Time']['output']>;
  peakViewers?: Maybe<Scalars['Int']['output']>;
  streamEnd?: Maybe<Scalars['Time']['output']>;
  streamStart?: Maybe<Scalars['Time']['output']>;
  totalViewers?: Maybe<Scalars['Int']['output']>;
  videoStartupTime?: Maybe<Scalars['Float']['output']>;
  viewersPerDeviceType?: Maybe<Array<Maybe<StreamViewersBreakdownItem>>>;
  viewersPerOs?: Maybe<Array<Maybe<StreamViewersBreakdownItem>>>;
  viewersTime?: Maybe<Array<Maybe<StreamViewersTimeItem>>>;
};

export enum StreamType {
  LIVE = 'LIVE',
  REWATCH = 'REWATCH'
}

export type StreamViewersBreakdownItem = {
  __typename?: 'StreamViewersBreakdownItem';
  avgWatchTime?: Maybe<Scalars['Int']['output']>;
  label?: Maybe<Scalars['String']['output']>;
  viewers?: Maybe<Scalars['Int']['output']>;
};

export type StreamViewersTimeItem = {
  __typename?: 'StreamViewersTimeItem';
  time?: Maybe<Scalars['Time']['output']>;
  viewers?: Maybe<Scalars['Int']['output']>;
};

export enum StreamingUrlState {
  STREAM_EMBED_CODE_BLANK = 'STREAM_EMBED_CODE_BLANK',
  STREAM_URL_BLANK = 'STREAM_URL_BLANK',
  STREAM_URL_READY = 'STREAM_URL_READY'
}

export enum StripeAccountIntegrityState {
  CHARGES_DISABLED = 'CHARGES_DISABLED',
  OK = 'OK',
  PAYOUTS_DISABLED = 'PAYOUTS_DISABLED'
}

export type StripeAccrual = {
  __typename?: 'StripeAccrual';
  balanceBatchId?: Maybe<Scalars['Int']['output']>;
  balanceChange: Scalars['Int']['output'];
  currency: Scalars['String']['output'];
  destinationAccount: Scalars['String']['output'];
  region: Scalars['String']['output'];
  scheduledAt?: Maybe<Scalars['Time']['output']>;
};

export type StripeOperations = {
  __typename?: 'StripeOperations';
  balanceBatches: Array<Maybe<BalanceBatch>>;
  finTrans: Array<Maybe<FinTran>>;
};

export type StripePaymentIntent = {
  __typename?: 'StripePaymentIntent';
  amount: Scalars['Int']['output'];
  currency: Scalars['String']['output'];
  region: Scalars['String']['output'];
  sid?: Maybe<Scalars['String']['output']>;
};

export type StripeReader = {
  __typename?: 'StripeReader';
  deviceType?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  label?: Maybe<Scalars['String']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  serialNumber?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type StripeRefund = {
  __typename?: 'StripeRefund';
  amount: Scalars['Int']['output'];
  failureReason?: Maybe<Scalars['String']['output']>;
  sid?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export enum StripeRestrictionStatus {
  /** This value only appears in the context of a filter */
  ANY_PROBLEM = 'ANY_PROBLEM',
  CHARGES_ONLY = 'CHARGES_ONLY',
  FULLY_RESTRICTED = 'FULLY_RESTRICTED',
  /** This value only appears in the context of a filter */
  LONG_UNVERIFIED = 'LONG_UNVERIFIED',
  OK = 'OK',
  PAYOUTS_ONLY = 'PAYOUTS_ONLY',
  SOON_RESTRICTED = 'SOON_RESTRICTED'
}

export type StripeTransfer = {
  __typename?: 'StripeTransfer';
  executedAt?: Maybe<Scalars['Time']['output']>;
  lastError?: Maybe<Scalars['Map']['output']>;
  sid?: Maybe<Scalars['String']['output']>;
};

export type StripeTransferReversal = {
  __typename?: 'StripeTransferReversal';
  amount: Scalars['Int']['output'];
  executedAt?: Maybe<Scalars['Time']['output']>;
  lastError?: Maybe<Scalars['Map']['output']>;
  sid?: Maybe<Scalars['String']['output']>;
  stripeTransfer?: Maybe<StripeTransfer>;
};

export type Subject = {
  __typename?: 'Subject';
  actions?: Maybe<Array<Maybe<Action>>>;
  name?: Maybe<Scalars['String']['output']>;
};

export type SubjectInput = {
  actions?: InputMaybe<Array<InputMaybe<ActionInput>>>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type SubmitStripeDataInput = {
  accountCountry?: InputMaybe<Scalars['String']['input']>;
  accountCurrency?: InputMaybe<EventCostCurrency>;
  accountNumber?: InputMaybe<Scalars['String']['input']>;
  accountRouting?: InputMaybe<Scalars['String']['input']>;
  additionalCurrencies?: InputMaybe<Array<InputMaybe<AdditionalCurrencyInput>>>;
  address?: InputMaybe<Scalars['String']['input']>;
  businessName?: InputMaybe<Scalars['String']['input']>;
  businessTaxId?: InputMaybe<Scalars['String']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  dob?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  legalEntityType?: InputMaybe<LegalEntityType>;
  passportFileBlob?: InputMaybe<Scalars['String']['input']>;
  personalAddressAddress?: InputMaybe<Scalars['String']['input']>;
  personalAddressCity?: InputMaybe<Scalars['String']['input']>;
  personalAddressPostalCode?: InputMaybe<Scalars['String']['input']>;
  personalState?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  promoterId: Scalars['ID']['input'];
  ssnLast4?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
};

export type SubmitStripeDataPayload = {
  __typename?: 'SubmitStripeDataPayload';
  clientMutationId: Scalars['String']['output'];
  promoter?: Maybe<Promoter>;
};

export type SyncEventInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};

export type SyncEventPayload = {
  __typename?: 'SyncEventPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type SyncInventoryWithShopifyInput = {
  clientMutationId: Scalars['String']['input'];
  inventoryId?: InputMaybe<Scalars['ID']['input']>;
};

export type SyncInventoryWithShopifyPayload = {
  __typename?: 'SyncInventoryWithShopifyPayload';
  clientMutationId: Scalars['String']['output'];
  inventory: Inventory;
};

export type Tag = Name & Node & {
  __typename?: 'Tag';
  createdAt: Scalars['Time']['output'];
  deprecated: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['Time']['output'];
};

export enum TagOrder {
  NAME = 'NAME'
}

export type TagWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<TagWhereInput>>>;
  deprecated?: InputMaybe<OperatorsBooleanEqInput>;
  name?: InputMaybe<OperatorsString>;
};

export type TagsConnection = {
  __typename?: 'TagsConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<TagsEdge>>>;
  pageInfo: PageInfo;
};

export type TagsEdge = {
  __typename?: 'TagsEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Tag>;
};

export enum TargetingMode {
  country = 'country',
  location = 'location',
  worldwide = 'worldwide'
}

export type TaxRate = {
  __typename?: 'TaxRate';
  faceValueRate?: Maybe<Scalars['Float']['output']>;
  feesRates?: Maybe<FeesRates>;
};

export type TaxSettings = {
  __typename?: 'TaxSettings';
  clubNight?: Maybe<Scalars['Boolean']['output']>;
  franceMainstream?: Maybe<Scalars['Boolean']['output']>;
};

export type TaxSettingsInput = {
  clubNight?: InputMaybe<Scalars['Boolean']['input']>;
  franceMainstream?: InputMaybe<Scalars['Boolean']['input']>;
};

export type TempAttachment = {
  __typename?: 'TempAttachment';
  storagePath: Scalars['String']['output'];
};

export type TestEventChangedNotificationInput = {
  changes?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  changeset: EventChangedNotificationChangeset;
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  message?: InputMaybe<Scalars['String']['input']>;
  sendMeACopy?: InputMaybe<Scalars['Boolean']['input']>;
};

export type TestEventChangedNotificationPayload = {
  __typename?: 'TestEventChangedNotificationPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type TestFanConnectInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  message: Scalars['String']['input'];
  ticketTypeIds: Array<InputMaybe<Scalars['ID']['input']>>;
  title: Scalars['String']['input'];
};

export type TestFanConnectPayload = {
  __typename?: 'TestFanConnectPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type ThinUserProfile = {
  __typename?: 'ThinUserProfile';
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
};

export type ThirdPartySettings = Node & {
  __typename?: 'ThirdPartySettings';
  appIcon: Scalars['String']['output'];
  appLink: Scalars['String']['output'];
  appName: Scalars['String']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  idVerification: Scalars['Boolean']['output'];
  promoterDisplayName?: Maybe<Scalars['String']['output']>;
  promoterId?: Maybe<Scalars['ID']['output']>;
  provideSecureUserAuth: Scalars['Boolean']['output'];
};

export type TicketHolder = {
  __typename?: 'TicketHolder';
  holderFirstName?: Maybe<Scalars['String']['output']>;
  holderLastName?: Maybe<Scalars['String']['output']>;
};

export type TicketPool = Node & {
  __typename?: 'TicketPool';
  archived?: Maybe<Scalars['Boolean']['output']>;
  holds?: Maybe<Array<Maybe<Hold>>>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  maxAllocation?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  unlimitedAllocation?: Maybe<Scalars['Boolean']['output']>;
};

export type TicketType = FaceValueInterface & Fees & Node & PriceBreakdownInterface & {
  __typename?: 'TicketType';
  activateCodeDateOffset?: Maybe<Scalars['Int']['output']>;
  additionalPaymentMethods?: Maybe<Array<Maybe<PaymentMethods>>>;
  allocation: Scalars['Int']['output'];
  allowSeatChange?: Maybe<Scalars['Boolean']['output']>;
  announceDate?: Maybe<Scalars['Time']['output']>;
  archived?: Maybe<Scalars['Boolean']['output']>;
  area?: Maybe<VenueArea>;
  attractivePriceType?: Maybe<Scalars['String']['output']>;
  attractiveSeatingAreaType?: Maybe<Scalars['String']['output']>;
  /** @deprecated Legacy */
  attractiveTaxFree?: Maybe<Scalars['Boolean']['output']>;
  codeLocked: Scalars['Boolean']['output'];
  currentPriceTier?: Maybe<PriceTier>;
  description?: Maybe<Scalars['String']['output']>;
  doorSalesEnabled: Scalars['Boolean']['output'];
  doorSalesPrice?: Maybe<Scalars['Int']['output']>;
  doorSalesPriceCurrent?: Maybe<Scalars['Int']['output']>;
  doorSalesPriceTaxed?: Maybe<Scalars['Int']['output']>;
  doorSalesStats?: Maybe<DoorSalesStats>;
  doorSalesTax?: Maybe<Scalars['Int']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  eventId?: Maybe<Scalars['Int']['output']>;
  eventPromotions?: Maybe<Array<Maybe<EventPromotion>>>;
  externalSkus?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  faceValue: Scalars['Int']['output'];
  fees?: Maybe<Array<Maybe<Fee>>>;
  hidden: Scalars['Boolean']['output'];
  icon?: Maybe<Scalars['String']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  increment?: Maybe<Scalars['Int']['output']>;
  isStream: Scalars['Boolean']['output'];
  maximumIncrements?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  offSaleDate?: Maybe<Scalars['Time']['output']>;
  onSaleDate?: Maybe<Scalars['Time']['output']>;
  onSaleNotificationSentAt?: Maybe<Scalars['Time']['output']>;
  onSaleNotificationStatus: Scalars['Boolean']['output'];
  order: Scalars['Int']['output'];
  presale: Scalars['Boolean']['output'];
  price?: Maybe<Scalars['Int']['output']>;
  priceBreakdown?: Maybe<Price>;
  priceGrade: Scalars['String']['output'];
  priceHidden: Scalars['Boolean']['output'];
  priceTierType?: Maybe<PriceTierTypes>;
  priceTiers?: Maybe<Array<Maybe<PriceTier>>>;
  products?: Maybe<Array<Maybe<Product>>>;
  rebate?: Maybe<Scalars['Int']['output']>;
  requiresAddress?: Maybe<Scalars['Boolean']['output']>;
  requiresOtherTypeIds?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
  reservedForGuestList: Scalars['Boolean']['output'];
  reservedSeating?: Maybe<Scalars['Boolean']['output']>;
  reservedSeatingType?: Maybe<ReservedSeatingTypes>;
  salesLimit?: Maybe<Scalars['Int']['output']>;
  seatCategories?: Maybe<Array<Maybe<SeatCategory>>>;
  seatmapUrl?: Maybe<Scalars['String']['output']>;
  startDate?: Maybe<Scalars['Time']['output']>;
  streamEmbedCode?: Maybe<Scalars['String']['output']>;
  streamLink?: Maybe<Scalars['String']['output']>;
  ticketPoolId?: Maybe<Scalars['String']['output']>;
  venueScheduleId?: Maybe<Scalars['ID']['output']>;
};

export type TicketTypeBreakdown = {
  __typename?: 'TicketTypeBreakdown';
  fees: Array<Maybe<SalesFees>>;
  priceTiersBreakdown: Array<Maybe<PriceTierBreakdown>>;
  promotionsBreakdown: Array<Maybe<PromotionBreakdown>>;
  ticketHoldersCount: Scalars['Int']['output'];
  ticketType: TicketType;
  ticketTypeId: Scalars['ID']['output'];
  totalAppSold: Scalars['Int']['output'];
  totalDigitalValue: Scalars['Int']['output'];
  totalFaceValue: Scalars['Int']['output'];
  totalPayoutValue: Scalars['Int']['output'];
  totalPosSold: Scalars['Int']['output'];
  totalPosValue: Scalars['Int']['output'];
  totalRebate: Scalars['Int']['output'];
  totalRefundRequested: Scalars['Int']['output'];
  totalRefundTicketsRequested: Scalars['Int']['output'];
  totalRefundValueRequested: Scalars['Int']['output'];
  totalReserved: Scalars['Int']['output'];
  totalSold: Scalars['Int']['output'];
  totalTerminalSold: Scalars['Int']['output'];
  totalTerminalValue: Scalars['Int']['output'];
  totalWlPurchases: Scalars['Int']['output'];
  totalWlRequests: Scalars['Int']['output'];
};

export type TicketTypesInput = {
  activateCodeDateOffset?: InputMaybe<Scalars['Int']['input']>;
  additionalPaymentMethods?: InputMaybe<Array<InputMaybe<PaymentMethods>>>;
  allocation?: InputMaybe<Scalars['Int']['input']>;
  allowSeatChange?: InputMaybe<Scalars['Boolean']['input']>;
  announceDate?: InputMaybe<Scalars['Time']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  area?: InputMaybe<CreateOrUpdateVenueAreaInput>;
  attractivePriceType?: InputMaybe<Scalars['String']['input']>;
  attractiveSeatingAreaType?: InputMaybe<Scalars['String']['input']>;
  attractiveTaxFree?: InputMaybe<Scalars['Boolean']['input']>;
  codeLocked?: InputMaybe<Scalars['Boolean']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  doorSalesEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  doorSalesPrice?: InputMaybe<Scalars['Int']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  externalSkus?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  faceValue?: InputMaybe<Scalars['Int']['input']>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  hidden?: InputMaybe<Scalars['Boolean']['input']>;
  icon?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  increment?: InputMaybe<Scalars['Int']['input']>;
  isStream?: InputMaybe<Scalars['Boolean']['input']>;
  maximumIncrements?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  presale?: InputMaybe<Scalars['Boolean']['input']>;
  priceGrade?: InputMaybe<Scalars['String']['input']>;
  priceHidden?: InputMaybe<Scalars['Boolean']['input']>;
  priceTierType?: InputMaybe<PriceTierTypes>;
  priceTiers?: InputMaybe<Array<InputMaybe<PriceTiersInput>>>;
  productIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  requiresAddress?: InputMaybe<Scalars['Boolean']['input']>;
  requiresOtherTypeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  reservedForGuestList?: InputMaybe<Scalars['Boolean']['input']>;
  reservedSeating?: InputMaybe<Scalars['Boolean']['input']>;
  reservedSeatingType?: InputMaybe<ReservedSeatingTypes>;
  salesLimit?: InputMaybe<Scalars['Int']['input']>;
  seatCategories?: InputMaybe<Array<InputMaybe<SeatCategoriesInput>>>;
  seatmapUrl?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
  streamEmbedCode?: InputMaybe<Scalars['String']['input']>;
  streamLink?: InputMaybe<Scalars['String']['input']>;
  ticketPoolId?: InputMaybe<Scalars['String']['input']>;
  venueScheduleId?: InputMaybe<Scalars['ID']['input']>;
  venueScheduleIndex?: InputMaybe<Scalars['Int']['input']>;
};

export type TimeStamps = {
  createdAt?: Maybe<Scalars['Time']['output']>;
  updatedAt?: Maybe<Scalars['Time']['output']>;
};

/** timeframe options for admission reports */
export enum Timeframe {
  FIFTEEN_MINUTES = 'FIFTEEN_MINUTES',
  FIVE_MINUTES = 'FIVE_MINUTES',
  MINUTE = 'MINUTE',
  THIRTY_MINUTES = 'THIRTY_MINUTES'
}

export type TokensList = {
  __typename?: 'TokensList';
  allCustomerDataToken?: Maybe<Scalars['String']['output']>;
  c1FormExportToken?: Maybe<Scalars['String']['output']>;
  c2FormExportToken?: Maybe<Scalars['String']['output']>;
  codeLocksExportToken?: Maybe<Scalars['String']['output']>;
  doorSalesExportToken?: Maybe<Scalars['String']['output']>;
  doorlistExportToken?: Maybe<Scalars['String']['output']>;
  eventReportExportToken?: Maybe<Scalars['String']['output']>;
  extraTransactionReportExportToken?: Maybe<Scalars['String']['output']>;
  fanSurveyExportToken?: Maybe<Scalars['String']['output']>;
  fullAttendeeListToken?: Maybe<Scalars['String']['output']>;
  groupedSalesReportTicketExportToken?: Maybe<Scalars['String']['output']>;
  groupedSalesReportTransactionExportToken?: Maybe<Scalars['String']['output']>;
  ntsWhitelistExportToken?: Maybe<Scalars['String']['output']>;
  refundRequestReportCsvExportToken?: Maybe<Scalars['String']['output']>;
  salesReportExportToken?: Maybe<Scalars['String']['output']>;
  seatingReportCsvExportToken?: Maybe<Scalars['String']['output']>;
  /** @deprecated Replaced by grouped_sales_report_(transaction/ticket)_export_token */
  streamingSalesReportCsvExportToken?: Maybe<Scalars['String']['output']>;
  ticketCountReportExportToken?: Maybe<Scalars['String']['output']>;
  vatReportExportToken?: Maybe<Scalars['String']['output']>;
};

export type TopEventContainer = {
  __typename?: 'TopEventContainer';
  event: Event;
};

export enum TopEventsOrder {
  REVENUE = 'REVENUE',
  SAVES = 'SAVES',
  TICKETS_SOLD = 'TICKETS_SOLD',
  WAITING_LIST = 'WAITING_LIST'
}

export enum TvPlatform {
  DICE = 'DICE',
  EXTERNAL = 'EXTERNAL'
}

export type UninviteGuestListInput = {
  clientMutationId: Scalars['String']['input'];
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type UninviteGuestListPayload = {
  __typename?: 'UninviteGuestListPayload';
  clientMutationId: Scalars['String']['output'];
  failed?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
  successful?: Maybe<Array<Maybe<Scalars['ID']['output']>>>;
};

export type UpdateAccountInput = {
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  facebookPage?: InputMaybe<FacebookPageInput>;
  facebookUserAccessToken?: InputMaybe<Scalars['String']['input']>;
  hasEuEvents?: InputMaybe<Scalars['Boolean']['input']>;
  hasUsEvents?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['ID']['input'];
  stripeSetupInitiated?: InputMaybe<Scalars['Boolean']['input']>;
  taxForm?: InputMaybe<Scalars['Upload']['input']>;
  vatNumber?: InputMaybe<Scalars['String']['input']>;
  vatNumberProvided?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateAccountPayload = {
  __typename?: 'UpdateAccountPayload';
  account?: Maybe<Account>;
  clientMutationId: Scalars['String']['output'];
};

export type UpdateAccountUserInput = {
  accountId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  permissionProfileId?: InputMaybe<Scalars['ID']['input']>;
  userId: Scalars['ID']['input'];
};

export type UpdateAccountUserPayload = {
  __typename?: 'UpdateAccountUserPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<User>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateAdmissionConfigInput = {
  clientMutationId: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  enabled?: InputMaybe<Scalars['Boolean']['input']>;
  fromShift?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  note?: InputMaybe<Scalars['String']['input']>;
  recurringLabels?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  ticketTypeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  toShift?: InputMaybe<Scalars['Int']['input']>;
  uniqueness?: InputMaybe<ConfigUniqueness>;
};

export type UpdateAdmissionConfigPayload = {
  __typename?: 'UpdateAdmissionConfigPayload';
  admissionConfig?: Maybe<AdmissionConfig>;
  clientMutationId: Scalars['String']['output'];
};

export type UpdateArtistInput = {
  backendArtistIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  disambiguation?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  id: Scalars['ID']['input'];
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  media?: InputMaybe<Array<InputMaybe<MediaItemInputObject>>>;
  musicbrainzId?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  performerType?: InputMaybe<PerformerType>;
  profileImageAttachmentId?: InputMaybe<Scalars['ID']['input']>;
  profileImageCropRegion?: InputMaybe<CropRegionInput>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};

export type UpdateArtistPayload = {
  __typename?: 'UpdateArtistPayload';
  artist?: Maybe<Artist>;
  clientMutationId: Scalars['String']['output'];
};

export type UpdateBundleInput = {
  cityId?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  curatedByPromoter?: InputMaybe<CuratedByPromoterInput>;
  curatedByVenue?: InputMaybe<CuratedByVenueInput>;
  eventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  expiryDate?: InputMaybe<Scalars['Date']['input']>;
  fromDate?: InputMaybe<Scalars['Date']['input']>;
  hidden: Scalars['Boolean']['input'];
  id: Scalars['ID']['input'];
  maxPrice?: InputMaybe<Scalars['Int']['input']>;
  minPrice?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
  profileDetails?: InputMaybe<ProfileDetailsInput>;
  promotersBundles?: InputMaybe<Array<InputMaybe<PromotersBundles>>>;
  toDate?: InputMaybe<Scalars['Date']['input']>;
};

export type UpdateBundlePayload = {
  __typename?: 'UpdateBundlePayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Bundle>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateDefaultEventTimingsInput = {
  clientMutationId: Scalars['String']['input'];
  defaultEventTimings?: InputMaybe<Array<InputMaybe<EventTimingInput>>>;
  promoterId: Scalars['ID']['input'];
};

export type UpdateDefaultEventTimingsPayload = {
  __typename?: 'UpdateDefaultEventTimingsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Promoter>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateDraftEventInput = {
  /** The name of the object type currently being queried. */
  additionalArtists?: InputMaybe<Array<InputMaybe<AdditionalArtistInput>>>;
  additionalInfos?: InputMaybe<Array<InputMaybe<EventAdditionalInfoInput>>>;
  addressCapacity?: InputMaybe<Scalars['Int']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressSiaeCode?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  ageLimit?: InputMaybe<Scalars['String']['input']>;
  announceDate?: InputMaybe<Scalars['Time']['input']>;
  artistIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  artists?: InputMaybe<Array<InputMaybe<EventArtistInput>>>;
  attractiveFields?: InputMaybe<AttractiveFieldsInput>;
  barcodeType?: InputMaybe<Scalars['String']['input']>;
  basePriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  billingNotes?: InputMaybe<Scalars['String']['input']>;
  billingPromoterId?: InputMaybe<Scalars['ID']['input']>;
  bundleIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  characteristicIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  charityEvent?: InputMaybe<Scalars['Boolean']['input']>;
  charityId?: InputMaybe<Scalars['String']['input']>;
  checklists?: InputMaybe<Array<InputMaybe<ChecklistInput>>>;
  cityIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  closeEventDate?: InputMaybe<Scalars['Time']['input']>;
  colour?: InputMaybe<ColourInput>;
  completedSteps?: InputMaybe<Scalars['Int']['input']>;
  costAmount?: InputMaybe<Scalars['Int']['input']>;
  costCurrency?: InputMaybe<Scalars['String']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  diceStatusNotes?: InputMaybe<Scalars['String']['input']>;
  diceStreamDuration?: InputMaybe<Scalars['Int']['input']>;
  diceStreamDvrEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  diceStreamRewatchEnabledUntil?: InputMaybe<Scalars['Time']['input']>;
  diceTv?: InputMaybe<Scalars['Boolean']['input']>;
  diceTvPlatform?: InputMaybe<TvPlatform>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  doorlistAdditionalRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  doorlistRecipientIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventIdLive?: InputMaybe<Scalars['String']['input']>;
  eventImages?: InputMaybe<Array<InputMaybe<EventImageInput>>>;
  eventLoadPredictions?: InputMaybe<Array<InputMaybe<EventLoadPredictionInput>>>;
  eventPromoters?: InputMaybe<Array<InputMaybe<EventPromoter>>>;
  eventRules?: InputMaybe<EventRulesInput>;
  eventSeatingChartId?: InputMaybe<Scalars['ID']['input']>;
  eventSharingObjects?: InputMaybe<Array<InputMaybe<EventSharingObjectInput>>>;
  eventType?: InputMaybe<EventType>;
  eventVenues?: InputMaybe<Array<InputMaybe<EventVenues>>>;
  extraNotes?: InputMaybe<Scalars['String']['input']>;
  fanFacingPromoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  fanSupportNotes?: InputMaybe<FanSupportNotesInput>;
  faqs?: InputMaybe<Array<InputMaybe<FaqInput>>>;
  featuredAreas?: InputMaybe<Array<InputMaybe<FeaturedAreaInput>>>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  feesBehaviour?: InputMaybe<FeesBehaviour>;
  flags?: InputMaybe<EventFlagsInput>;
  freeEvent?: InputMaybe<Scalars['Boolean']['input']>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  id: Scalars['ID']['input'];
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  isTicketAvailableAtDoor?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  licenseNumber?: InputMaybe<Scalars['String']['input']>;
  lineup?: InputMaybe<Array<InputMaybe<LineupInput>>>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  lockVersion?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  manualValidationEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  marketeerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  maxTicketsLimit?: InputMaybe<Scalars['Int']['input']>;
  media?: InputMaybe<Array<InputMaybe<MediaItemInputObject>>>;
  musicbrainzArtists?: InputMaybe<Array<InputMaybe<MusicbrainzArtists>>>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotification?: InputMaybe<Scalars['Boolean']['input']>;
  onSaleNotificationAt?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotificationSmsContent?: InputMaybe<Scalars['String']['input']>;
  overriddenPromoterName?: InputMaybe<Scalars['String']['input']>;
  overrideFees?: InputMaybe<Scalars['Boolean']['input']>;
  permName?: InputMaybe<Scalars['String']['input']>;
  platformAccountCode?: InputMaybe<PlatformAccountCode>;
  postFanPriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  presentedBy?: InputMaybe<Scalars['String']['input']>;
  printedTicketFormat?: InputMaybe<PrintedTicketFormat>;
  products?: InputMaybe<Array<InputMaybe<ProductInput>>>;
  promoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  promoterStatusNotes?: InputMaybe<Scalars['String']['input']>;
  pwlWindow?: InputMaybe<Scalars['Int']['input']>;
  readAccessEmails?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  recurrentEventSchedule?: InputMaybe<RecurrentEventsScheduleInput>;
  relatedEventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  requiresBoxOfficeTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  requiresTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  restrictCountries?: InputMaybe<Array<InputMaybe<CountryCode>>>;
  restrictCountriesKind?: InputMaybe<RestrictionKind>;
  salesforceContractId?: InputMaybe<Scalars['ID']['input']>;
  scheduleStatus?: InputMaybe<ScheduleStatus>;
  seatingChannels?: InputMaybe<Array<InputMaybe<SeatingChannelInput>>>;
  sendReceiptViaSms?: InputMaybe<Scalars['Boolean']['input']>;
  showArtistDescription?: InputMaybe<ShowArtistDescription>;
  socialDistancingRulesetKey?: InputMaybe<Scalars['String']['input']>;
  stages?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  stripeAccountId?: InputMaybe<Scalars['String']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  taxSettings?: InputMaybe<TaxSettingsInput>;
  thirdPartySettingsId?: InputMaybe<Scalars['ID']['input']>;
  ticketPools?: InputMaybe<Array<InputMaybe<AttachTicketPoolInput>>>;
  ticketType?: InputMaybe<Scalars['String']['input']>;
  ticketTypes?: InputMaybe<Array<InputMaybe<TicketTypesInput>>>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  totalTickets?: InputMaybe<Scalars['Int']['input']>;
  venue?: InputMaybe<Scalars['String']['input']>;
  venueConfigurationId?: InputMaybe<Scalars['ID']['input']>;
  venueIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  venueName?: InputMaybe<Scalars['String']['input']>;
  venueSchedules?: InputMaybe<Array<InputMaybe<VenueScheduleInput>>>;
  venueSpaceId?: InputMaybe<Scalars['ID']['input']>;
  waitingListExchangeWindows?: InputMaybe<Array<InputMaybe<WaitingListExchangeWindowInput>>>;
};

export type UpdateDraftEventPayload = {
  __typename?: 'UpdateDraftEventPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Event>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateEventInput = {
  /** The name of the object type currently being queried. */
  additionalArtists?: InputMaybe<Array<InputMaybe<AdditionalArtistInput>>>;
  additionalInfos?: InputMaybe<Array<InputMaybe<EventAdditionalInfoInput>>>;
  addressCapacity?: InputMaybe<Scalars['Int']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressSiaeCode?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  ageLimit?: InputMaybe<Scalars['String']['input']>;
  announceDate?: InputMaybe<Scalars['Time']['input']>;
  artistIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  artists?: InputMaybe<Array<InputMaybe<EventArtistInput>>>;
  attractiveFields?: InputMaybe<AttractiveFieldsInput>;
  barcodeType?: InputMaybe<Scalars['String']['input']>;
  basePriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  billingNotes?: InputMaybe<Scalars['String']['input']>;
  billingPromoterId?: InputMaybe<Scalars['ID']['input']>;
  bundleIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  characteristicIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  charityEvent?: InputMaybe<Scalars['Boolean']['input']>;
  charityId?: InputMaybe<Scalars['String']['input']>;
  checklists?: InputMaybe<Array<InputMaybe<ChecklistInput>>>;
  cityIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  closeEventDate?: InputMaybe<Scalars['Time']['input']>;
  colour?: InputMaybe<ColourInput>;
  completedSteps?: InputMaybe<Scalars['Int']['input']>;
  costAmount?: InputMaybe<Scalars['Int']['input']>;
  costCurrency?: InputMaybe<Scalars['String']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  diceStatusNotes?: InputMaybe<Scalars['String']['input']>;
  diceStreamDuration?: InputMaybe<Scalars['Int']['input']>;
  diceStreamDvrEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  diceStreamRewatchEnabledUntil?: InputMaybe<Scalars['Time']['input']>;
  diceTv?: InputMaybe<Scalars['Boolean']['input']>;
  diceTvPlatform?: InputMaybe<TvPlatform>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  doorlistAdditionalRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  doorlistRecipientIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventIdLive?: InputMaybe<Scalars['String']['input']>;
  eventImages?: InputMaybe<Array<InputMaybe<EventImageInput>>>;
  eventLoadPredictions?: InputMaybe<Array<InputMaybe<EventLoadPredictionInput>>>;
  eventPromoters?: InputMaybe<Array<InputMaybe<EventPromoter>>>;
  eventRules?: InputMaybe<EventRulesInput>;
  eventSeatingChartId?: InputMaybe<Scalars['ID']['input']>;
  eventSharingObjects?: InputMaybe<Array<InputMaybe<EventSharingObjectInput>>>;
  eventType?: InputMaybe<EventType>;
  eventVenues?: InputMaybe<Array<InputMaybe<EventVenues>>>;
  extraNotes?: InputMaybe<Scalars['String']['input']>;
  fanFacingPromoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  fanSupportNotes?: InputMaybe<FanSupportNotesInput>;
  faqs?: InputMaybe<Array<InputMaybe<FaqInput>>>;
  featuredAreas?: InputMaybe<Array<InputMaybe<FeaturedAreaInput>>>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  feesBehaviour?: InputMaybe<FeesBehaviour>;
  flags?: InputMaybe<EventFlagsInput>;
  freeEvent?: InputMaybe<Scalars['Boolean']['input']>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  id: Scalars['ID']['input'];
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  isTicketAvailableAtDoor?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  licenseNumber?: InputMaybe<Scalars['String']['input']>;
  lineup?: InputMaybe<Array<InputMaybe<LineupInput>>>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  lockVersion?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  manualValidationEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  marketeerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  maxTicketsLimit?: InputMaybe<Scalars['Int']['input']>;
  media?: InputMaybe<Array<InputMaybe<MediaItemInputObject>>>;
  musicbrainzArtists?: InputMaybe<Array<InputMaybe<MusicbrainzArtists>>>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotification?: InputMaybe<Scalars['Boolean']['input']>;
  onSaleNotificationAt?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotificationSmsContent?: InputMaybe<Scalars['String']['input']>;
  overriddenPromoterName?: InputMaybe<Scalars['String']['input']>;
  overrideFees?: InputMaybe<Scalars['Boolean']['input']>;
  permName?: InputMaybe<Scalars['String']['input']>;
  platformAccountCode?: InputMaybe<PlatformAccountCode>;
  postFanPriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  presentedBy?: InputMaybe<Scalars['String']['input']>;
  printedTicketFormat?: InputMaybe<PrintedTicketFormat>;
  products?: InputMaybe<Array<InputMaybe<ProductInput>>>;
  promoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  promoterStatusNotes?: InputMaybe<Scalars['String']['input']>;
  pwlWindow?: InputMaybe<Scalars['Int']['input']>;
  readAccessEmails?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  recurrentEventSchedule?: InputMaybe<RecurrentEventsScheduleInput>;
  relatedEventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  requiresBoxOfficeTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  requiresTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  restrictCountries?: InputMaybe<Array<InputMaybe<CountryCode>>>;
  restrictCountriesKind?: InputMaybe<RestrictionKind>;
  salesforceContractId?: InputMaybe<Scalars['ID']['input']>;
  scheduleStatus?: InputMaybe<ScheduleStatus>;
  seatingChannels?: InputMaybe<Array<InputMaybe<SeatingChannelInput>>>;
  sendReceiptViaSms?: InputMaybe<Scalars['Boolean']['input']>;
  showArtistDescription?: InputMaybe<ShowArtistDescription>;
  socialDistancingRulesetKey?: InputMaybe<Scalars['String']['input']>;
  stages?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  stripeAccountId?: InputMaybe<Scalars['String']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  taxSettings?: InputMaybe<TaxSettingsInput>;
  thirdPartySettingsId?: InputMaybe<Scalars['ID']['input']>;
  ticketPools?: InputMaybe<Array<InputMaybe<AttachTicketPoolInput>>>;
  ticketType?: InputMaybe<Scalars['String']['input']>;
  ticketTypes?: InputMaybe<Array<InputMaybe<TicketTypesInput>>>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  totalTickets?: InputMaybe<Scalars['Int']['input']>;
  venue?: InputMaybe<Scalars['String']['input']>;
  venueConfigurationId?: InputMaybe<Scalars['ID']['input']>;
  venueIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  venueName?: InputMaybe<Scalars['String']['input']>;
  venueSchedules?: InputMaybe<Array<InputMaybe<VenueScheduleInput>>>;
  venueSpaceId?: InputMaybe<Scalars['ID']['input']>;
  waitingListExchangeWindows?: InputMaybe<Array<InputMaybe<WaitingListExchangeWindowInput>>>;
};

export type UpdateEventPayload = {
  __typename?: 'UpdateEventPayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
  warnings?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type UpdateEventPromotionInput = {
  clientMutationId: Scalars['String']['input'];
  code?: InputMaybe<Scalars['String']['input']>;
  codesOperations?: InputMaybe<Array<InputMaybe<CodesOperationsInput>>>;
  discount?: InputMaybe<Scalars['Int']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  fanFacingName?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  maxRedemptions?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  seatsIoChannel?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
  ticketTypeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  unlockAfterExpired?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateEventPromotionPayload = {
  __typename?: 'UpdateEventPromotionPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventPromotion>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateEventReviewInput = {
  assigneeId?: InputMaybe<Scalars['ID']['input']>;
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  priority?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<EventReviewStatus>;
};

export type UpdateEventReviewPayload = {
  __typename?: 'UpdateEventReviewPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventReview>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateEventSeatingChannelInput = {
  clientMutationId: Scalars['String']['input'];
  color: Scalars['String']['input'];
  eventPromotionId?: InputMaybe<Scalars['ID']['input']>;
  key: Scalars['ID']['input'];
  name: Scalars['String']['input'];
  objects?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type UpdateEventSeatingChannelObjectsInput = {
  clientMutationId: Scalars['String']['input'];
  key: Scalars['ID']['input'];
  objectsToAdd?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  objectsToRemove?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type UpdateEventSeatingChannelObjectsPayload = {
  __typename?: 'UpdateEventSeatingChannelObjectsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<SeatsIoChannelWithObjects>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateEventSeatingChannelPayload = {
  __typename?: 'UpdateEventSeatingChannelPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<SeatsIoChannel>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateEventStateInput = {
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  notify?: InputMaybe<Scalars['Boolean']['input']>;
  notifyOn?: InputMaybe<Scalars['Time']['input']>;
  state: EventState;
};

export type UpdateEventStatePayload = {
  __typename?: 'UpdateEventStatePayload';
  clientMutationId: Scalars['String']['output'];
  event?: Maybe<Event>;
};

export type UpdateFanConnectInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  id: Scalars['ID']['input'];
  message: Scalars['String']['input'];
  onBehalfOfUserId?: InputMaybe<Scalars['ID']['input']>;
  scheduledAt?: InputMaybe<Scalars['Time']['input']>;
  sendMeACopy?: InputMaybe<Scalars['Boolean']['input']>;
  ticketTypeIds: Array<InputMaybe<Scalars['ID']['input']>>;
  title: Scalars['String']['input'];
};

export type UpdateFanConnectPayload = {
  __typename?: 'UpdateFanConnectPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateFanSurveyQuestionInput = {
  clientMutationId: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
  fanQuestionInput?: InputMaybe<FanQuestionInput>;
};

export type UpdateFanSurveyQuestionPayload = {
  __typename?: 'UpdateFanSurveyQuestionPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Event>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateGuestListEntryInput = {
  clientMutationId: Scalars['String']['input'];
  email?: InputMaybe<Scalars['EmailAddress']['input']>;
  id: Scalars['ID']['input'];
  quantity: Scalars['Int']['input'];
  ticketTypeId: Scalars['ID']['input'];
};

export type UpdateGuestListEntryPayload = {
  __typename?: 'UpdateGuestListEntryPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<GuestListEntry>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateHierarchicalTagInput = {
  clientMutationId: Scalars['String']['input'];
  deprecated?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  name: Scalars['String']['input'];
  parentId: Scalars['String']['input'];
};

export type UpdateHierarchicalTagPayload = {
  __typename?: 'UpdateHierarchicalTagPayload';
  clientMutationId: Scalars['String']['output'];
  hierarchicalTag?: Maybe<HierarchicalTag>;
};

export type UpdateInventoryProductsInput = {
  clientMutationId: Scalars['String']['input'];
  inventoryId: Scalars['ID']['input'];
  products: Array<InputMaybe<InventoryProductInput>>;
};

export type UpdateInventoryProductsPayload = {
  __typename?: 'UpdateInventoryProductsPayload';
  clientMutationId: Scalars['String']['output'];
  inventory?: Maybe<Inventory>;
};

export type UpdateLabelInput = {
  checklists?: InputMaybe<Array<InputMaybe<ChecklistInput>>>;
  clientMutationId: Scalars['String']['input'];
  description: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  name: Scalars['String']['input'];
};

export type UpdateLabelPayload = {
  __typename?: 'UpdateLabelPayload';
  clientMutationId: Scalars['String']['output'];
  label?: Maybe<Label>;
};

export type UpdateLinkoutInput = {
  artistId?: InputMaybe<Scalars['ID']['input']>;
  cityIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  currency?: InputMaybe<Scalars['String']['input']>;
  date: Scalars['Time']['input'];
  destinationEventId?: InputMaybe<Scalars['ID']['input']>;
  endDate: Scalars['Time']['input'];
  externalUrl?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  imageAttachmentId?: InputMaybe<Scalars['ID']['input']>;
  imageCropRegion?: InputMaybe<CropRegionInput>;
  linkoutType: LinkoutType;
  name: Scalars['String']['input'];
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  price?: InputMaybe<Scalars['Int']['input']>;
  promoterId?: InputMaybe<Scalars['ID']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  timezone: Scalars['String']['input'];
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export type UpdateLinkoutPayload = {
  __typename?: 'UpdateLinkoutPayload';
  clientMutationId: Scalars['String']['output'];
  linkout?: Maybe<Linkout>;
};

export type UpdateMailchimpSettingsInput = {
  clientMutationId: Scalars['String']['input'];
  listId: Scalars['String']['input'];
  syncOnlyOptIn: Scalars['Boolean']['input'];
};

export type UpdateMailchimpSettingsPayload = {
  __typename?: 'UpdateMailchimpSettingsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Viewer>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateMarketeerInput = {
  appOptInEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  clientMutationId: Scalars['String']['input'];
  contacts?: InputMaybe<Array<InputMaybe<ContactsInput>>>;
  fbAccessToken?: InputMaybe<Scalars['String']['input']>;
  fbPixelId?: InputMaybe<Scalars['String']['input']>;
  gaTrackingId?: InputMaybe<Scalars['String']['input']>;
  googleAdsConversionId?: InputMaybe<Scalars['String']['input']>;
  googleAdsPurchaseConversionLabel?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  privacyPolicyLink?: InputMaybe<Scalars['String']['input']>;
  tiktokPixelId?: InputMaybe<Scalars['String']['input']>;
  twitterCheckoutInitiatedPixelId?: InputMaybe<Scalars['String']['input']>;
  twitterPixelId?: InputMaybe<Scalars['String']['input']>;
  twitterPurchasePixelId?: InputMaybe<Scalars['String']['input']>;
  webOptInEnabled?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateMarketeerPayload = {
  __typename?: 'UpdateMarketeerPayload';
  clientMutationId: Scalars['String']['output'];
  marketeer?: Maybe<Marketeer>;
};

export type UpdatePasswordInput = {
  clientMutationId: Scalars['String']['input'];
  newPassword?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePasswordPayload = {
  __typename?: 'UpdatePasswordPayload';
  clientMutationId: Scalars['String']['output'];
  viewer?: Maybe<Viewer>;
};

export type UpdatePermissionProfileInput = {
  caption?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  subjects?: InputMaybe<Array<InputMaybe<SubjectInput>>>;
};

export type UpdatePermissionProfilePayload = {
  __typename?: 'UpdatePermissionProfilePayload';
  clientMutationId: Scalars['String']['output'];
  permissionProfile?: Maybe<PermissionProfile>;
};

export type UpdatePreferredLanguageInput = {
  clientMutationId: Scalars['String']['input'];
  preferredLanguage?: InputMaybe<Language>;
};

export type UpdatePreferredLanguagePayload = {
  __typename?: 'UpdatePreferredLanguagePayload';
  clientMutationId: Scalars['String']['output'];
  viewer?: Maybe<Viewer>;
};

export type UpdateProfileInput = {
  clientMutationId: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  mioRedesignV2?: InputMaybe<Scalars['Boolean']['input']>;
  preferredLanguage?: InputMaybe<Language>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateProfilePayload = {
  __typename?: 'UpdateProfilePayload';
  clientMutationId: Scalars['String']['output'];
  viewer?: Maybe<Viewer>;
};

export type UpdatePromoterInput = {
  accountIban?: InputMaybe<Scalars['String']['input']>;
  accountManagerId?: InputMaybe<Scalars['ID']['input']>;
  accountManagerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  accountName?: InputMaybe<Scalars['String']['input']>;
  accountNumber?: InputMaybe<Scalars['String']['input']>;
  accountSortCode?: InputMaybe<Scalars['String']['input']>;
  accountType?: InputMaybe<Scalars['String']['input']>;
  accountVatNumber?: InputMaybe<Scalars['String']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  allowSkipReview?: InputMaybe<Scalars['Boolean']['input']>;
  apiToken?: InputMaybe<Scalars['String']['input']>;
  apiTokenExpiryDate?: InputMaybe<Scalars['Time']['input']>;
  associatedMarketeerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  autoRescheduledEventRefunds?: InputMaybe<AutoRescheduledEventRefundsInput>;
  automaticRollingPaymentsConfiguration?: InputMaybe<AutomaticRollingPaymentsConfigurationInput>;
  bankAddress?: InputMaybe<Scalars['String']['input']>;
  bankName?: InputMaybe<Scalars['String']['input']>;
  billingNotes?: InputMaybe<Scalars['String']['input']>;
  charityTaxFree?: InputMaybe<Scalars['Boolean']['input']>;
  clientMutationId: Scalars['String']['input'];
  clientSuccessManagerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  contacts?: InputMaybe<Array<InputMaybe<ContactsInput>>>;
  coolingOffPeriod?: InputMaybe<Scalars['Boolean']['input']>;
  coolingOffPeriodHours?: InputMaybe<Scalars['Int']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  curatedBundle?: InputMaybe<CuratedBundleInput>;
  dicePartner?: InputMaybe<Scalars['Boolean']['input']>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  disabledReason?: InputMaybe<EnumAccountDisabledReason>;
  displayName?: InputMaybe<Scalars['String']['input']>;
  eventDefaults?: InputMaybe<EventDefaultsInput>;
  extrasEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  fanSupportNotes?: InputMaybe<FanSupportNotesInput>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  forbidSelfPayouts?: InputMaybe<Scalars['Boolean']['input']>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  holdPayouts?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['ID']['input'];
  isDisabled?: InputMaybe<Scalars['Boolean']['input']>;
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  legalEntity?: InputMaybe<Scalars['String']['input']>;
  licenseNumber?: InputMaybe<Scalars['String']['input']>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  merchEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  permissionProfileOverrides?: InputMaybe<Array<InputMaybe<PermissionProfileOverrideInput>>>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  profileActive?: InputMaybe<Scalars['Boolean']['input']>;
  profileDetails?: InputMaybe<ProfileDetailsInput>;
  promoterTaxSettings?: InputMaybe<PromoterTaxSettingsInput>;
  qflowEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  resoldEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  routingNumber?: InputMaybe<Scalars['String']['input']>;
  salesforcePromoterFields?: InputMaybe<SalesforcePromoterFieldsInput>;
  sendReceiptViaSms?: InputMaybe<Scalars['Boolean']['input']>;
  showPriceBreakdown?: InputMaybe<Scalars['Boolean']['input']>;
  statusNotes?: InputMaybe<Scalars['String']['input']>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  stripeAccountId?: InputMaybe<Scalars['String']['input']>;
  stripeDocumentId?: InputMaybe<Scalars['String']['input']>;
  stripeFallbackAccountId?: InputMaybe<Scalars['String']['input']>;
  stripeFallbackPlatformCode?: InputMaybe<PlatformAccountCode>;
  stripeLocationId?: InputMaybe<Scalars['String']['input']>;
  stripeVerified?: InputMaybe<Scalars['Boolean']['input']>;
  swiftCode?: InputMaybe<Scalars['String']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  taxCode?: InputMaybe<Scalars['String']['input']>;
  ticketAgreementComplete?: InputMaybe<Scalars['Boolean']['input']>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  typeOfOrganizer?: InputMaybe<EnumTypeOfOrganizer>;
};

export type UpdatePromoterPayload = {
  __typename?: 'UpdatePromoterPayload';
  clientMutationId: Scalars['String']['output'];
  promoter?: Maybe<Promoter>;
};

export type UpdateReportScheduleInput = {
  clientMutationId: Scalars['String']['input'];
  emailList?: InputMaybe<Array<InputMaybe<Scalars['EmailAddress']['input']>>>;
  endAt?: InputMaybe<Scalars['Time']['input']>;
  id: Scalars['ID']['input'];
  locale?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  options?: InputMaybe<ScheduledReportOptionsInput>;
  recurrence?: InputMaybe<RecurrenceInput>;
  startAt?: InputMaybe<Scalars['Time']['input']>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateReportSchedulePayload = {
  __typename?: 'UpdateReportSchedulePayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ReportSchedule>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateScheduledReportInput = {
  clientMutationId: Scalars['String']['input'];
  emailList?: InputMaybe<Array<InputMaybe<Scalars['EmailAddress']['input']>>>;
  id: Scalars['ID']['input'];
  locale?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  options?: InputMaybe<ScheduledReportOptionsInput>;
  scheduledAt?: InputMaybe<Scalars['Time']['input']>;
};

export type UpdateScheduledReportPayload = {
  __typename?: 'UpdateScheduledReportPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ScheduledReport>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateTagInput = {
  clientMutationId: Scalars['String']['input'];
  deprecated?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  name: Scalars['String']['input'];
};

export type UpdateTagPayload = {
  __typename?: 'UpdateTagPayload';
  clientMutationId: Scalars['String']['output'];
  tag?: Maybe<Tag>;
};

export type UpdateThirdPartySettingsInput = {
  appIcon: Scalars['String']['input'];
  appLink: Scalars['String']['input'];
  appName: Scalars['String']['input'];
  clientMutationId: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  idVerification: Scalars['Boolean']['input'];
  promoterDisplayName?: InputMaybe<Scalars['String']['input']>;
  promoterId?: InputMaybe<Scalars['ID']['input']>;
  provideSecureUserAuth: Scalars['Boolean']['input'];
};

export type UpdateThirdPartySettingsPayload = {
  __typename?: 'UpdateThirdPartySettingsPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<ThirdPartySettings>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateTicketPoolInput = {
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  clientMutationId: Scalars['String']['input'];
  holds?: InputMaybe<Array<InputMaybe<HoldInput>>>;
  id: Scalars['ID']['input'];
  maxAllocation?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  ticketTypes?: InputMaybe<Array<InputMaybe<TicketTypesInput>>>;
  unlimitedAllocation?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdateTicketPoolPayload = {
  __typename?: 'UpdateTicketPoolPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<TicketPool>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpdateUserInput = {
  clientMutationId: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  mioRedesignV2?: InputMaybe<Scalars['Boolean']['input']>;
  mongoUserId?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  notificationPermissions?: InputMaybe<NotificationPermissionsInput>;
  permissionProfileId?: InputMaybe<Scalars['ID']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  position?: InputMaybe<Scalars['String']['input']>;
  preferredLanguage?: InputMaybe<Language>;
};

export type UpdateUserPayload = {
  __typename?: 'UpdateUserPayload';
  clientMutationId: Scalars['String']['output'];
  user?: Maybe<User>;
};

export type UpdateVenueInput = {
  accessControl?: InputMaybe<Scalars['String']['input']>;
  accessibilityLink?: InputMaybe<Scalars['String']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  ageLimit?: InputMaybe<Scalars['String']['input']>;
  allocatedSeating?: InputMaybe<Scalars['String']['input']>;
  areas?: InputMaybe<Array<InputMaybe<CreateOrUpdateVenueAreaInput>>>;
  associatedPromoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  attractiveEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  attractiveRoomSiaeCode?: InputMaybe<Scalars['String']['input']>;
  attributes?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  barcodeType?: InputMaybe<Scalars['String']['input']>;
  capacity?: InputMaybe<Scalars['Int']['input']>;
  characteristicIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  cityId?: InputMaybe<Scalars['String']['input']>;
  clientMutationId: Scalars['String']['input'];
  configurations?: InputMaybe<Array<InputMaybe<CreateOrUpdateVenueConfigurationInput>>>;
  contactEmail?: InputMaybe<Scalars['String']['input']>;
  contactPhone?: InputMaybe<Scalars['String']['input']>;
  contacts?: InputMaybe<Array<InputMaybe<ContactsInput>>>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  curatedBundle?: InputMaybe<CuratedBundleInput>;
  dicePartner?: InputMaybe<Scalars['Boolean']['input']>;
  externalLinks?: InputMaybe<ExternalLinksInput>;
  facebookPageId?: InputMaybe<Scalars['String']['input']>;
  faqs?: InputMaybe<Array<InputMaybe<FaqInput>>>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  flags?: InputMaybe<VenueFlagsInput>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  hideCapacity?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['ID']['input'];
  isSecret?: InputMaybe<Scalars['Boolean']['input']>;
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  logoAttachmentId?: InputMaybe<Scalars['ID']['input']>;
  logoCropRegion?: InputMaybe<CropRegionInput>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  mobileTicketsEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  multizone?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  profileActive?: InputMaybe<Scalars['Boolean']['input']>;
  profileDetails?: InputMaybe<ProfileDetailsInput>;
  promoterAllocation?: InputMaybe<Scalars['Int']['input']>;
  qflowEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  scannerModel?: InputMaybe<Scalars['String']['input']>;
  scannerType?: InputMaybe<Scalars['String']['input']>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  ticketType?: InputMaybe<Scalars['String']['input']>;
  ticketValidation?: InputMaybe<Scalars['String']['input']>;
  ticketingPartner?: InputMaybe<Scalars['String']['input']>;
  tier?: InputMaybe<VenueTier>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<VenueType>;
  venueAllocation?: InputMaybe<Scalars['Int']['input']>;
  venueImages?: InputMaybe<Array<InputMaybe<VenueImageInput>>>;
  venueOwnerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  venueSpaces?: InputMaybe<Array<InputMaybe<VenueSpaceInput>>>;
};

export type UpdateVenuePayload = {
  __typename?: 'UpdateVenuePayload';
  clientMutationId: Scalars['String']['output'];
  venue?: Maybe<Venue>;
};

export type UploadEventPromotionCodesInput = {
  clientMutationId: Scalars['String']['input'];
  filename: Scalars['String']['input'];
  force?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['ID']['input'];
};

export type UploadEventPromotionCodesPayload = {
  __typename?: 'UploadEventPromotionCodesPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<EventPromotion>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

export type UpsertArtistDestinationAccountInput = {
  artistId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  region?: InputMaybe<Scalars['String']['input']>;
};

export type UpsertArtistDestinationAccountPayload = {
  __typename?: 'UpsertArtistDestinationAccountPayload';
  clientMutationId: Scalars['String']['output'];
  destinationAccount?: Maybe<DestinationAccount>;
};

export type UpsertInventoryInput = {
  artistId: Scalars['ID']['input'];
  clientMutationId: Scalars['String']['input'];
  shopifyStore?: InputMaybe<ShopifyStoreInput>;
};

export type UpsertInventoryPayload = {
  __typename?: 'UpsertInventoryPayload';
  clientMutationId: Scalars['String']['output'];
  inventory?: Maybe<Inventory>;
};

export type User = Name & Node & {
  __typename?: 'User';
  accountUsers?: Maybe<Array<Maybe<AccountUser>>>;
  detailedRemittanceRecipient: Scalars['Boolean']['output'];
  doorlistRecipient: Scalars['Boolean']['output'];
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  hasQflowAccount: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  loginEnabled: Scalars['Boolean']['output'];
  mioRedesignV2?: Maybe<Scalars['Boolean']['output']>;
  mongoUserId?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  notificationPermissions?: Maybe<NotificationPermissions>;
  pamUser?: Maybe<Scalars['Boolean']['output']>;
  permissionProfile?: Maybe<PermissionProfile>;
  phone?: Maybe<Scalars['String']['output']>;
  position?: Maybe<Scalars['String']['output']>;
  preferredLanguage?: Maybe<Language>;
  promoters?: Maybe<Array<Maybe<Promoter>>>;
  remittanceRecipient: Scalars['Boolean']['output'];
  suggestedMongoUserIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  timezoneName?: Maybe<Scalars['String']['output']>;
  venues?: Maybe<Array<Maybe<Venue>>>;
};

/** Account user or invitation request */
export type UserAccountObject = AccountUserInvitation | User;

export type UserAccountObjectConnection = {
  __typename?: 'UserAccountObjectConnection';
  edges?: Maybe<Array<Maybe<UserAccountObjectEdge>>>;
  pageInfo: PageInfo;
};

export type UserAccountObjectEdge = {
  __typename?: 'UserAccountObjectEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<UserAccountObject>;
};

export enum UserSource {
  KIM = 'KIM',
  SELF_SERVICE = 'SELF_SERVICE',
  SELF_SERVICE_V2 = 'SELF_SERVICE_V2'
}

export type UsersConnection = {
  __typename?: 'UsersConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<UsersEdge>>>;
  pageInfo: PageInfo;
};

export type UsersEdge = {
  __typename?: 'UsersEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<User>;
};

export type UsersWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<UsersWhereInput>>>;
  email?: InputMaybe<OperatorsString>;
  name?: InputMaybe<OperatorsString>;
};

export type ValidateDraftEventInput = {
  /** The name of the object type currently being queried. */
  additionalArtists?: InputMaybe<Array<InputMaybe<AdditionalArtistInput>>>;
  additionalInfos?: InputMaybe<Array<InputMaybe<EventAdditionalInfoInput>>>;
  addressCapacity?: InputMaybe<Scalars['Int']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLocality?: InputMaybe<Scalars['String']['input']>;
  addressRegion?: InputMaybe<Scalars['String']['input']>;
  addressSiaeCode?: InputMaybe<Scalars['String']['input']>;
  addressState?: InputMaybe<Scalars['String']['input']>;
  ageLimit?: InputMaybe<Scalars['String']['input']>;
  announceDate?: InputMaybe<Scalars['Time']['input']>;
  artistIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  artists?: InputMaybe<Array<InputMaybe<EventArtistInput>>>;
  attractiveFields?: InputMaybe<AttractiveFieldsInput>;
  barcodeType?: InputMaybe<Scalars['String']['input']>;
  basePriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  billingNotes?: InputMaybe<Scalars['String']['input']>;
  billingPromoterId?: InputMaybe<Scalars['ID']['input']>;
  bundleIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  characteristicIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  charityEvent?: InputMaybe<Scalars['Boolean']['input']>;
  charityId?: InputMaybe<Scalars['String']['input']>;
  checklists?: InputMaybe<Array<InputMaybe<ChecklistInput>>>;
  cityIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  clientMutationId: Scalars['String']['input'];
  closeEventDate?: InputMaybe<Scalars['Time']['input']>;
  colour?: InputMaybe<ColourInput>;
  completedSteps?: InputMaybe<Scalars['Int']['input']>;
  costAmount?: InputMaybe<Scalars['Int']['input']>;
  costCurrency?: InputMaybe<Scalars['String']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['Time']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  diceStatusNotes?: InputMaybe<Scalars['String']['input']>;
  diceStreamDuration?: InputMaybe<Scalars['Int']['input']>;
  diceStreamDvrEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  diceStreamRewatchEnabledUntil?: InputMaybe<Scalars['Time']['input']>;
  diceTv?: InputMaybe<Scalars['Boolean']['input']>;
  diceTvPlatform?: InputMaybe<TvPlatform>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  doorlistAdditionalRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  doorlistRecipientIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventIdLive?: InputMaybe<Scalars['String']['input']>;
  eventImages?: InputMaybe<Array<InputMaybe<EventImageInput>>>;
  eventLoadPredictions?: InputMaybe<Array<InputMaybe<EventLoadPredictionInput>>>;
  eventPromoters?: InputMaybe<Array<InputMaybe<EventPromoter>>>;
  eventRules?: InputMaybe<EventRulesInput>;
  eventSeatingChartId?: InputMaybe<Scalars['ID']['input']>;
  eventSharingObjects?: InputMaybe<Array<InputMaybe<EventSharingObjectInput>>>;
  eventType?: InputMaybe<EventType>;
  eventVenues?: InputMaybe<Array<InputMaybe<EventVenues>>>;
  extraNotes?: InputMaybe<Scalars['String']['input']>;
  fanFacingPromoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  fanSupportNotes?: InputMaybe<FanSupportNotesInput>;
  faqs?: InputMaybe<Array<InputMaybe<FaqInput>>>;
  featuredAreas?: InputMaybe<Array<InputMaybe<FeaturedAreaInput>>>;
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  feesBehaviour?: InputMaybe<FeesBehaviour>;
  flags?: InputMaybe<EventFlagsInput>;
  freeEvent?: InputMaybe<Scalars['Boolean']['input']>;
  fullAddress?: InputMaybe<Scalars['String']['input']>;
  hierarchicalTagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  id: Scalars['ID']['input'];
  isTest?: InputMaybe<Scalars['Boolean']['input']>;
  isTicketAvailableAtDoor?: InputMaybe<Scalars['Boolean']['input']>;
  labelIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  licenseNumber?: InputMaybe<Scalars['String']['input']>;
  lineup?: InputMaybe<Array<InputMaybe<LineupInput>>>;
  links?: InputMaybe<Array<InputMaybe<LinkInput>>>;
  lockVersion?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  manualValidationEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  marketeerIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  maxTicketsLimit?: InputMaybe<Scalars['Int']['input']>;
  media?: InputMaybe<Array<InputMaybe<MediaItemInputObject>>>;
  musicbrainzArtists?: InputMaybe<Array<InputMaybe<MusicbrainzArtists>>>;
  name?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  offSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleDate?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotification?: InputMaybe<Scalars['Boolean']['input']>;
  onSaleNotificationAt?: InputMaybe<Scalars['Time']['input']>;
  onSaleNotificationSmsContent?: InputMaybe<Scalars['String']['input']>;
  overriddenPromoterName?: InputMaybe<Scalars['String']['input']>;
  overrideFees?: InputMaybe<Scalars['Boolean']['input']>;
  permName?: InputMaybe<Scalars['String']['input']>;
  platformAccountCode?: InputMaybe<PlatformAccountCode>;
  postFanPriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  postOfficeBoxNumber?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  presentedBy?: InputMaybe<Scalars['String']['input']>;
  printedTicketFormat?: InputMaybe<PrintedTicketFormat>;
  products?: InputMaybe<Array<InputMaybe<ProductInput>>>;
  promoterIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  promoterStatusNotes?: InputMaybe<Scalars['String']['input']>;
  pwlWindow?: InputMaybe<Scalars['Int']['input']>;
  readAccessEmails?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  recurrentEventSchedule?: InputMaybe<RecurrentEventsScheduleInput>;
  relatedEventIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  requiresBoxOfficeTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  requiresTicketNomination?: InputMaybe<Scalars['Boolean']['input']>;
  restrictCountries?: InputMaybe<Array<InputMaybe<CountryCode>>>;
  restrictCountriesKind?: InputMaybe<RestrictionKind>;
  salesforceContractId?: InputMaybe<Scalars['ID']['input']>;
  scheduleStatus?: InputMaybe<ScheduleStatus>;
  seatingChannels?: InputMaybe<Array<InputMaybe<SeatingChannelInput>>>;
  sendReceiptViaSms?: InputMaybe<Scalars['Boolean']['input']>;
  showArtistDescription?: InputMaybe<ShowArtistDescription>;
  socialDistancingRulesetKey?: InputMaybe<Scalars['String']['input']>;
  stages?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  streetAddress?: InputMaybe<Scalars['String']['input']>;
  stripeAccountId?: InputMaybe<Scalars['String']['input']>;
  tagIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  taxSettings?: InputMaybe<TaxSettingsInput>;
  thirdPartySettingsId?: InputMaybe<Scalars['ID']['input']>;
  ticketPools?: InputMaybe<Array<InputMaybe<AttachTicketPoolInput>>>;
  ticketType?: InputMaybe<Scalars['String']['input']>;
  ticketTypes?: InputMaybe<Array<InputMaybe<TicketTypesInput>>>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  totalTickets?: InputMaybe<Scalars['Int']['input']>;
  venue?: InputMaybe<Scalars['String']['input']>;
  venueConfigurationId?: InputMaybe<Scalars['ID']['input']>;
  venueIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  venueName?: InputMaybe<Scalars['String']['input']>;
  venueSchedules?: InputMaybe<Array<InputMaybe<VenueScheduleInput>>>;
  venueSpaceId?: InputMaybe<Scalars['ID']['input']>;
  waitingListExchangeWindows?: InputMaybe<Array<InputMaybe<WaitingListExchangeWindowInput>>>;
};

export type ValidateDraftEventPayload = {
  __typename?: 'ValidateDraftEventPayload';
  clientMutationId: Scalars['String']['output'];
  /** A list of failed validations. May be blank or null if mutation succeeded. */
  messages?: Maybe<Array<Maybe<ValidationMessage>>>;
  /** The object created/updated/deleted by the mutation. May be null if mutation failed. */
  result?: Maybe<Event>;
  /** Indicates if the mutation completed successfully or not.  */
  successful: Scalars['Boolean']['output'];
};

/**
 *   Validation messages are returned when mutation input does not meet the requirements.
 *   While client-side validation is highly recommended to provide the best User Experience,
 *   All inputs will always be validated server-side.
 *
 *   Some examples of validations are:
 *
 *   * Username must be at least 10 characters
 *   * Email field does not contain an email address
 *   * Birth Date is required
 *
 *   While GraphQL has support for required values, mutation data fields are always
 *   set to optional in our API. This allows 'required field' messages
 *   to be returned in the same manner as other validations. The only exceptions
 *   are id fields, which may be required to perform updates or deletes.
 *
 */
export type ValidationMessage = {
  __typename?: 'ValidationMessage';
  /** A unique error code for the type of validation used. */
  code: Scalars['String']['output'];
  /**
   * The input field that the error applies to. The field can be used to
   * identify which field the error message should be displayed next to in the
   * presentation layer.
   *
   * If there are multiple errors to display for a field, multiple validation
   * messages will be in the result.
   *
   * This field may be null in cases where an error cannot be applied to a specific field.
   *
   */
  field?: Maybe<Scalars['String']['output']>;
  /**
   * A friendly error message, appropriate for display to the end user.
   *
   * The message is interpolated to include the appropriate variables.
   *
   * Example: `Username must be at least 10 characters`
   *
   * This message may change without notice, so we do not recommend you match against the text.
   * Instead, use the *code* field for matching.
   *
   */
  message?: Maybe<Scalars['String']['output']>;
  /** A list of substitutions to be applied to a validation message template */
  options?: Maybe<Array<Maybe<ValidationOption>>>;
  /**
   * A template used to generate the error message, with placeholders for option substiution.
   *
   * Example: `Username must be at least {count} characters`
   *
   * This message may change without notice, so we do not recommend you match against the text.
   * Instead, use the *code* field for matching.
   *
   */
  template?: Maybe<Scalars['String']['output']>;
};

export type ValidationOption = {
  __typename?: 'ValidationOption';
  /** The name of a variable to be subsituted in a validation message template */
  key: Scalars['String']['output'];
  /** The value of a variable to be substituted in a validation message template */
  value: Scalars['String']['output'];
};

export type Variant = Node & {
  __typename?: 'Variant';
  allocation: Scalars['Int']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  product: Product;
  /** @deprecated Deprecated, use name instead */
  size?: Maybe<Scalars['String']['output']>;
  sku?: Maybe<Scalars['String']['output']>;
};

export type VariantBreakdownItem = {
  __typename?: 'VariantBreakdownItem';
  totalAppSold: Scalars['Int']['output'];
  totalDigitalValue: Scalars['Int']['output'];
  totalFaceValue: Scalars['Int']['output'];
  totalSold: Scalars['Int']['output'];
  variant?: Maybe<Variant>;
  variantId: Scalars['ID']['output'];
};

export type VariantInput = {
  allocation: Scalars['Int']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  name: Scalars['String']['input'];
  size?: InputMaybe<Scalars['String']['input']>;
  sku?: InputMaybe<Scalars['String']['input']>;
};

export type Venue = Contracts & Fees & Location & Name & Node & {
  __typename?: 'Venue';
  accessControl: Scalars['String']['output'];
  accessibilityLink?: Maybe<Scalars['String']['output']>;
  addressCountry?: Maybe<Scalars['String']['output']>;
  addressLocality?: Maybe<Scalars['String']['output']>;
  addressRegion?: Maybe<Scalars['String']['output']>;
  addressState?: Maybe<Scalars['String']['output']>;
  ageLimit?: Maybe<Scalars['String']['output']>;
  allocatedSeating: Scalars['String']['output'];
  allowedForSubmission: Scalars['Boolean']['output'];
  areas?: Maybe<Array<Maybe<VenueArea>>>;
  associatedPromoters?: Maybe<Array<Maybe<Promoter>>>;
  attractiveEnabled?: Maybe<Scalars['Boolean']['output']>;
  attractiveRoomSiaeCode?: Maybe<Scalars['String']['output']>;
  attributes?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  barcodeType: Scalars['String']['output'];
  bundle?: Maybe<Bundle>;
  capacity: Scalars['Int']['output'];
  characteristics?: Maybe<Array<Maybe<Characteristic>>>;
  cityId?: Maybe<Scalars['String']['output']>;
  configurations?: Maybe<Array<Maybe<VenueConfiguration>>>;
  contactEmail?: Maybe<Scalars['String']['output']>;
  contactPhone?: Maybe<Scalars['String']['output']>;
  contacts?: Maybe<Array<Maybe<User>>>;
  contracts?: Maybe<Array<Maybe<FeesConfiguration>>>;
  countryCode?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['Time']['output']>;
  curatedBundle?: Maybe<Bundle>;
  dicePartner?: Maybe<Scalars['Boolean']['output']>;
  externalLinks: ExternalLinks;
  facebookPageId?: Maybe<Scalars['String']['output']>;
  faqs?: Maybe<Array<Maybe<Faq>>>;
  fees?: Maybe<Array<Maybe<Fee>>>;
  flags: VenueFlags;
  fullAddress?: Maybe<Scalars['String']['output']>;
  hideCapacity: Scalars['Boolean']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  isSecret?: Maybe<Scalars['Boolean']['output']>;
  isTest?: Maybe<Scalars['Boolean']['output']>;
  labels?: Maybe<Array<Maybe<Label>>>;
  latitude?: Maybe<Scalars['Float']['output']>;
  /** @deprecated Use externalLinks instead */
  links: Array<Maybe<Scalars['Map']['output']>>;
  logoAttachment?: Maybe<Attachment>;
  logoCropRegion?: Maybe<CropRegion>;
  longitude?: Maybe<Scalars['Float']['output']>;
  mobileTicketsEnabled: Scalars['Boolean']['output'];
  multizone: Scalars['Boolean']['output'];
  name?: Maybe<Scalars['String']['output']>;
  notes: Scalars['String']['output'];
  permName?: Maybe<Scalars['String']['output']>;
  postOfficeBoxNumber?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  profileActive?: Maybe<Scalars['Boolean']['output']>;
  profileDetails?: Maybe<ProfileDetails>;
  promoterAllocation: Scalars['Int']['output'];
  qflowEnabled?: Maybe<Scalars['Boolean']['output']>;
  scannerModel: Scalars['String']['output'];
  scannerType: Scalars['String']['output'];
  seatingCharts?: Maybe<SeatingChartConnection>;
  streetAddress?: Maybe<Scalars['String']['output']>;
  ticketType: Scalars['String']['output'];
  ticketValidation: Scalars['String']['output'];
  ticketingPartner: Scalars['String']['output'];
  tier?: Maybe<VenueTier>;
  timezoneName?: Maybe<Scalars['String']['output']>;
  type?: Maybe<VenueType>;
  updatedAt?: Maybe<Scalars['Time']['output']>;
  venueAllocation: Scalars['Int']['output'];
  venueImages?: Maybe<Array<Maybe<VenueImage>>>;
  venueOwners?: Maybe<Array<Maybe<Promoter>>>;
  venueSpaces?: Maybe<Array<Maybe<VenueSpace>>>;
};


export type VenueSeatingChartsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};

export type VenueAddress = {
  addressCapacity?: Maybe<Scalars['Int']['output']>;
  addressCountry?: Maybe<Scalars['String']['output']>;
  addressLocality?: Maybe<Scalars['String']['output']>;
  addressRegion?: Maybe<Scalars['String']['output']>;
  addressSiaeCode?: Maybe<Scalars['String']['output']>;
  addressState?: Maybe<Scalars['String']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  fullAddress?: Maybe<Scalars['String']['output']>;
  latitude?: Maybe<Scalars['Float']['output']>;
  longitude?: Maybe<Scalars['Float']['output']>;
  postOfficeBoxNumber?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  streetAddress?: Maybe<Scalars['String']['output']>;
  timezoneName?: Maybe<Scalars['String']['output']>;
};

export type VenueArea = Node & {
  __typename?: 'VenueArea';
  code: Scalars['String']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type VenueConfiguration = Node & {
  __typename?: 'VenueConfiguration';
  attractiveRoomSiaeCode?: Maybe<Scalars['String']['output']>;
  capacity: Scalars['Int']['output'];
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  seatingAreaConfigs: Array<Maybe<SeatingAreaConfig>>;
};

export type VenueFlags = {
  __typename?: 'VenueFlags';
  doorListRequired: Scalars['Map']['output'];
};

export type VenueFlagsInput = {
  doorListRequired?: InputMaybe<FlagValue>;
};

export type VenueImage = Node & {
  __typename?: 'VenueImage';
  attachment?: Maybe<Attachment>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
};

export type VenueImageInput = {
  attachmentId?: InputMaybe<Scalars['ID']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
};

export type VenueSchedule = Node & {
  __typename?: 'VenueSchedule';
  date?: Maybe<Scalars['Time']['output']>;
  endDate?: Maybe<Scalars['Time']['output']>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  venueConfigurationId?: Maybe<Scalars['ID']['output']>;
  venueId?: Maybe<Scalars['ID']['output']>;
};

export type VenueScheduleInput = {
  date?: InputMaybe<Scalars['Time']['input']>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  venueConfigurationId?: InputMaybe<Scalars['ID']['input']>;
  venueId?: InputMaybe<Scalars['ID']['input']>;
};

export type VenueSpace = Node & {
  __typename?: 'VenueSpace';
  /** The ID of an object */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type VenueSpaceInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  name: Scalars['String']['input'];
};

export enum VenueTier {
  TIER_1 = 'TIER_1',
  TIER_2 = 'TIER_2',
  TIER_3 = 'TIER_3'
}

export enum VenueType {
  arena = 'arena',
  bar = 'bar',
  church = 'church',
  cinema = 'cinema',
  club = 'club',
  comedy_club = 'comedy_club',
  multiroom_club = 'multiroom_club',
  pub = 'pub',
  railway_arch = 'railway_arch',
  underground = 'underground'
}

export type VenueWhereInput = {
  _or?: InputMaybe<Array<InputMaybe<VenueWhereInput>>>;
  id?: InputMaybe<OperatorsIdEqInput>;
  name?: InputMaybe<OperatorsString>;
  profileActive?: InputMaybe<OperatorsBooleanEqInput>;
};

export type VenuesConnection = {
  __typename?: 'VenuesConnection';
  count: Scalars['Int']['output'];
  edges?: Maybe<Array<Maybe<VenuesEdge>>>;
  pageInfo: PageInfo;
};

export type VenuesEdge = {
  __typename?: 'VenuesEdge';
  cursor?: Maybe<Scalars['String']['output']>;
  node?: Maybe<Venue>;
};

export type Viewer = Node & {
  __typename?: 'Viewer';
  account?: Maybe<Account>;
  activities?: Maybe<ActivitiesConnection>;
  /** get all admission_tickets for an event */
  admissionTickets?: Maybe<AdmissionTicketsConnection>;
  aggregatedSales?: Maybe<AggregatedSalesReport>;
  artists?: Maybe<ArtistConnection>;
  availableAccounts: Array<Maybe<AccountAvailability>>;
  availableEntities?: Maybe<Array<Maybe<AssociatedEntities>>>;
  /** get average scans per timeframe */
  avgScanPerTimeframe?: Maybe<Scalars['Map']['output']>;
  balanceReportExportToken: Scalars['String']['output'];
  /** get basic report */
  baseReport?: Maybe<BaseReport>;
  bundles?: Maybe<BundleConnection>;
  characteristics?: Maybe<CharacteristicConnection>;
  chargebacksReportExportToken: Scalars['String']['output'];
  cities?: Maybe<Array<Maybe<City>>>;
  config?: Maybe<Config>;
  defaultDoorlistRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  dicePartner: Scalars['Boolean']['output'];
  diceStaff: Scalars['Boolean']['output'];
  doorSalesExports?: Maybe<DoorSalesExportConnection>;
  email?: Maybe<Scalars['String']['output']>;
  eventPromotionPriceCalculation?: Maybe<Array<Maybe<EventPromotionTicketTypePrices>>>;
  eventReviews?: Maybe<EventReviewConnection>;
  events?: Maybe<EventConnection>;
  eventsCurrencies: Array<Maybe<EventCostCurrency>>;
  externalEntities?: Maybe<Array<Maybe<ExternalEntity>>>;
  /** @deprecated Please use products_revenue_report instead */
  extrasRevenueReport?: Maybe<Array<Maybe<ExtrasRevenueReportItem>>>;
  faceValueSalesAttribution?: Maybe<Scalars['Map']['output']>;
  fanActivities?: Maybe<FanActivityConnection>;
  fanConnects?: Maybe<FanConnectConnection>;
  fans?: Maybe<FanConnection>;
  featuredAreaEstimates?: Maybe<Array<Maybe<FeaturedAreaEstimate>>>;
  feeConfigurationByPromoter?: Maybe<FeesConfiguration>;
  firstName?: Maybe<Scalars['String']['output']>;
  hierarchicalTags?: Maybe<HierarchicalTagsConnection>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  impersonator?: Maybe<User>;
  labels?: Maybe<LabelsConnection>;
  lastName?: Maybe<Scalars['String']['output']>;
  linkouts?: Maybe<LinkoutConnection>;
  manualPayoutExportToken?: Maybe<Scalars['String']['output']>;
  marketeers?: Maybe<MarketeersConnection>;
  mioRedesignV2: Scalars['Boolean']['output'];
  musicbrainzArtists?: Maybe<Array<Maybe<MusicbrainzArtist>>>;
  name?: Maybe<Scalars['String']['output']>;
  payouts?: Maybe<PayoutConnection>;
  payoutsExportToken?: Maybe<Scalars['String']['output']>;
  permissionProfileStructure?: Maybe<PermissionProfileStructure>;
  permissionProfiles?: Maybe<Array<Maybe<PermissionProfile>>>;
  permissions?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  preferredLanguage?: Maybe<Language>;
  priceCalculation?: Maybe<Price>;
  productCategories?: Maybe<Array<Maybe<Category>>>;
  productPriceCalculation?: Maybe<Price>;
  productsRevenueReport?: Maybe<Array<Maybe<ProductsRevenueReportItem>>>;
  promoterFansDataExportToken: Scalars['String']['output'];
  promoters?: Maybe<PromotersConnection>;
  recurrentEventsSchedule?: Maybe<Array<Maybe<EventSchedule>>>;
  reportsAndSchedules?: Maybe<ReportOrScheduleObjectConnection>;
  revenueReport?: Maybe<Array<Maybe<RevenueReportItem>>>;
  salesReportExportToken: Scalars['String']['output'];
  salesTaxCalculation?: Maybe<Scalars['Int']['output']>;
  searchAttractiveTickets?: Maybe<AttractiveTicketConnection>;
  source?: Maybe<UserSource>;
  /** get status breakdown */
  statusBreakdown?: Maybe<Scalars['Map']['output']>;
  /** get status breakdown per ticket type */
  statusBreakdownPerTty?: Maybe<Scalars['Map']['output']>;
  stripeOperations?: Maybe<StripeOperations>;
  switchAccount?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<TagsConnection>;
  /**
   * List all Third Party Settings created for the given promoter ID.
   *
   */
  thirdPartySettings?: Maybe<Array<Maybe<ThirdPartySettings>>>;
  ticketCountSalesAttribution?: Maybe<Scalars['Map']['output']>;
  timezoneName?: Maybe<Scalars['String']['output']>;
  topEvents?: Maybe<Array<Maybe<TopEventContainer>>>;
  /** get the total scans per timeframe */
  totalScanPerTimeframe?: Maybe<Scalars['Map']['output']>;
  /** get scans per user */
  totalScanPerUser?: Maybe<Scalars['Map']['output']>;
  underMaintenance: Scalars['Boolean']['output'];
  users?: Maybe<UsersConnection>;
  venues?: Maybe<VenuesConnection>;
};


export type ViewerActivitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<ActivityWhereInput>;
};


export type ViewerAdmissionTicketsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  eventId: Scalars['ID']['input'];
  fanFilter?: InputMaybe<FanFilterInput>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


export type ViewerAggregatedSalesArgs = {
  currency?: InputMaybe<EventCostCurrency>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
};


export type ViewerArtistsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  where?: InputMaybe<ArtistWhereInput>;
};


export type ViewerAvgScanPerTimeframeArgs = {
  eventId: Scalars['ID']['input'];
  timeframe: Timeframe;
};


export type ViewerBalanceReportExportTokenArgs = {
  accountIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  endDate: Scalars['Time']['input'];
  legalEntity?: InputMaybe<Scalars['String']['input']>;
  startDate: Scalars['Time']['input'];
};


export type ViewerBaseReportArgs = {
  eventId: Scalars['ID']['input'];
};


export type ViewerBundlesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Array<InputMaybe<BundleOrder>>>;
  where?: InputMaybe<BundleWhereInput>;
};


export type ViewerCharacteristicsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<CharacteristicWhereInput>;
};


export type ViewerChargebacksReportExportTokenArgs = {
  accountIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
};


export type ViewerCitiesArgs = {
  includeGlobal?: InputMaybe<Scalars['Boolean']['input']>;
  includeHidden?: InputMaybe<Scalars['Boolean']['input']>;
  where?: InputMaybe<CitiesWhereInput>;
};


export type ViewerDefaultDoorlistRecipientsArgs = {
  venueIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};


export type ViewerDoorSalesExportsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Date']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  startDate?: InputMaybe<Scalars['Date']['input']>;
};


export type ViewerEventPromotionPriceCalculationArgs = {
  discount: Scalars['Int']['input'];
  eventId: Scalars['ID']['input'];
  ticketTypeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};


export type ViewerEventReviewsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Array<InputMaybe<EventsConnectionOrder>>>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  where?: InputMaybe<EventReviewWhereInput>;
};


export type ViewerEventsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Array<InputMaybe<EventsConnectionOrder>>>;
  scopes?: InputMaybe<EventScopesInput>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  where?: InputMaybe<EventWhereInput>;
};


export type ViewerExternalEntitiesArgs = {
  entityType?: InputMaybe<Scalars['String']['input']>;
  integrationType?: InputMaybe<Scalars['String']['input']>;
};


export type ViewerExtrasRevenueReportArgs = {
  currency?: InputMaybe<EventCostCurrency>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventId?: InputMaybe<Scalars['ID']['input']>;
  productId?: InputMaybe<Scalars['ID']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
};


export type ViewerFaceValueSalesAttributionArgs = {
  from: Scalars['Date']['input'];
  promoterId: Scalars['ID']['input'];
  to: Scalars['Date']['input'];
};


export type ViewerFanActivitiesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  fanId: Scalars['ID']['input'];
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<FanActivityWhereInput>;
};


export type ViewerFanConnectsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Array<InputMaybe<FanConnectOrder>>>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  where?: InputMaybe<FanConnectWhereInput>;
};


export type ViewerFansArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  where?: InputMaybe<FanWhereInput>;
};


export type ViewerFeaturedAreaEstimatesArgs = {
  lat: Scalars['Float']['input'];
  lng: Scalars['Float']['input'];
  radius: Scalars['Float']['input'];
};


export type ViewerFeeConfigurationByPromoterArgs = {
  eventId?: InputMaybe<Scalars['ID']['input']>;
  promoterId: Scalars['ID']['input'];
};


export type ViewerHierarchicalTagsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Array<InputMaybe<HierarchicalTagOrder>>>;
  where?: InputMaybe<HierarchicalTagWhereInput>;
};


export type ViewerLabelsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<LabelWhereInput>;
};


export type ViewerLinkoutsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
};


export type ViewerManualPayoutExportTokenArgs = {
  endDate: Scalars['Time']['input'];
  fallbackAccountId: Scalars['String']['input'];
  startDate: Scalars['Time']['input'];
};


export type ViewerMarketeersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<MarketeerWhereInput>;
};


export type ViewerMusicbrainzArtistsArgs = {
  filterKimArtists?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  query: Scalars['String']['input'];
};


export type ViewerPayoutsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Array<InputMaybe<PayoutsOrder>>>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  where?: InputMaybe<PayoutsWhereInput>;
};


export type ViewerPayoutsExportTokenArgs = {
  accountIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  kind: PayoutsExportKind;
  startDate?: InputMaybe<Scalars['Time']['input']>;
};


export type ViewerPermissionProfileStructureArgs = {
  externalOnly?: InputMaybe<Scalars['Boolean']['input']>;
};


export type ViewerPriceCalculationArgs = {
  basePriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  billingPromoterId?: InputMaybe<Scalars['ID']['input']>;
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  eventId?: InputMaybe<Scalars['ID']['input']>;
  faceValue: Scalars['Int']['input'];
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  forcePwlActive?: InputMaybe<Scalars['Boolean']['input']>;
  ignorePwlFee?: InputMaybe<Scalars['Boolean']['input']>;
  postFanPriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  venueIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
};


export type ViewerProductPriceCalculationArgs = {
  basePriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  billingPromoterId?: InputMaybe<Scalars['ID']['input']>;
  categoryId: Scalars['ID']['input'];
  disableUsTax?: InputMaybe<Scalars['Boolean']['input']>;
  eventId?: InputMaybe<Scalars['ID']['input']>;
  faceValue: Scalars['Int']['input'];
  fees?: InputMaybe<Array<InputMaybe<FeeInput>>>;
  postFanPriceFees?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  venueId?: InputMaybe<Scalars['ID']['input']>;
};


export type ViewerProductsRevenueReportArgs = {
  currency?: InputMaybe<EventCostCurrency>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventId?: InputMaybe<Scalars['ID']['input']>;
  productId?: InputMaybe<Scalars['ID']['input']>;
  rootType?: InputMaybe<ProductRootType>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
};


export type ViewerPromoterFansDataExportTokenArgs = {
  accountId?: InputMaybe<Scalars['ID']['input']>;
};


export type ViewerPromotersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<PromotersOrder>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  where?: InputMaybe<PromoterWhereInput>;
};


export type ViewerRecurrentEventsScheduleArgs = {
  announceDate: Scalars['Time']['input'];
  date: Scalars['Time']['input'];
  endDate: Scalars['Time']['input'];
  frequency: RepeatFrequency;
  occurrences?: InputMaybe<Scalars['Int']['input']>;
  offSaleDate: Scalars['Time']['input'];
  onSaleDate: Scalars['Time']['input'];
  repeatEnds: RepeatEnds;
  repeatOn?: InputMaybe<RepeatOn>;
  timezoneName?: InputMaybe<Scalars['String']['input']>;
  until?: InputMaybe<Scalars['Time']['input']>;
};


export type ViewerReportsAndSchedulesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filters?: InputMaybe<ReportsAndSchedulesFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


export type ViewerRevenueReportArgs = {
  currency?: InputMaybe<EventCostCurrency>;
  endDate?: InputMaybe<Scalars['Time']['input']>;
  eventId?: InputMaybe<Scalars['ID']['input']>;
  startDate?: InputMaybe<Scalars['Time']['input']>;
  ticketTypeId?: InputMaybe<Scalars['ID']['input']>;
};


export type ViewerSalesReportExportTokenArgs = {
  accountIds?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  endDate: Scalars['Time']['input'];
  groupBy: SalesReportGrouping;
  startDate: Scalars['Time']['input'];
};


export type ViewerSalesTaxCalculationArgs = {
  amount: Scalars['Int']['input'];
  eventId: Scalars['ID']['input'];
  venueId: Scalars['ID']['input'];
};


export type ViewerSearchAttractiveTicketsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  barcode?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  cardNumber?: InputMaybe<Scalars['String']['input']>;
  emissionId?: InputMaybe<Scalars['Int']['input']>;
  eventDate?: InputMaybe<Scalars['Time']['input']>;
  eventName?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  fiscalDateMax?: InputMaybe<Scalars['Time']['input']>;
  fiscalDateMin?: InputMaybe<Scalars['Time']['input']>;
  fiscalSeal?: InputMaybe<Scalars['String']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  progressiveNumber?: InputMaybe<Scalars['String']['input']>;
};


export type ViewerStatusBreakdownArgs = {
  eventId: Scalars['ID']['input'];
};


export type ViewerStatusBreakdownPerTtyArgs = {
  eventId: Scalars['ID']['input'];
};


export type ViewerStripeOperationsArgs = {
  q: Scalars['String']['input'];
};


export type ViewerSwitchAccountArgs = {
  accountId: Scalars['ID']['input'];
};


export type ViewerTagsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  orderBy?: InputMaybe<Array<InputMaybe<TagOrder>>>;
  where?: InputMaybe<TagWhereInput>;
};


export type ViewerThirdPartySettingsArgs = {
  promoterId: Scalars['ID']['input'];
};


export type ViewerTicketCountSalesAttributionArgs = {
  from: Scalars['Date']['input'];
  promoterId: Scalars['ID']['input'];
  to: Scalars['Date']['input'];
};


export type ViewerTopEventsArgs = {
  limit: Scalars['Int']['input'];
  orderBy: TopEventsOrder;
};


export type ViewerTotalScanPerTimeframeArgs = {
  eventId: Scalars['ID']['input'];
  fromDate: Scalars['Time']['input'];
  timeframe: Timeframe;
  toDate: Scalars['Time']['input'];
};


export type ViewerTotalScanPerUserArgs = {
  eventId: Scalars['ID']['input'];
};


export type ViewerUsersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<UsersWhereInput>;
};


export type ViewerVenuesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  where?: InputMaybe<VenueWhereInput>;
};

export type WaitingListEntry = Node & {
  __typename?: 'WaitingListEntry';
  amount: Scalars['Int']['output'];
  expiresAt?: Maybe<Scalars['Time']['output']>;
  fan?: Maybe<Fan>;
  /** The ID of an object */
  id: Scalars['ID']['output'];
  joinedAt?: Maybe<Scalars['Time']['output']>;
  price: Scalars['Int']['output'];
  reservedAt?: Maybe<Scalars['Time']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  ticketType?: Maybe<TicketType>;
};

export type WaitingListExchangeWindow = {
  __typename?: 'WaitingListExchangeWindow';
  duration: Scalars['Int']['output'];
  id?: Maybe<Scalars['ID']['output']>;
  offset?: Maybe<Scalars['Int']['output']>;
};

export type WaitingListExchangeWindowInput = {
  duration: Scalars['Int']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};

export type GetEventPromotionsQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetEventPromotionsQuery = { __typename?: 'RootQueryType', viewer?: { __typename?: 'Viewer', events?: { __typename?: 'EventConnection', edges?: Array<{ __typename?: 'EventEdge', node?: { __typename?: 'Event', eventPromotions?: Array<{ __typename?: 'EventPromotion', accessType: PromotionAccessType, code?: string | null, endDate?: any | null, fanFacingName?: string | null, id: string, isEnded: boolean, maxRedemptions?: number | null, name: string, promotionType: PromotionType, startDate?: any | null, codeLocks?: { __typename?: 'CodeLockConnection', edges?: Array<{ __typename?: 'CodeLockEdge', node?: { __typename?: 'CodeLock', code: string, createdUses?: number | null, disabled?: boolean | null, id: string, link?: string | null, status: CodeLockStatus, unlimited?: boolean | null, usageStatus: CodeLockUsageStatus, uses?: number | null, validFrom?: any | null, validTo?: any | null } | null } | null> | null } | null } | null> | null } | null } | null> | null } | null } | null };

export type GetEventTrackingLinksQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetEventTrackingLinksQuery = { __typename?: 'RootQueryType', viewer?: { __typename?: 'Viewer', events?: { __typename?: 'EventConnection', edges?: Array<{ __typename?: 'EventEdge', node?: { __typename?: 'Event', marketingLinks?: Array<{ __typename?: 'SocialLink', campaign: string, channel: string, customUrl?: string | null, deelsParams?: string | null, diceLink?: boolean | null, id?: string | null, insertedAt: any, postType: SocialLinkPostType, url?: string | null } | null> | null } | null } | null> | null } | null } | null };

export type GetEventWithTicketTypesQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GetEventWithTicketTypesQuery = { __typename?: 'RootQueryType', viewer?: { __typename?: 'Viewer', id: string, events?: { __typename?: 'EventConnection', edges?: Array<{ __typename?: 'EventEdge', node?: { __typename?: 'Event', ticketType?: string | null, ticketTypes?: Array<{ __typename?: 'TicketType', allocation: number, archived?: boolean | null, description?: string | null, id: string, name?: string | null, priceTiers?: Array<{ __typename?: 'PriceTier', id: string, name?: string | null, priceBreakdown?: { __typename?: 'Price', faceValue: number, fees: number, total: number, breakdown: Array<{ __typename?: 'FeeOutput', active?: boolean | null, amount?: number | null, applicable?: boolean | null, computed: number, type: FeeType, unit: FeeUnit } | null> } | null } | null> | null } | null> | null } | null } | null> | null } | null } | null };


export const GetEventPromotionsDocument = gql`
    query getEventPromotions($id: ID!) {
  viewer {
    events(first: 1, where: {id: {eq: $id}}) {
      edges {
        node {
          eventPromotions {
            accessType
            code
            endDate
            fanFacingName
            id
            isEnded
            maxRedemptions
            name
            promotionType
            startDate
            codeLocks(first: 10000) {
              edges {
                node {
                  code
                  createdUses
                  disabled
                  id
                  link
                  status
                  unlimited
                  usageStatus
                  uses
                  validFrom
                  validTo
                }
              }
            }
          }
        }
      }
    }
  }
}
    `;
export const GetEventTrackingLinksDocument = gql`
    query getEventTrackingLinks($id: ID!) {
  viewer {
    events(first: 1, where: {id: {eq: $id}}) {
      edges {
        node {
          marketingLinks {
            campaign
            channel
            customUrl
            deelsParams
            diceLink
            id
            insertedAt
            postType
            url
          }
        }
      }
    }
  }
}
    `;
export const GetEventWithTicketTypesDocument = gql`
    query getEventWithTicketTypes($id: String!) {
  viewer {
    events(first: 1, where: {eventIdLive: {eq: $id}}) {
      edges {
        node {
          ticketType
          ticketTypes {
            allocation
            archived
            description
            id
            name
            priceTiers {
              id
              name
              priceBreakdown {
                faceValue
                fees
                total
                breakdown {
                  active
                  amount
                  applicable
                  computed
                  type
                  unit
                }
              }
            }
          }
        }
      }
    }
    id
  }
}
    `;

export type SdkFunctionWrapper = <T>(action: (requestHeaders?:Record<string, string>) => Promise<T>, operationName: string, operationType?: string, variables?: any) => Promise<T>;


const defaultWrapper: SdkFunctionWrapper = (action, _operationName, _operationType, _variables) => action();

export function getSdk(client: GraphQLClient, withWrapper: SdkFunctionWrapper = defaultWrapper) {
  return {
    getEventPromotions(variables: GetEventPromotionsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetEventPromotionsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetEventPromotionsQuery>(GetEventPromotionsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getEventPromotions', 'query', variables);
    },
    getEventTrackingLinks(variables: GetEventTrackingLinksQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetEventTrackingLinksQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetEventTrackingLinksQuery>(GetEventTrackingLinksDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getEventTrackingLinks', 'query', variables);
    },
    getEventWithTicketTypes(variables: GetEventWithTicketTypesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetEventWithTicketTypesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetEventWithTicketTypesQuery>(GetEventWithTicketTypesDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getEventWithTicketTypes', 'query', variables);
    }
  };
}
export type Sdk = ReturnType<typeof getSdk>;