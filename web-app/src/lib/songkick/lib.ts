import * as cheerio from 'cheerio'
import { indexObject } from '@/lib/sync/indexing'
import { Artist, Event, EventOrganizer, Venue } from '@/payload-types'
import { MusicEvent, transformArtist, transformEvent, transformEventOrganizer, transformVenue } from '@/lib/songkick/transformers'
import { PayloadRequest } from 'payload'

export function extractLdJson(
  html: string,
  skipCancelled: boolean = true
): any[] {
  const $ = cheerio.load(html)
  const result: any[] = []

  $('script[type="application/ld+json"]').each((_, el) => {
    const jsonText = $(el).html()
    if (!jsonText) return

    try {
      const parsed = JSON.parse(jsonText.trim())

      const entries = Array.isArray(parsed) ? parsed : [parsed]

      for (const entry of entries) {
        if (skipCancelled && entry.eventStatus === 'https://schema.org/EventCancelled') {
          continue
        }
        result.push(entry)
      }

    } catch (err) {
      console.warn('Не удалось распарсить JSON-LD:', err)
    }
  })

  return result
}

async function handleVenue(venue: Omit<Venue, 'id' | 'createdAt' | 'updatedAt'>, req: PayloadRequest) {
  const { payload } = req
  return await indexObject('venues', venue, payload)
}

async function handleEventOrganizer(eventOrganizer: Omit<EventOrganizer, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>, req: PayloadRequest) {
  const { payload } = req
  return await indexObject('eventOrganizers', eventOrganizer, payload)
}

async function handleArtist(artist: Omit<Artist, "sizes" | "createdAt" | "updatedAt" | "id">, req: PayloadRequest) {
  const { payload } = req
  return await indexObject('artists', artist, payload)
}

async function handleEvent(event: Omit<Event, "sizes" | "createdAt" | "updatedAt" | "id">, req: PayloadRequest) {
  const { payload } = req
  return await indexObject('events', event, payload)
}

export async function handleSongkickEvent(
  songkickEvent: MusicEvent,
  req: PayloadRequest
): Promise<Event> {

  const venue = await handleVenue(transformVenue(songkickEvent), req) as Venue
  // const eventOrganizer = await handleEventOrganizer(transformEventOrganizer(songkickEvent.organizer), req)
  const artists = []
  for (const performer of songkickEvent.performer) {
    const artist = await handleArtist(transformArtist(performer), req)
    artists.push(artist)
  }

  const event = transformEvent(songkickEvent)
  event.Location = {
    locationType: 'venue',
    venue: venue?.id,
  }
  // event.eventOrganizer = eventOrganizer.id
  event.lineup = artists
    .filter((x): x is Artist => !!x && !!x.id)
    .map(x => ({ artist: x.id }));

  const upsertEvent = await handleEvent(event, req) as Event
  return upsertEvent
}