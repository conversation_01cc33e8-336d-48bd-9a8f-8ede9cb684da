import { formatSlug } from '@/fields/slug/formatSlug'
import { Artist, Event, EventOrganizer, Media, Venue } from '@/payload-types'
import { createLexicalState } from '../sync/lib'

interface Overview {
  overview?: {
    hero?: (number | null) | Media
    content?: {
      root: {
        type: string
        children: {
          type: string
          version: number
        }[]
        direction: ('ltr' | 'rtl') | null
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
        indent: number
        version: number
      }
    } | null
  }
}

export interface GeoCoordinates {
  '@type': 'GeoCoordinates'
  latitude: number
  longitude: number
}

export interface PostalAddress {
  '@type': 'PostalAddress'
  addressLocality: string
  addressCountry: string
  addressRegion: string
  streetAddress: string
  postalCode: string
}

export interface Place {
  '@type': 'Place'
  address: PostalAddress
  name: string
  sameAs: string
  geo: GeoCoordinates
}

export interface MusicGroup {
  '@type': 'MusicGroup'
  name: string
  genre: string[]
  sameAs: string
}

export interface Organization {
  '@type': 'Organization'
  name: string
  url: string
}

export interface AggregateOffer {
  '@type': 'AggregateOffer'
  url: string
  availability: string
  validFrom: string
  highPrice: number
  lowPrice: number
  priceCurrency: string
  offerCount: number
}

export interface MusicEvent {
  '@context': string
  '@type': 'MusicEvent'
  name: string
  url: string
  image: string
  eventAttendanceMode: string
  location: Place
  eventStatus: string
  startDate: string
  endDate: string
  performer: MusicGroup[]
  description: string
  organizer: Organization
  offers: AggregateOffer[]
}

function normalizeToIso(input: string): string {
  if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
    return `${input}T00:00:00.000Z`
  }

  if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/.test(input)) {
    return `${input}.000Z`
  }

  return input
}

export const transformVenue = (
  doc: MusicEvent,
): Omit<Venue, 'sizes' | 'createdAt' | 'updatedAt' | 'id'> => {
  return {
    origin: 'songkick',
    name: doc.location.name,
    address: doc.location.address.streetAddress ?? 'No address provided',
    city: doc.location.address.addressLocality,
    country: doc.location.address.addressCountry,
    coordinates: [doc.location?.geo?.latitude, doc.location?.geo?.longitude],
    timezone: `${doc.location.address.addressCountry}/${doc.location.address.addressLocality}`,
    slug: formatSlug(doc.name),
  } satisfies Omit<Venue, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

export const transformEventOrganizer = (
  doc: Organization,
): Omit<EventOrganizer, 'sizes' | 'createdAt' | 'updatedAt' | 'id'> => {
  return {
    slug: formatSlug(doc.name),
    name: doc.name,
  } satisfies Omit<EventOrganizer, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

export const transformArtist = (
  doc: MusicGroup,
): Omit<Artist, 'sizes' | 'createdAt' | 'updatedAt' | 'id'> => {
  return {
    name: doc.name,
    slug: formatSlug(doc.name),
    spotifyId: ''
  } satisfies Omit<Artist, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

export const transformEvent = (
  doc: MusicEvent,
): Omit<Event, 'sizes' | 'createdAt' | 'updatedAt' | 'id'> => {
  const overview: Overview = { overview: {} }
  if (doc.description) {
    overview.overview!.content = createLexicalState(doc.description)
  }

  return {
    name: doc.name,
    slug: formatSlug(doc.name),
    startDate: normalizeToIso(doc.startDate),
    endDate: doc.endDate,
    overview,
    ticketUrls: doc.offers.map((x) => x.url),
  } satisfies Omit<Event, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}
