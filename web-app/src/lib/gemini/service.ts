import { GoogleGenAI, GenerateContentResponse } from "@google/genai";

export class GeminiService {
  private ai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY!;
    if (!apiKey) {
      throw new Error("Missing GEMINI_API_KEY");
    }
    this.ai = new GoogleGenAI({ apiKey });
  }

  async generate(
    prompt: string | string[],
    withSearch = true
  ): Promise<GenerateContentResponse> {
    const contents = Array.isArray(prompt) ? prompt : [prompt];

    return this.ai.models.generateContent({
      model: "gemini-2.5-flash-preview-05-20",
      contents,
      config: {
        tools: withSearch ? [{ googleSearch: {} }] : [],
      },
    });
  }
}
