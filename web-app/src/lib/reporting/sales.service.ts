import type { Payload } from 'payload'

export interface TicketSalesData {
  ticketTypeName: string
  ticketsSold: number
  faceValue: number
  totalGrossValue: number
}

export interface EventSalesData {
  eventId: number
  eventName: string
  eventDate: string
  artistName: string
  venueName: string
  ticketSales: TicketSalesData[]
  totalTicketsSold: number
  totalGrossRevenue: number
}

export interface RecipientData {
  recipientId: number
  recipientName: string
  recipientEmail: string
  recipientType: 'agent' | 'manager'
  events: EventSalesData[]
}

export class SalesService {
  constructor(private payload: Payload) {}

  private getActiveEventsWhereClause(daysAhead?: number): any {
    const now = new Date()
    const futureDate = daysAhead ? new Date(now.getTime() + daysAhead * 24 * 60 * 60 * 1000) : null

    const whereClause: any = {
      and: [
        { startDate: { gte: now.toISOString() } },
        { saleOnDate: { lte: now.toISOString() } },
        {
          or: [{ saleOffDate: { exists: false } }, { saleOffDate: { gte: now.toISOString() } }],
        },
      ],
    }

    if (futureDate) {
      whereClause.and.push({ startDate: { lte: futureDate.toISOString() } })
    }
    return whereClause
  }

  async getEventSalesData(eventId: number): Promise<EventSalesData | null> {
    try {
      const event = await this.payload.findByID({
        collection: 'events',
        id: eventId,
        depth: 2,
      })

      if (!event) return null

      const { docs: orders } = await this.payload.find({
        collection: 'orders',
        where: {
          event: { equals: eventId },
        },
        limit: 10000,
        depth: 1,
      })

      const ticketIds = orders.flatMap((order) =>
        (order.tickets as any[]).map((ticket) => (typeof ticket === 'object' ? ticket.id : ticket)),
      )

      if (ticketIds.length === 0) {
        const headliningArtist = (event as any).lineup?.[0]?.artist
        const artistName = headliningArtist?.name || 'Unknown Artist'
        const venueName =
          typeof (event as any).Location?.venue === 'object'
            ? (event as any).Location.venue.name
            : 'Unknown Venue'

        return {
          eventId: event.id,
          eventName: (event as any).name,
          eventDate: new Date((event as any).startDate).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          }),
          artistName,
          venueName,
          ticketSales: [],
          totalTicketsSold: 0,
          totalGrossRevenue: 0,
        }
      }

      const { docs: allTicketsForEvent } = await this.payload.find({
        collection: 'tickets',
        where: {
          id: { in: ticketIds },
        },
        limit: ticketIds.length,
        depth: 1,
      })

      const ticketsByTpe: { [key: string]: any[] } = {}
      for (const ticket of allTicketsForEvent) {
        const ticketTypeId =
          typeof (ticket as any).ticketType === 'object'
            ? (ticket as any).ticketType.id
            : (ticket as any).ticketType
        if (!ticketsByTpe[ticketTypeId]) {
          ticketsByTpe[ticketTypeId] = []
        }
        ticketsByTpe[ticketTypeId].push(ticket)
      }

      const ticketSalesData: TicketSalesData[] = []
      let totalTicketsSold = 0
      let totalGrossRevenue = 0

      for (const ticketTypeId in ticketsByTpe) {
        const ticketsInGroup = ticketsByTpe[ticketTypeId]
        if (!ticketsInGroup || ticketsInGroup.length === 0) {
          continue
        }

        const firstTicket = ticketsInGroup[0]
        if (!firstTicket) {
          continue
        }

        const ticketType =
          typeof firstTicket.ticketType === 'object'
            ? firstTicket.ticketType
            : await this.payload.findByID({
                collection: 'ticketTypes',
                id: ticketTypeId,
                depth: 0,
              })

        if (!ticketType || ticketType.archived || ticketType.internalPresentationOnly) {
          continue
        }

        const ticketsSold = ticketsInGroup.length
        const faceValue = ticketType.faceValue || 0
        const totalGrossValue = ticketsSold * faceValue

        ticketSalesData.push({
          ticketTypeName: ticketType.ticketTypeName,
          ticketsSold,
          faceValue,
          totalGrossValue,
        })

        totalTicketsSold += ticketsSold
        totalGrossRevenue += totalGrossValue
      }

      const headliningArtist = (event as any).lineup?.[0]?.artist
      const artistName = headliningArtist?.name || 'Unknown Artist'
      const venueName =
        typeof (event as any).Location?.venue === 'object'
          ? (event as any).Location.venue.name
          : 'Unknown Venue'

      return {
        eventId: event.id,
        eventName: (event as any).name,
        eventDate: new Date((event as any).startDate).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        artistName,
        venueName,
        ticketSales: ticketSalesData,
        totalTicketsSold,
        totalGrossRevenue,
      }
    } catch (error) {
      console.error('Error getting event sales data:', error)
      return null
    }
  }

  async getRecipientsWithReporting(): Promise<RecipientData[]> {
    const recipients: RecipientData[] = []

    const { docs: agents } = await this.payload.find({
      collection: 'agents',
      where: { automatedReportingEnabled: { equals: true } },
      depth: 1,
      limit: 1000,
    })

    const { docs: managers } = await this.payload.find({
      collection: 'managers',
      where: { automatedReportingEnabled: { equals: true } },
      depth: 1,
      limit: 1000,
    })

    for (const agent of agents) {
      recipients.push({
        recipientId: agent.id,
        recipientName: (agent as any).name,
        recipientEmail: (agent as any).generalContacInfo.email,
        recipientType: 'agent',
        events: [],
      })
    }

    for (const manager of managers) {
      recipients.push({
        recipientId: manager.id,
        recipientName: (manager as any).name,
        recipientEmail: (manager as any).generalContacInfo.email,
        recipientType: 'manager',
        events: [],
      })
    }

    return recipients
  }

  async getEventsForRecipient(
    recipientId: number,
    recipientType: 'agent' | 'manager',
    daysAhead?: number,
  ): Promise<EventSalesData[]> {
    const whereClause = this.getActiveEventsWhereClause(daysAhead)

    const representationField =
      recipientType === 'agent'
        ? 'lineup.artist.representation.agent'
        : 'lineup.artist.representation.manager'

    whereClause.and.push({ [representationField]: { equals: recipientId } })

    const { docs: events } = await this.payload.find({
      collection: 'events',
      where: whereClause,
      depth: 2,
      limit: 1000,
    })

    const recipientEvents: EventSalesData[] = []
    for (const event of events) {
      const salesData = await this.getEventSalesData(event.id)
      if (salesData) {
        recipientEvents.push(salesData)
      }
    }

    return recipientEvents
  }
}
