query getEvent($id: ID!) {
  node(id: $id) {
        ... on Event {
            currency
            description
            endDatetime
            eventIdLive
            genreTypes
            genres
            hidden
            id
            name
            promoters
            startDatetime
            state
            totalTicketAllocationQty
            updatedAt
            url
            artists {
                name
            }
            images {
                type
                url
            }
            socialLinks {
                campaign
                default
                url
            }
            venues {
                ageLimit
                city
                country
                displayedAddress
                latitude
                longitude
                name
                postOfficeBoxNumber
                postalCode
                region
                state
                streetAddress
                timezoneName
                type
            }
        }
  }
}
