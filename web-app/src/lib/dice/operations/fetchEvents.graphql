query fetchEvents($first: Int!, $after: String, $stateFilter: EventState) {
  viewer {
    events(first: $first, after: $after, where: { state: { eq: $stateFilter } }) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
          node {
                    eventIdLive
                    currency
                    description
                    endDatetime
                    eventIdLive
                    genreTypes
                    genres
                    hidden
                    id
                    name
                    promoters
                    startDatetime
                    state
                    totalTicketAllocationQty
                    updatedAt
                    url
                    artists {
                        name
                    }
                    images {
                        type
                        url
                    }
                    socialLinks {
                        campaign
                        default
                        url
                    }
                    venues {
                        ageLimit
                        city
                        country
                        displayedAddress
                        latitude
                        longitude
                        name
                        postOfficeBoxNumber
                        postalCode
                        region
                        state
                        streetAddress
                        timezoneName
                        type
                    }
                }
      }
    }
  }
}
