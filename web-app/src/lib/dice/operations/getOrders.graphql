# src/lib/dice/operations/getOrders.graphql
query getOrders($first: Int!, $where: OrderWhereInput, $after: String) {
  viewer {
    orders(first: $first, where: $where, after: $after) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          purchasedAt
          total
          commission
          diceCommission
          quantity
          ipCity
          ipCountry
          fan {
            email
          }
          tickets { 
            id
            code
            fullPrice
            priceTier {
                allocation
                doorSalesPrice
                faceValue
                id
                name
                price
                time
                        }
            ticketType {
                archived
                description
                doorSalesPrice
                externalSkus
                faceValue
                id
                name
                price
                priceTierType
                totalTicketAllocationQty
          }
          }
          event {
            id
            eventIdLive
          }
        }
      }
    }
  }
}
