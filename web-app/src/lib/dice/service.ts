// lib/dice/service.ts
import { GraphQLClient } from 'graphql-request';
import { getSdk, Sdk } from './graphql-types'; 
import {
  Event as DiceEvent,
  Order as DiceOrder,
  EventState,
  Scalars
} from './schema-types';

export default class DiceService {
  private sdk: Sdk;
  public fetchEventsPage: Sdk['fetchEvents'];
  constructor(
    token: string,
    endpoint: string = 'https://partners-endpoint.dice.fm/graphql'
  ) {
    const client = new GraphQLClient(endpoint, {
      headers: { Authorization: `Bearer ${token}` }
    });
    this.sdk = getSdk(client);
    this.fetchEventsPage = this.sdk.fetchEvents.bind(this.sdk);

  }

  public async searchEventsByName(
    name: string,
    state: EventState = EventState.Approved
  ): Promise<DiceEvent[]> {
    const matches: DiceEvent[] = [];
    const term = name.toLowerCase();
    let after: string | undefined;

    do {
      const res = await this.sdk.fetchEvents({
        first: 10,
        after,
        stateFilter: state
      });
      const conn = res.viewer?.events;
      if (!conn) break;
      conn.edges?.forEach(e => {
        const ev = e?.node;
        if (ev && ev.name?.toLowerCase().includes(term)) {
          matches.push(ev);
        }
      });
      after = conn.pageInfo.hasNextPage ? conn.pageInfo.endCursor ?? undefined : undefined;
    } while (after);

    return matches;
  }

  public async getEvent(id: string): Promise<DiceEvent | null> {
    const { node } = await this.sdk.getEvent({ id });
    if (!node || node.__typename !== 'Event') {
      return null;
    }
    return node;
  }

  public async getOrderById(orderId: string): Promise<DiceOrder | null> {
    const { node } = await this.sdk.getOrderById({ orderId });
    return node?.__typename === 'Order' ? node : null;
  }

  public async getPaginatedOrders(params: {
    take: number;
    after?: string;
    purchasedAfter?: Scalars['Datetime'];
    eventId?: string;
  }) {
    const { viewer } = await this.sdk.getPaginatedOrders({
      take: params.take,
      after: params.after,
      purchasedAfter: params.purchasedAfter,
      eventId: params.eventId
    });
    return viewer?.orders ?? null;
  }

  public async getPaginatedEvents(params: {
    take: number;
    after?: string;
    state?: EventState;
  }) {
    const { viewer } = await this.sdk.getPaginatedEvents({
      take: params.take,
      after: params.after,
      state: params.state
    });
    return viewer?.events ?? null;
  }

  public async getOrders(params: {
    first?: number;
    after?: string;
    updatedSince?: Scalars['Datetime'];
  }) {
    const { viewer } = await this.sdk.getOrders({
      first: params.first ?? 50,
      after: params.after,
      where: params.updatedSince
        ? { purchasedAt: { gte: params.updatedSince } }
        : undefined
    });
    const conn = viewer?.orders;
    return {
      pageInfo: conn?.pageInfo ?? { hasNextPage: false },
      orders: (conn?.edges ?? []).map(e => e?.node as DiceOrder).filter(Boolean)
    };
  }
  
}
