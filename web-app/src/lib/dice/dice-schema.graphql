schema {
  query: RootQueryType
  mutation: RootMutationType
}

type Address {
  """Address Country Code"""
  countryCode: String

  """Address Country"""
  county: String

  """Address First Name"""
  firstName: String

  """Address Last Name"""
  lastName: String

  """Address Line 1"""
  line1: String

  """Address Line 2"""
  line2: String

  """Address Post Code"""
  postCode: String

  """Address Town"""
  town: String
}

type Adjustment {
  feesChange: [TicketFee]
  processedAt: Datetime
  reason: String
  ticket: Ticket
}

type Artist {
  """Name"""
  name: String
}

input ClaimTicketsInput {
  fanId: ID
  fanSecureToken: String
  ticketIds: [ID]!
}

type ClaimTicketsPayload {
  """
  A list of failed validations. May be blank or null if mutation succeeded.
  """
  messages: [ValidationMessage]

  """
  The object created/updated/deleted by the mutation. May be null if mutation failed.
  """
  result: [Ticket]

  """Indicates if the mutation completed successfully or not. """
  successful: Boolean!
}

"""ISO 8601 Date and time - YYYY-MM-DDThh:mm:ss"""
scalar Datetime

input EqBooleanInput {
  """Equal to"""
  eq: Boolean
}

input EqStringInput {
  """Equal to"""
  eq: String
}

type Event implements Node {
  """List of artists participating in the event"""
  artists: [Artist]

  """
  Cost currency. See EventCostCurrency enum for the list of available values
  """
  currency: EventCostCurrency

  """Description"""
  description: String

  """End date and time"""
  endDatetime: Datetime

  """Event id. Field is available with restricted access"""
  eventIdLive: String

  """Paginated list of sold extras"""
  extras(after: String, before: String, first: Int, last: Int, where: ExtraWhereInput): ExtraConnection

  """List of related genre types"""
  genreTypes: [String]

  """List of related genres"""
  genres: [String]

  """Is the event hidden?"""
  hidden: Boolean

  """The ID of an object"""
  id: ID!

  """List of images"""
  images: [Image]

  """Name. Field is available with restricted access"""
  name: String

  """List of products available for purchasing"""
  products: [Product]

  """List of promoters with access to event in MIO"""
  promoters: [String]
  socialLinks: [SocialLink]

  """Start date and time. Field is available with restricted access"""
  startDatetime: Datetime

  """State. See EventState enum for the list of available values"""
  state: EventState

  """List of tickets types available for purchasing"""
  ticketTypes: [TicketType]

  """Paginated list of sold tickets"""
  tickets(after: String, before: String, first: Int, last: Int): TicketConnection

  """Total ticket allocation quantity"""
  totalTicketAllocationQty: Int

  """Last updated at date and time"""
  updatedAt: Datetime

  """Fan facing url for the event"""
  url: String

  """List of venues"""
  venues: [Venue]
}

type EventConnection {
  edges: [EventEdge]
  pageInfo: PageInfo!
  totalCount: Int
}

enum EventCostCurrency {
  AED
  AFN
  ALL
  AMD
  AOA
  ARS
  AUD
  AWG
  AZN
  BAM
  BBD
  BDT
  BGN
  BHD
  BIF
  BMD
  BND
  BOB
  BRL
  BWP
  BYR
  BZD
  CAD
  CDF
  CHF
  CLP
  CNY
  COP
  CRC
  CVE
  CZK
  DJF
  DKK
  DOP
  DZD
  EGP
  ERN
  ETB
  EUR
  GBP
  GEL
  GHS
  GNF
  GTQ
  GYD
  HKD
  HNL
  HRK
  HUF
  IDR
  ILS
  INR
  IQD
  IRR
  ISK
  JMD
  JOD
  JPY
  KES
  KHR
  KMF
  KRW
  KWD
  KZT
  LBP
  LKR
  LRD
  LTL
  LVL
  LYD
  MAD
  MDL
  MGA
  MKD
  MMK
  MOP
  MUR
  MXN
  MYR
  MZN
  NAD
  NGN
  NIO
  NOK
  NPR
  NZD
  OMR
  PAB
  PEN
  PHP
  PKR
  PLN
  PYG
  QAR
  RON
  RSD
  RUB
  RWF
  SAR
  SDG
  SEK
  SGD
  SOS
  STD
  SYP
  THB
  TND
  TOP
  TRY
  TTD
  TWD
  TZS
  UAH
  UGX
  USD
  UYU
  UZS
  VEF
  VND
  XAF
  XOF
  YER
  ZAR
  ZMK
}

type EventEdge {
  cursor: String
  node: Event
}

enum EventImageType {
  BRAND
  LANDSCAPE
  PORTRAIT
  SQUARE
}

enum EventState {
  APPROVED
  ARCHIVED
  CANCELLED
  DECLINED
  DRAFT
  REVIEW
  SUBMITTED
}

input EventWhereInput {
  endDatetime: OperatorsDateInput
  genre: OperatorsIdInput
  id: OperatorsIdInput
  startDatetime: OperatorsDateInput
  state: OperatorsEventStateInput
  updatedAt: OperatorsDateInput
}

type Extra implements Node {
  """QR code. Field is available with restricted access"""
  code: String

  """Partner commission in cents"""
  commission: Int

  """DICE commission in cents"""
  diceCommission: Int

  """Fees breakdown by category"""
  fees: [TicketFee]

  """Price without commissions in cents"""
  fullPrice: Int

  """
  Flag that extra has separate barcode. Field is available with restricted access
  """
  hasSeparateAccessBarcode: Boolean

  """Ticket holder. Field is available with restricted access"""
  holder: Fan

  """The ID of an object"""
  id: ID!

  """Product. Field is available with restricted access"""
  product: Product

  """
  Ticket which the extra is linked to. Field is available with restricted access
  """
  ticket: Ticket

  """
  Total price with commissions in cents. Field is available with restricted access
  """
  total: Int

  """Variant. Field is available with restricted access"""
  variant: Variant
}

type ExtraConnection {
  edges: [ExtraEdge]
  pageInfo: PageInfo!
  totalCount: Int
}

type ExtraEdge {
  cursor: String
  node: Extra
}

input ExtraWhereInput {
  eventId: OperatorsIdInput
  hasSeparateAccessBarcode: EqBooleanInput
  id: OperatorsIdInput
  productId: OperatorsIdInput
}

type Fan {
  """Day of birth"""
  dob: String

  """Email"""
  email: String

  """First name"""
  firstName: String

  """Unique fan ID"""
  id: ID

  """Last name"""
  lastName: String

  """Opt-in flag"""
  optInPartners: Boolean

  """Phone number"""
  phoneNumber: String
}

type FanSurveyAnswer {
  """Related fan survey question"""
  fanSurveyQuestion: FanSurveyQuestion

  """Fan survey answer"""
  value: String
}

type FanSurveyQuestion {
  """Fan survey question description"""
  title: String
}

type Genre implements Node {
  """The ID of an object"""
  id: ID!

  """Name"""
  name: String!
}

type GenreConnection {
  edges: [GenreEdge]
  pageInfo: PageInfo!
  totalCount: Int
}

type GenreEdge {
  cursor: String
  node: Genre
}

type GenreType implements Node {
  """Child Genres"""
  genres(after: String, before: String, first: Int, last: Int): GenreConnection

  """The ID of an object"""
  id: ID!

  """Name"""
  name: String!
}

input GenreTypeWhereInput {
  name: OperatorsStringInput
}

type GenreTypesConnection {
  edges: [GenreTypesEdge]
  pageInfo: PageInfo!
  totalCount: Int
}

type GenreTypesEdge {
  cursor: String
  node: GenreType
}

type Image {
  """
  Type of the image. See EventImageType enum for the list of avaliable values
  """
  type: EventImageType

  """CDN url to image"""
  url: String!
}

type RootMutationType {
  claimTickets(input: ClaimTicketsInput!): ClaimTicketsPayload
}

interface Node {
  """The ID of the object."""
  id: ID!
}

input OperatorsDateInput {
  """Between the provided datetimes"""
  between: [Datetime]

  """Greater than"""
  gt: Datetime

  """Greater or equal to"""
  gte: Datetime

  """Less than"""
  lt: Datetime

  """Less or equal to"""
  lte: Datetime

  """Not between the provided datetimes"""
  notBetween: [Datetime]

  """Is null"""
  null: Boolean
}

input OperatorsEventStateInput {
  """Equal to"""
  eq: EventState

  """In the list of provided values"""
  in: [EventState]

  """Not equal to"""
  ne: EventState

  """Not in the list of provided values"""
  notIn: [EventState]
}

input OperatorsIdInput {
  """Equal to"""
  eq: ID

  """In the list of provided ids"""
  in: [ID]

  """Not equal to"""
  ne: ID

  """Not in the list of provided ids"""
  notIn: [ID]
}

input OperatorsStringInput {
  """Equal to"""
  eq: String

  """In the list of provided values"""
  in: [String]

  """Not equal to"""
  ne: String

  """Not in the list of provided values"""
  notIn: [String]
}

type Order implements Node {
  """Shipping/Fan Address"""
  address: Address

  """List of adjustments"""
  adjustments: [Adjustment]

  """Partner commission in cents"""
  commission: Int

  """DICE commission in cents"""
  diceCommission: Int

  """Related event"""
  event: Event

  """Order buyer"""
  fan: Fan

  """Fees breakdown by category"""
  fees: [TicketFee]

  """Price without commissions in cents"""
  fullPrice: Int

  """The ID of an object"""
  id: ID!

  """IP based city"""
  ipCity: String

  """IP based country"""
  ipCountry: String

  """
  Date and time order was purchased. Field is available with restricted access
  """
  purchasedAt: Datetime

  """
  Quantity of purchased tickets. Field is available with restricted access
  """
  quantity: Int

  """List of returns"""
  returns: [Return]

  """List of bought tickets"""
  tickets: [Ticket]

  """
  Total price with commissions in cents. Field is available with restricted access
  """
  total: Int
}

type OrderConnection {
  edges: [OrderEdge]
  pageInfo: PageInfo!
  totalCount: Int
}

type OrderEdge {
  cursor: String
  node: Order
}

input OrderWhereInput {
  eventId: OperatorsIdInput
  id: OperatorsIdInput
  purchasedAt: OperatorsDateInput
}

type PageInfo {
  """When paginating forwards, the cursor to continue."""
  endCursor: String

  """When paginating forwards, are there more items?"""
  hasNextPage: Boolean!

  """When paginating backwards, are there more items?"""
  hasPreviousPage: Boolean!

  """When paginating backwards, the cursor to continue."""
  startCursor: String
}

type PriceTier {
  allocation: Int

  """Door sale Price"""
  doorSalesPrice: Int
  faceValue: Int
  id: ID!
  name: String
  price: Int
  time: Datetime
}

enum PriceTierTypes {
  allocation
  time
}

type Product {
  """Archived flag. Archived products are not available for purchasing."""
  archived: Boolean

  """Description. Field is available with restricted access"""
  description: String

  """Face value"""
  faceValue: Int

  """Product unique id. Field is available with restricted access"""
  id: ID

  """Product name. Field is available with restricted access"""
  name: String

  """
  Ticket types linked to the product. Field is available with restricted access
  """
  ticketTypes: [TicketType]

  """Total product allocation quantity"""
  totalAllocationQty: Int
}

type RootQueryType {
  node(
    """The ID of an object."""
    id: ID!
  ): Node

  """"""
  viewer: Viewer
}

type Return implements Node {
  """The ID of an object"""
  id: ID!

  """Related order"""
  order: Order

  """Return reason"""
  reason: String

  """Returned at date and time"""
  returnedAt: Datetime

  """Related ticket"""
  ticket: Ticket

  """Related ticket id"""
  ticketId: ID!
}

type ReturnConnection {
  edges: [ReturnEdge]
  pageInfo: PageInfo!
  totalCount: Int
}

type ReturnEdge {
  cursor: String
  node: Return
}

input ReturnWhereInput {
  eventId: OperatorsIdInput
  id: OperatorsIdInput
  returnedAt: OperatorsDateInput
}

type Seat {
  """Seat Name. Field is available with restricted access"""
  name: String
}

type SocialLink {
  campaign: String!
  default: Boolean!

  """e.g. https://link.dice.fm/AE6xPxPd7jb"""
  url: String!
}

type Ticket implements Node {
  """Shipping/Fan Address"""
  address: Address

  """
  When this ticket has been claimed. Field is available with restricted access
  """
  claimedAt: Datetime

  """QR code. Field is available with restricted access """
  code: String

  """Partner commission in cents"""
  commission: Int

  """DICE commission in cents"""
  diceCommission: Int
  extras(where: TicketExtraWhereInput): [Extra]
  fanSurveyAnswers: [FanSurveyAnswer]

  """Fees breakdown by category"""
  fees: [TicketFee]

  """Price without commissions in cents"""
  fullPrice: Int

  """Ticket holder. Field is available with restricted access"""
  holder: Fan

  """The ID of an object"""
  id: ID!

  """Ticket price tier"""
  priceTier: PriceTier

  """Seat. Field is available with restricted access"""
  seat: Seat

  """Ticket type. Field is available with restricted access"""
  ticketType: TicketType

  """
  Total price with commissions in cents. Field is available with restricted access
  """
  total: Int
}

type TicketConnection {
  edges: [TicketEdge]
  pageInfo: PageInfo!
  totalCount: Int
}

type TicketEdge {
  cursor: String
  node: Ticket
}

input TicketExtraWhereInput {
  hasSeparateAccessBarcode: EqBooleanInput
  productId: OperatorsIdInput
}

type TicketFee {
  category: TicketFeeCategory
  dice: Int
  promoter: Int
}

enum TicketFeeCategory {
  ADDITIONAL_PROMOTER
  BOOKING
  BOX_OFFICE
  CHARITY_DONATION
  DEPOSIT
  EXTRA_CHARGE
  FACILITY
  FOOD_AND_BEVERAGE
  MEET_AND_GREET
  PAID_WAITING_LIST
  FULFILMENT
  PRESALE
  PROCESSING
  SALES_TAX
  TIER_DIFF
  VENDOR
  VENUE
  VENUE_LEVY
}

type TicketTransfer implements Node {
  """The ID of an object"""
  id: ID!

  """Related orders"""
  orders: [Order]

  """Related tickets"""
  tickets: [Ticket]

  """Transferred at date and time"""
  transferredAt: Datetime
}

type TicketTransferConnection {
  edges: [TicketTransferEdge]
  pageInfo: PageInfo!
  totalCount: Int
}

type TicketTransferEdge {
  cursor: String
  node: TicketTransfer
}

input TicketTransferWhereInput {
  eventId: OperatorsIdInput
  id: OperatorsIdInput
  transferredAt: OperatorsDateInput
}

type TicketType {
  """Archived flag. Archived ticket types are not available for purchasing."""
  archived: Boolean

  """Description. Field is available with restricted access"""
  description: String

  """Door sale Price"""
  doorSalesPrice: Int

  """External SKUs"""
  externalSkus: [String]

  """Face value"""
  faceValue: Int

  """Unique ID. Field is available with restricted access"""
  id: ID

  """Title. Field is available with restricted access"""
  name: String

  """Price"""
  price: Int

  """Type of price tiers. Can be either time or allocation if applicable"""
  priceTierType: PriceTierTypes

  """List of associated Price Tiers"""
  priceTiers: [PriceTier]

  """Total ticket allocation quantity"""
  totalTicketAllocationQty: Int
}

input TicketWhereInput {
  claimAllowed: EqBooleanInput
  eventId: OperatorsIdInput
  fanPhoneNumber: EqStringInput
  fanSecureToken: EqStringInput
  id: OperatorsIdInput
  ticketTypeId: OperatorsIdInput
}

"  Validation messages are returned when mutation input does not meet the requirements.\n  While client-side validation is highly recommended to provide the best User Experience,\n  All inputs will always be validated server-side.\n\n  Some examples of validations are:\n\n  * Username must be at least 10 characters\n  * Email field does not contain an email address\n  * Birth Date is required\n\n  While GraphQL has support for required values, mutation data fields are always\n  set to optional in our API. This allows 'required field' messages\n  to be returned in the same manner as other validations. The only exceptions\n  are id fields, which may be required to perform updates or deletes.\n"
type ValidationMessage {
  """A unique error code for the type of validation used."""
  code: String!

  "The input field that the error applies to. The field can be used to\nidentify which field the error message should be displayed next to in the\npresentation layer.\n\nIf there are multiple errors to display for a field, multiple validation\nmessages will be in the result.\n\nThis field may be null in cases where an error cannot be applied to a specific field.\n"
  field: String

  "A friendly error message, appropriate for display to the end user.\n\nThe message is interpolated to include the appropriate variables.\n\nExample: `Username must be at least 10 characters`\n\nThis message may change without notice, so we do not recommend you match against the text.\nInstead, use the *code* field for matching.\n"
  message: String

  """A list of substitutions to be applied to a validation message template"""
  options: [ValidationOption]

  "A template used to generate the error message, with placeholders for option substiution.\n\nExample: `Username must be at least {count} characters`\n\nThis message may change without notice, so we do not recommend you match against the text.\nInstead, use the *code* field for matching.\n"
  template: String
}

type ValidationOption {
  """
  The name of a variable to be subsituted in a validation message template
  """
  key: String!

  """
  The value of a variable to be substituted in a validation message template
  """
  value: String!
}

type Variant {
  """Variant unique ID"""
  id: ID

  """Variant name"""
  name: String

  """Variant size"""
  size: String

  """Variant SKU"""
  sku: String
}

type Venue {
  """Age limit"""
  ageLimit: String

  """City"""
  city: String

  """Country"""
  country: String

  """Displayed address"""
  displayedAddress: String

  """Latitude"""
  latitude: Float

  """Longitude"""
  longitude: Float

  """Title. Field is available with restricted access"""
  name: String

  """Post office box number"""
  postOfficeBoxNumber: String

  """Postal code"""
  postalCode: String

  """Region/Province"""
  region: String

  """State"""
  state: String

  """Street address"""
  streetAddress: String

  """Timezone name"""
  timezoneName: String

  """Type"""
  type: String
}

"""The currently authenticated partner."""
type Viewer implements Node {
  """Paginated list of events. Field is available with restricted access"""
  events(after: String, before: String, first: Int, last: Int, where: EventWhereInput): EventConnection

  """Paginated list of extras. Field is available with restricted access"""
  extras(after: String, before: String, first: Int, last: Int, where: ExtraWhereInput): ExtraConnection

  """Paginated list of event genre types"""
  genreTypes(after: String, before: String, first: Int, last: Int, where: GenreTypeWhereInput): GenreTypesConnection

  """The ID of an object"""
  id: ID!

  """Name"""
  name: String

  """Paginated list of orders. Field is available with restricted access"""
  orders(after: String, before: String, first: Int, last: Int, where: OrderWhereInput): OrderConnection

  """Paginated list of returns"""
  returns(after: String, before: String, first: Int, last: Int, where: ReturnWhereInput): ReturnConnection

  """Paginated list of ticket transfers"""
  ticketTransfers(after: String, before: String, first: Int, last: Int, where: TicketTransferWhereInput): TicketTransferConnection

  """Paginated list of tickets. Field is available with restricted access"""
  tickets(after: String, before: String, first: Int, last: Int, where: TicketWhereInput): TicketConnection
}