import DiceService from '@/lib/dice/service'
import { NextResponse } from 'next/server'
import type { PayloadRequest } from 'payload'
import { promises as fs } from 'fs'
import path from 'path'
import { tmpdir } from 'os'
import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'
import type { Event as DiceEvent, Venue as DiceVenue } from '@/lib/dice/schema-types'
import { formatSlug } from '@/fields/slug/formatSlug'
import { handleImageUpload } from '../media/lib'

interface Result {
  startDatetime?: string
  endDatetime?: string
  description?: DefaultTypedEditorState
  socialLinks?: { resource: string; link: string }[]
  previewImage?: number
  hero?: number
  venue?: number
}

const createLexicalState = (text: string): DefaultTypedEditorState => ({
  root: {
    type: 'root',
    version: 1,
    format: '',
    indent: 0,
    direction: 'ltr',
    children: [
      {
        type: 'paragraph',
        version: 1,
        format: '',
        indent: 0,
        direction: 'ltr',
        textFormat: 0,
        children: [
          { type: 'text', version: 1, format: 0, detail: 0, mode: 'normal', style: '', text },
        ],
      },
    ],
  },
})

async function handleVenue(venue: DiceVenue, req: PayloadRequest) {
  const { docs } = await req.payload.find({
    collection: 'venues',
    where: {
      address: { equals: venue.streetAddress },
    },
  })
  if (docs[0]) return docs[0].id

  const newVenue = await req.payload.create({
    collection: 'venues',
    data: {
      slug: formatSlug(venue.name as string),
      name: venue.name as string,
      address: venue.streetAddress
        ? (venue.streetAddress as string)
        : (venue.displayedAddress as string),
      city: venue.city as string,
      country: venue.country as string,
      timezone: venue.timezoneName as string,
      coordinates: [venue.longitude ?? 0, venue.latitude ?? 0],
    },
  })

  return newVenue.id
}

export async function handleDicePartnerEvent(event: DiceEvent, req: PayloadRequest) {
  const result: Result = {}
  console.log(event);
  

  if (event.images?.length) {
    const square = event.images.find((i) => i?.type === 'SQUARE')?.url
    const landscape = event.images.find((i) => i?.type === 'LANDSCAPE')?.url

    if (square) {
      const fileName = event.name ? `${event.name} square image` : `${event.id} square image`
      const doc = await handleImageUpload(square, formatSlug(event.name || event.id), fileName, req.payload)
      result.previewImage = doc.id
    }
    if (landscape) {
      const fileName = event.name ? `${event.name} landscape image` : `${event.id} landscape image`
      const doc = await handleImageUpload(
        landscape,
        formatSlug(event.name || event.id),
        fileName,
        req.payload,
      )
      result.hero = doc.id
    }
  }

  if (event.description) {
    result.description = createLexicalState(event.description)
  }

  if (event.startDatetime) result.startDatetime = event.startDatetime
  if (event.endDatetime) result.endDatetime = event.endDatetime

  if (event.url) {
    result.socialLinks = [{ resource: 'Dice', link: event.url }]
  }

  if (event.venues && event.venues[0]) {
    result.venue = await handleVenue(event.venues[0], req)
  }
  return result
}
export async function getEventData(id: string, req: PayloadRequest): Promise<Result> {
  const dice = new DiceService(process.env.DICE_PARTNER_API_TOKEN || '', process.env.DICE_PARTNER_API_ENDPOINT || 'https://partners-endpoint.dice.fm/graphql')
  const event = (await dice.getEvent(id)) as DiceEvent
  return await handleDicePartnerEvent(event, req)
}
