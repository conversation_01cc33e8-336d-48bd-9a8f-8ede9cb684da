import { S3Client, PutObjectCommand, GetObjectCommand, ListObjectsV2Command, ListObjectsV2CommandOutput } from '@aws-sdk/client-s3'
import { Readable } from 'stream'

export const s3 = new S3Client({
    endpoint: `${process.env.S3_ENDPOINT}` || '',
    region: process.env.S3_REGION ?? 'auto',
    credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY_ID!,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!,
    },
    forcePathStyle: true,
})

export async function uploadToObjectStorage(
    body: any,
    key: string,
    bucket: string,
    contentType: 'text/html' | 'application/json',
): Promise<string> {
    await s3.send(
        new PutObjectCommand({
            Bucket: bucket,
            Key: key,
            Body: body,
            ContentType: contentType,
        }),
    )
    const baseUrl = process.env.S3_PUBLIC_URL!
    return `${baseUrl}/${bucket}/${key}`
}

function streamToString(stream: Readable): Promise<string> {
    return new Promise((resolve, reject) => {
        const chunks: any[] = []
        stream.on('data', (chunk) => chunks.push(chunk))
        stream.on('error', reject)
        stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf-8')))
    })
}

export async function getFromObjectStorage(key: string, bucket: string): Promise<any> {
    try {
        const response = await s3.send(
            new GetObjectCommand({
                Bucket: bucket,
                Key: key,
            }),
        )

        const body = await streamToString(response.Body as Readable)
        return body
    } catch (err: any) {
        if (err.$metadata?.httpStatusCode === 404) return null
        return null
    }
}

export async function listAllKeys(
    bucket: string,
    prefix?: string
): Promise<string[]> {
    const keys: string[] = []
    let ContinuationToken: string | undefined = undefined

    do {
        const { Contents, NextContinuationToken }: ListObjectsV2CommandOutput = await s3.send(
            new ListObjectsV2Command({
                Bucket: bucket,
                Prefix: prefix,
                ContinuationToken,
            })
        )
        Contents?.forEach((obj) => {
            if (obj.Key) keys.push(obj.Key)
        })
        ContinuationToken = NextContinuationToken ?? undefined
    } while (ContinuationToken)

    return keys
}

