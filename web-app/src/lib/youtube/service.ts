
export type YouTubeVideo = {
  id: string
  title: string
  description: string
  publishedAt: string
  thumbnails: {
    default: { url: string; width: number; height: number }
    medium:  { url: string; width: number; height: number }
    high:    { url: string; width: number; height: number }
  }
  url: string
}

export type YouTubeVideoDetail = YouTubeVideo & {
  duration: string
  viewCount: number
  likeCount: number
  commentCount: number
  categoryId: string
}

export type YouTubeChannel = {
  id: string
  title: string
  description: string
  publishedAt: string
  thumbnails: YouTubeVideo['thumbnails']
  subscriberCount: number
  viewCount: number
  videoCount: number
}

export type YouTubeComment = {
  id: string
  authorDisplayName: string
  textDisplay: string
  publishedAt: string
}

class YouTubeService {
  private static readonly API_KEY: string = process.env.YOUTUBE_API_KEY || ''
  private static readonly BASE_URL = 'https://www.googleapis.com/youtube/v3'


  static async getChannelIdByHandle(handle: string): Promise<string> {
    try {
      const formatted = handle.startsWith('@') ? handle : `@${handle}`
      const params = new URLSearchParams({
        key: this.API_KEY,
        part: 'id',
        forHandle: formatted,
      })
      const res = await fetch(`${this.BASE_URL}/channels?${params}`)
      if (!res.ok) {
        console.error(`Channel lookup failed: ${res.status} ${res.statusText}`)
        return ''
      }
      const data = (await res.json()) as { items: Array<{ id: string }> }
      const item = data.items[0]
      if (!item) {
        console.error(`No channel found for handle ${formatted}`)
        return ''
      }
      return item.id
    } catch (error) {
      console.error('getChannelIdByHandle error:', error)
      return ''
    }
  }

  private static async fetchVideosPage(
    channelId: string,
    order: 'viewCount' | 'date',
    maxResults = 10,
    pageToken?: string
  ): Promise<{ videos: YouTubeVideo[]; nextPageToken?: string }> {
    try {
      const params = new URLSearchParams({
        key:        this.API_KEY,
        channelId,
        part:       'snippet',
        type:       'video',
        order,
        maxResults: String(maxResults),
        ...(pageToken ? { pageToken } : {}),
      })
      const res = await fetch(`${this.BASE_URL}/search?${params}`)
      if (!res.ok) {
        console.error(`YouTube search failed: ${res.status} ${res.statusText}`)
        return { videos: [], nextPageToken: undefined }
      }
      const data = (await res.json()) as {
        items: Array<{ id: { videoId: string }; snippet: any }>
        nextPageToken?: string
      }
      const videos = data.items.map(item => {
        const sn = item.snippet
        const vid = item.id.videoId
        return {
          id:          vid,
          title:       sn.title,
          description: sn.description,
          publishedAt: sn.publishedAt,
          thumbnails:  sn.thumbnails,
          url:         `https://www.youtube.com/watch?v=${vid}`,
        }
      })
      return { videos, nextPageToken: data.nextPageToken }
    } catch (error) {
      console.error('fetchVideosPage error:', error)
      return { videos: [], nextPageToken: undefined }
    }
  }

  static async getPopularVideos(
    channelId: string,
    maxResults = 10,
    pageToken?: string
  ): Promise<{ videos: YouTubeVideo[]; nextPageToken?: string }> {
    return this.fetchVideosPage(channelId, 'viewCount', maxResults, pageToken)
  }

  static async getPopularVideosByHandle(
    handle: string,
    maxResults = 10,
    pageToken?: string
  ): Promise<{ videos: YouTubeVideo[]; nextPageToken?: string }> {
    const channelId = await this.getChannelIdByHandle(handle)
    if (!channelId) return { videos: [], nextPageToken: undefined }
    return this.getPopularVideos(channelId, maxResults, pageToken)
  }

  static async getLatestVideos(
    channelId: string,
    maxResults = 10,
    pageToken?: string
  ): Promise<{ videos: YouTubeVideo[]; nextPageToken?: string }> {
    return this.fetchVideosPage(channelId, 'date', maxResults, pageToken)
  }

  static async searchVideos(
    query: string,
    maxResults = 10,
    pageToken?: string
  ): Promise<{ videos: YouTubeVideo[]; nextPageToken?: string }> {
    try {
      const params = new URLSearchParams({
        key:        this.API_KEY,
        part:       'snippet',
        q:          query,
        type:       'video',
        maxResults: String(maxResults),
        ...(pageToken ? { pageToken } : {}),
      })
      const res = await fetch(`${this.BASE_URL}/search?${params}`)
      if (!res.ok) {
        console.error(`Video search failed: ${res.status} ${res.statusText}`)
        return { videos: [], nextPageToken: undefined }
      }
      const data = (await res.json()) as {
        items: Array<{ id: { videoId: string }; snippet: any }>
        nextPageToken?: string
      }
      const videos = data.items.map(item => {
        const sn = item.snippet
        const vid = item.id.videoId
        return {
          id:          vid,
          title:       sn.title,
          description: sn.description,
          publishedAt: sn.publishedAt,
          thumbnails:  sn.thumbnails,
          url:         `https://www.youtube.com/watch?v=${vid}`,
        }
      })
      return { videos, nextPageToken: data.nextPageToken }
    } catch (error) {
      console.error('searchVideos error:', error)
      return { videos: [], nextPageToken: undefined }
    }
  }
}

export default YouTubeService
