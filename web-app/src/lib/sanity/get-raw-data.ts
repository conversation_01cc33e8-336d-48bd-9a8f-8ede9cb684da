import { createClient } from '@sanity/client'

const client = createClient({
  projectId: 'pge26oqu',
  dataset: 'production',
  useCdn: true,
  apiVersion: '2023-01-01',
  resultSourceMap: true,
})

export async function getSanytiData(type: string) {
  const result = await client.fetch(`*[_type == "${type}"]`)
  return result
}

export async function getSanytiDocById(id: string) {
  const result = await client.fetch(`*[_id == "${id}"]`)
  return result
}

export const sanityCollections = [
  'genre',
  'author',
  'venue',
  'eventBrand',
  'residency',
  'festival',
  'article',
  'artist',
  'event',
] as const
