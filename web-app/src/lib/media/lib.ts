import { BasePayload } from 'payload'
import { promises as fs } from 'fs'
import path, { basename, extname } from 'path'
import { tmpdir } from 'os'
import slugify from 'slugify'

export async function handleImageUpload(
    url: string,
    slug: string,
    filename: string, 
    payload: BasePayload,
  ) {
    const { docs } = await payload.find({
      collection: 'media',
      limit: 1,
      where: { alt: { equals: slug } },
    })
    if (docs[0]) return docs[0]
  
    const res = await fetch(url)
    if (!res.ok) throw new Error(`Failed to download image: ${res.status}`)
    const buffer = Buffer.from(await res.arrayBuffer())
  
    const originalExt = extname(filename) || '.jpg'           
    const safeBase = slugify(basename(filename, originalExt), {
      lower: true,
      strict: true,   
    })
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-') 
    const safeName = `${safeBase}-${timestamp}${originalExt}`      
    const tmpPath = path.join(tmpdir(), safeName)
  
    await fs.writeFile(tmpPath, buffer)
  
    const mediaDoc = await payload.create({
      collection: 'media',
      filePath: tmpPath,
      data: { alt: slug },
      overrideAccess: true,
    })
  
    await fs.unlink(tmpPath)
    return mediaDoc
  }