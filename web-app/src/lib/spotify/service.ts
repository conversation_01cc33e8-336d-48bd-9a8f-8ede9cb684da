type AuthResponse = {
  access_token: string
  expires_in: number
}

export type SpotifyArtist = {
  id: string
  name: string
  images: { url: string; height: number; width: number }[]
  genres: string[]
  external_urls: { spotify: string }
}

export type SpotifyTrack = {
  id: string
  name: string
  artists: { id: string; name: string }[]
  album: {
    id: string
    name: string
    images: { url: string; height: number; width: number }[]
  }
  external_urls: { spotify: string }
  preview_url: string | null
  popularity: number
  duration_ms: number
}

const DEFAULT_ARTIST: SpotifyArtist = {
  id: '',
  name: '',
  images: [],
  genres: [],
  external_urls: { spotify: '' },
}

class SpotifyService {
  private static token: string | null = null
  private static expiresAt = 0

  private static async fetchToken(): Promise<string> {
    const now = Date.now()
    if (this.token && now < this.expiresAt) return this.token
    try {
      const cid = process.env.SPOTIFY_CLIENT_ID!
      const secret = process.env.SPOTIFY_CLIENT_SECRET!
      const res = await fetch('https://accounts.spotify.com/api/token', {
        method: 'POST',
        headers: {
          Authorization: 'Basic ' + Buffer.from(`${cid}:${secret}`).toString('base64'),
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({ grant_type: 'client_credentials' }),
      })
      if (!res.ok) {
        console.error(`Token fetch failed: ${res.statusText}`)
        return ''
      }
      const data = (await res.json()) as AuthResponse
      this.expiresAt = now + (data.expires_in - 60) * 1000
      this.token = data.access_token
      return this.token
    } catch (error) {
      console.error('Token fetch error:', error)
      return ''
    }
  }

  static async getArtist(id: string): Promise<SpotifyArtist> {
    try {
      const token = await this.fetchToken()
      if (!token) {
        console.error('No valid token available for getArtist')
        return DEFAULT_ARTIST
      }
      const res = await fetch(`https://api.spotify.com/v1/artists/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      if (!res.ok) {
        console.error(`Artist fetch failed: ${res.statusText}`)
        return DEFAULT_ARTIST
      }
      return (await res.json()) as SpotifyArtist
    } catch (error) {
      console.error('getArtist error:', error)
      return DEFAULT_ARTIST
    }
  }

  static async searchArtists(
    query: string,
    limit = 20,
    offset = 0,
  ): Promise<SpotifyArtist[]> {
    try {
      const token = await this.fetchToken()
      if (!token) {
        console.error('No valid token available for searchArtists')
        return []
      }
      const params = new URLSearchParams({
        q: query,
        type: 'artist',
        limit: String(limit),
        offset: String(offset),
      })
      const res = await fetch(`https://api.spotify.com/v1/search?${params}`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      if (!res.ok) {
        console.error(`Search artists failed: ${res.statusText}`)
        return []
      }
      const data = (await res.json()) as { artists: { items: SpotifyArtist[] } }
      return data.artists.items
    } catch (error) {
      console.error('searchArtists error:', error)
      return []
    }
  }

  static async getArtistTopTracks(
    artistId: string,
    market = 'US',
  ): Promise<SpotifyTrack[]> {
    try {
      const token = await this.fetchToken()
      if (!token) {
        console.error('No valid token available for getArtistTopTracks')
        return []
      }
      const res = await fetch(
        `https://api.spotify.com/v1/artists/${artistId}/top-tracks?market=${market}`,
        { headers: { Authorization: `Bearer ${token}` } },
      )
      if (!res.ok) {
        console.error(`Top tracks fetch failed: ${res.statusText}`)
        return []
      }
      const data = (await res.json()) as { tracks: SpotifyTrack[] }
      return data.tracks
    } catch (error) {
      console.error('getArtistTopTracks error:', error)
      return []
    }
  }
}

export default SpotifyService
