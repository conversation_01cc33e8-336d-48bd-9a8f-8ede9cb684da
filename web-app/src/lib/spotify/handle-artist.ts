import type { BasePayload } from 'payload'
import SpotifyService, { SpotifyArtist } from './service'
import { handleImageUpload } from '../media/lib'
import { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'
import { createLexicalState, createLexicalStateFromHTML } from '../sync/lib'
import ChartmetricService from '../chartmetric/service'
import { socialLinks } from '@/fields/socialLinks'
type SocialLink = {
  resource:
    | 'Facebook'
    | 'Instagram'
    | 'TikTok'
    | 'SoundCloud'
    | 'Discord'
    | 'YouTube'
    | 'Twitter'
    | 'Twitch'
    | 'Pinterest'
    | 'Spotify'
    | 'BeatPort'
    | 'Website'
    | 'Dice'
  link: string
}

interface Result {
  genres?: number[]
  socialLinks?: SocialLink[]
  previewImage?: number
  overview?: DefaultTypedEditorState
  country?: number
  chartmetricId?: number
}


type GenreSource = 'Spotify' | 'GrayArea' | 'Dice' | 'ResidentAdvisor' | 'ChartMetric'

async function handleGenres(genres: string[], payload: BasePayload, type: GenreSource = 'Spotify') {
  const ids: number[] = []
  for (const genre of genres) {
    const { docs } = await payload.find({
      collection: 'genres',
      limit: 1,
      where: { name: { equals: genre }, type: { equals: type } },
    })
    if (docs[0]) {
      ids.push(docs[0].id)
      continue
    }

    const genreDoc = await payload.create({
      collection: 'genres',
      data: { name: genre, type, slug: genre },
      overrideAccess: true,
    })

    ids.push(genreDoc.id)
  }
  return ids
}

async function handleCountry(code: string, payload: BasePayload) {
  const { docs } = await payload.find({
    collection: 'countries',
    limit: 1,
    where: { code: { equals: code } },
  })
  if (docs[0]) {
    return docs[0].id
  }
}

const getArtistUrlsByName = async (name: string) => {
  const { obj } = await ChartmetricService.searchArtists(name)
  const first = obj.artists[0]
  if (!first) return null
  const { obj: urls } = await ChartmetricService.getArtistUrls(first.id)
  return { id: first.id, name: first.name, urls }
}

export async function getArtistData(
  spotifyId: string,
  payload: BasePayload,
  withoutCM: boolean = false,
): Promise<Result> {
  const artist = (await SpotifyService.getArtist(spotifyId)) as SpotifyArtist
  const result: Result = { socialLinks: [], overview: createLexicalState('') }

  if (artist.images?.length) {
    const img = artist.images.find((i) => i?.height === 640)?.url
    if (img) {
      const filename = `${artist.name} - Spotify Cover`
      const doc = await handleImageUpload(img, artist.name, filename, payload)
      result.previewImage = doc.id
    }
  }

  if (artist.genres?.length) {
    result.genres = await handleGenres(artist.genres, payload)
  }

  if (!withoutCM) {
    try {
      const cm = await getArtistUrlsByName(artist.name)
      if (cm) {
        result.chartmetricId = cm.id
        result.socialLinks = (socialLinks[0] as any)?.options
          .map((x: { value: string }) => {
            const cmUrl = cm.urls.find((y) => y.domain.toLowerCase() === x.value.toLowerCase())
            if (!cmUrl) return
            return {
              resource: x.value,
              link: cmUrl.url[0],
            }
          })
          .filter((x: any) => x)
        const { obj: profile } = (await ChartmetricService.getArtist(cm.id)) as { obj: any }
        if (profile?.code2) {
          result.country = await handleCountry(profile.code2, payload)
        }
        if (profile?.genres) {
          const genreNames = new Set<string>()

          if (profile.genres.primary?.name) {
            genreNames.add(profile.genres.primary.name)
          }

          for (const g of profile.genres.secondary ?? []) {
            if (g?.name) genreNames.add(g.name)
          }

          for (const g of profile.genres.sub ?? []) {
            if (g?.name) genreNames.add(g.name)
          }

          const cmGenres = await handleGenres(Array.from(genreNames), payload, 'ChartMetric')
          result.genres = [...new Set([...(result.genres ?? []), ...cmGenres])]
        }

        const bio: string | undefined = profile?.bio ?? profile?.biography ?? profile?.description
        if (bio && !bio.toLowerCase().startsWith('http')) {
          result.overview = createLexicalStateFromHTML(bio)
        }
      }
    } catch (err) {}
  }

  return result
}
