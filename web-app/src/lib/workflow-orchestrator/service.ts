import { BasePayload } from 'payload'
import { logger } from '../logger/logger'

export type WorkflowSlug =
  | 'crawlSanity'
  | 'crawlRAWorkflow'
  | 'crawlSongkickWorkflow'
  | 'crawlDicekWorkflow'
  | 'importSanityDataWorkflow'
  | 'syncDicePartnerEventsWorkflow'
  | 'syncDiceOrdersWorkflow'
  | 'syncDiceWorkflow'
  | 'syncQFlowWorkflow'
  | 'syncSongkickWorkflow'
  | 'syncPlanetscaleUsersWorkflow'
  | 'syncPlanetscalePresaleRegistrationsWorkflow'
  | 'syncSongkickWorkflow'

interface WorkflowOrchestratorOptions {
  continueOnError?: boolean
}

type StepConfig = {
  slug: WorkflowSlug
  continueOnError?: boolean
  needs?: WorkflowSlug[]
}

export class WorkflowOrchestrator {
  private stages: StepConfig[][] = []
  private globalContinueOnError: boolean
  private payload: BasePayload

  constructor(payload: BasePayload, { continueOnError = false }: WorkflowOrchestratorOptions = {}) {
    this.payload = payload
    this.globalContinueOnError = continueOnError
  }

  addStage(stepConfigs: StepConfig[]) {
    if (!Array.isArray(stepConfigs) || stepConfigs.length === 0) {
      throw new Error('addStage требует непустой массив StepConfig')
    }
    const copy: StepConfig[] = stepConfigs.map((s) => ({
      slug: s.slug,
      continueOnError: s.continueOnError,
      needs: s.needs ? [...s.needs] : undefined,
    }))
    this.stages.push(copy)
    return this
  }

  async run(): Promise<Record<WorkflowSlug, string>> {
    const log = logger.child({ module: 'job-orchestrator' })

    const allJobIds: Record<string, string> = {}
    const completedGlobal = new Set<WorkflowSlug>()

    for (const stage of this.stages) {
      const remaining = new Set<WorkflowSlug>(stage.map((s) => s.slug))
      const configBySlug = new Map<WorkflowSlug, StepConfig>()
      stage.forEach((s) => configBySlug.set(s.slug, s))

      while (remaining.size > 0) {
        const ready: WorkflowSlug[] = []
        for (const slug of remaining) {
          const cfg = configBySlug.get(slug)!
          const needs = cfg.needs || []
          const allDepsDone = needs.every((dep) => completedGlobal.has(dep))
          if (allDepsDone) {
            ready.push(slug)
          }
        }

        if (ready.length === 0) {
          const unresolved = Array.from(remaining).join(', ')
          log.error(
            `Cyclical or unsupported dependence in stage. ` +
              `There are steps left: [${unresolved}], but none can start (needs not met).`,
          )
          throw new Error(
            `Cyclical or unsupported dependence in stage. ` +
              `There are steps left: [${unresolved}], but none can start (needs not met).`,
          )
        }

        const wavePromises = ready.map(async (slug) => {
          const cfg = configBySlug.get(slug)!
          const thisStepContinue = cfg.continueOnError ?? this.globalContinueOnError

          let jobId: string | null = null
          try {
            const existing = await this.payload.find({
              collection: 'payload-jobs',
              where: {
                workflowSlug: { equals: slug },
                completedAt: { equals: null },
              },
              limit: 1,
            })
            if (existing.totalDocs > 0) {
              const msg = `Workflow “${slug}” is already running or waiting to be executed.`
              log.warn(msg)
              if (!thisStepContinue) {
                log.error(msg)
                throw new Error(msg)
              }
              return null
            }

            const queued = await this.payload.jobs.queue({ workflow: slug, input: {} })
            jobId = String(queued.id)
            log.info(`[${new Date().toISOString()}] [${slug}] queued as jobId=${jobId}`)

            await this.payload.jobs.runByID({ id: jobId })
            log.info(`[${new Date().toISOString()}] [${slug}] runByID finished`)

            return jobId
          } catch (err) {
            log.error(`Error in the workflow "${slug}":`, err)
            if (!thisStepContinue) {
              log.error(err)
              throw err
            } else {
              log.warn(`Ignore the “${slug}” error and continue.`)
              return null
            }
          }
        })

        const waveResults = await Promise.all(wavePromises)

        ready.forEach((slug, idx) => {
          const id = waveResults[idx]
          if (id) {
            allJobIds[slug] = id
          }
          completedGlobal.add(slug)
          remaining.delete(slug)
        })
      }
    }

    return allJobIds
  }
}
