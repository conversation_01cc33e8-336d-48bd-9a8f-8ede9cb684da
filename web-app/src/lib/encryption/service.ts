import crypto from 'crypto';

const ALGO = 'aes-256-gcm';
const IV_BYTES = 12;
const TAG_BYTES = 16;
const KEY_BYTES = 32;

export class CryptoService {
  private key: Buffer | undefined = undefined;

  private initKey() {
    const secret = process.env.PAYLOAD_SECRET!;

    this.key = crypto
      .createHash('sha256')
      .update(secret, 'utf8')
      .digest()
      .slice(0, KEY_BYTES);
  }
  private getKey ():Buffer{
    if(!this.key){
      this.initKey()
    }
    return this.key as Buffer
  }
  public encrypt(plain: string): string {
    if (typeof plain !== 'string') {
      return plain;
    }
    const iv = crypto.randomBytes(IV_BYTES);
    const cipher = crypto.createCipheriv(ALGO, this.getKey(), iv);
    const ciphertext = Buffer.concat([
      cipher.update(plain, 'utf8'),
      cipher.final(),
    ]);
    const tag = cipher.getAuthTag();
    return Buffer.concat([iv, tag, ciphertext]).toString('hex');
  }

  public decrypt(data: unknown): string | unknown {
    if (
      typeof data !== 'string' ||
      !/^[0-9a-f]+$/i.test(data) ||
      data.length % 2 !== 0
    ) {
      return data;
    }

    const buf = Buffer.from(data, 'hex');
    if (buf.length < IV_BYTES + TAG_BYTES) {
      return data;
    }

    const iv = buf.subarray(0, IV_BYTES);
    const tag = buf.subarray(IV_BYTES, IV_BYTES + TAG_BYTES);
    const ciphertext = buf.subarray(IV_BYTES + TAG_BYTES);

    try {
      const decipher = crypto.createDecipheriv(ALGO, this.getKey(), iv);
      decipher.setAuthTag(tag);
      const plainBuf = Buffer.concat([
        decipher.update(ciphertext),
        decipher.final(),
      ]);
      return plainBuf.toString('utf8');
    } catch {
      return data;
    }
  }
}
