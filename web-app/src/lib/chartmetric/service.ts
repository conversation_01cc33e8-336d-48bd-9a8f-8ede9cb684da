type AuthResponse = {
  token: string
  expires_in: number
  refresh_token: string
  scope: string
}

type UrlItem = { domain: string; url: string[] }
type CanonicalUrls = Record<string, string | null>

export default class ChartmetricService {
  private static bearer: string | null = null
  private static bearerExpiry = 0

  private static async ensureBearer(): Promise<string> {
    const now = Date.now()
    if (this.bearer && now < this.bearerExpiry - 5_000) {
      return this.bearer
    }
    try {
      const res = await fetch('https://api.chartmetric.com/api/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshtoken: process.env.CHARTMETRIC_TOKEN }),
      })
      if (!res.ok) {
        console.error(`Chartmetric auth failed: ${res.status} ${res.statusText}`)
        return ''
      }
      const { token, expires_in } = (await res.json()) as AuthResponse
      this.bearer = token
      this.bearerExpiry = Date.now() + expires_in * 1_000
      return this.bearer
    } catch (error) {
      console.error('Chartmetric auth error:', error)
      return ''
    }
  }

  private static async get<T>(
    path: string,
    qs: Record<string, string> = {},
    attempt = 0,
  ): Promise<T> {
    try {
      const bearer = await this.ensureBearer()
      if (!bearer) {
        console.error('No valid bearer token available')
        return {} as T
      }
      const url = new URL(`https://api.chartmetric.com/api/${path}`)
      Object.entries(qs).forEach(([k, v]) => url.searchParams.set(k, v))
      const res = await fetch(url.toString(), {
        headers: { Authorization: `Bearer ${bearer}` },
      })

      if (res.status === 429) {
        const retryAfter =
          Number(res.headers.get('Retry-After')) ||
          (Number(res.headers.get('X-RateLimit-Reset')) - Math.floor(Date.now() / 1_000))
        if (attempt < 5) {
          await new Promise((r) => setTimeout(r, Math.max(retryAfter, 1) * 1_000))
          return this.get<T>(path, qs, attempt + 1)
        }
      }

      if (!res.ok) {
        console.error(`Chartmetric request failed: ${res.status} ${res.statusText} for ${url}`)
        return {} as T
      }

      return (await res.json()) as T
    } catch (error) {
      console.error(`Error in Chartmetric GET (${path}):`, error)
      return {} as T
    }
  }

  static async searchArtists(
    q: string,
  ): Promise<{ obj: { artists: { id: number; name: string }[] } }> {
    return (await this.get<{ obj: { artists: { id: number; name: string }[] } }>(
      'search',
      { q, type: 'artists' },
    )) as { obj: { artists: { id: number; name: string }[] } }
  }

  static async getArtist(
    id: number,
  ): Promise<any> {
    return (await this.get<any>(`artist/${id}`))
  }

  static async getArtistMetrics(
    id: number,
    since = '30d',
  ): Promise<any> {
    return (await this.get<any>(`artist/${id}/stats`, { since }))
  }

  static async getArtistUrls(
    id: number,
  ): Promise<{ obj: UrlItem[] }> {
    return (await this.get<{ obj: UrlItem[] }>(`artist/${id}/urls`))
  }

  static async searchArtistUrls(
    name: string,
  ): Promise<{
    id: number
    name: string
    urls: CanonicalUrls
  } | null> {
    try {
      const { obj } = await this.searchArtists(name)
      const first = obj.artists[0]
      if (!first) return null
      const { obj: links } = await this.get<{ obj: UrlItem[] }>(
        `artist/${first.id}/urls`,
      )
      const urls: CanonicalUrls = {}
      links.forEach(({ domain, url }) => {
        urls[domain] = [...new Set(url)][0] ?? null
      })
      return { id: first.id, name: first.name, urls }
    } catch (error) {
      console.error('searchArtistUrls error:', error)
      return null
    }
  }
}