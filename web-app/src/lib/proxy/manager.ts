import fetch from "node-fetch";

const ONE_DAY_SECONDS = 24 * 60 * 60;
const PROXYSCRAPE_URL =
  "https://api.proxyscrape.com/v4/free-proxy-list/get?request=display_proxies&proxy_format=protocolipport&format=json";

let proxyList: string[] | null = null;
let proxyListFetchedAt: number | null = null;

const failedMap: Map<string, number> = new Map();
const lastUsedMap: Map<string, number> = new Map();

function nowEpochSeconds(): number {
  return Math.floor(Date.now() / 1000);
}

export async function getProxyList(): Promise<string[]> {
  const now = nowEpochSeconds();

  if (
    proxyList !== null &&
    proxyListFetchedAt !== null &&
    now - proxyListFetchedAt < ONE_DAY_SECONDS
  ) {
    return proxyList;
  }

  let res;
  try {
    res = await fetch(PROXYSCRAPE_URL);
  } catch (networkErr) {
    throw new Error(`Network error fetching proxy list: ${(networkErr as Error).message}`);
  }

  if (!res.ok) {
    throw new Error(`Failed to fetch proxy list: ${res.status} ${res.statusText}`);
  }

  const contentType = res.headers.get("content-type") || "";
  if (!contentType.includes("application/json")) {
    throw new Error(`Unexpected Content-Type "${contentType}", expected JSON`);
  }

  let parsed: unknown;
  try {
    parsed = await res.json();
  } catch (jsonErr) {
    throw new Error(`Failed to parse proxy list JSON: ${(jsonErr as Error).message}`);
  }

  let extracted: string[] = [];

  if (Array.isArray(parsed)) {
    if (!parsed.every((el) => typeof el === "string")) {
      throw new Error(
        `Expected array of strings, but got array with element types: ${parsed
          .map((el) => typeof el)
          .join(", ")}`
      );
    }
    extracted = parsed as string[];
  }
  else if (parsed !== null && typeof parsed === "object") {
    const obj = parsed as Record<string, unknown>;

    if ("data" in obj) {
      const maybeArr = (obj as any).data;
      if (!Array.isArray(maybeArr)) {
        throw new Error(`Expected parsed.data to be an array, but got ${typeof maybeArr}`);
      }
      for (const el of maybeArr) {
        if (el && typeof el === "object" && typeof (el as any).proxy === "string") {
          extracted.push((el as any).proxy);
        } else {
          throw new Error(
            `Expected each element in parsed.data to have a string "proxy" field; got ${JSON.stringify(
              el
            )}`
          );
        }
      }
    }
    else if ("proxies" in obj) {
      const maybeArr = (obj as any).proxies;
      if (!Array.isArray(maybeArr)) {
        throw new Error(`Expected parsed.proxies to be an array, but got ${typeof maybeArr}`);
      }
      for (const el of maybeArr) {
        if (el && typeof el === "object" && typeof (el as any).proxy === "string") {
          extracted.push((el as any).proxy);
        } else {
          throw new Error(
            `Expected each element in parsed.proxies to have a string "proxy" field; got ${JSON.stringify(
              el
            )}`
          );
        }
      }
    }
    else {
      const vals = Object.values(obj);
      for (const val of vals) {
        if (val && typeof val === "object" && typeof (val as any).proxy === "string") {
          extracted.push((val as any).proxy);
        } else {
          throw new Error(
            `Expected each value in parsed object to be object with string "proxy"; got ${JSON.stringify(
              val
            )}`
          );
        }
      }
    }
  }
  else {
    throw new Error(
      `Unexpected JSON type: ${parsed === null ? "null" : typeof parsed}, expected array or object`
    );
  }

  if (extracted.length === 0) {
    throw new Error("Proxy list parsed successfully, but no entries were extracted");
  }

  proxyList = extracted;
  proxyListFetchedAt = now;
  failedMap.clear();
  lastUsedMap.clear();

  return proxyList;
}

export async function getAvailableProxies(): Promise<string[]> {
  const list = await getProxyList();
  const now = nowEpochSeconds();
  const available: string[] = [];

  for (const proxy of list) {
    const failTs = failedMap.get(proxy);
    if (failTs === undefined) {
      available.push(proxy);
    } else if (now - failTs >= ONE_DAY_SECONDS) {
      failedMap.delete(proxy);
      available.push(proxy);
    }
  }

  return available;
}

export async function chooseNextProxy(): Promise<string> {
  const available = await getAvailableProxies();

  if (!Array.isArray(available)) {
    throw new Error(`Internal error: available proxies is not an array (got ${typeof available})`);
  }
  if (available.length === 0) {
    throw new Error("No available proxies at the moment");
  }

  const now = nowEpochSeconds();
  const newProxies: string[] = [];
  const usedProxies: string[] = [];

  for (const proxy of available) {
    if (!lastUsedMap.has(proxy)) newProxies.push(proxy);
    else usedProxies.push(proxy);
  }

  let chosen: string;
  if (newProxies.length > 0) {
    chosen = newProxies[Math.floor(Math.random() * newProxies.length)]!;
  } else {
    chosen = usedProxies[0]!;
    let minTs = lastUsedMap.get(chosen)!;
    for (const p of usedProxies) {
      const ts = lastUsedMap.get(p)!;
      if (ts < minTs) {
        chosen = p;
        minTs = ts;
      }
    }
  }

  lastUsedMap.set(chosen, now);
  return chosen;
}
export async function markProxyFailed(proxy: string): Promise<void> {
  const now = nowEpochSeconds();
  failedMap.set(proxy, now);
  lastUsedMap.delete(proxy);
}
