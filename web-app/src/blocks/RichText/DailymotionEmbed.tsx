'use client';

import React, { useMemo } from 'react';
import type { TextFieldClientComponent } from 'payload';
import {
  useField,
  TextInput,
  FieldError,
  FieldDescription,
} from '@payloadcms/ui';

const DailymotionEmbed: TextFieldClientComponent = ({ path }) => {
  const {
    value = '',
    setValue,
    showError,
    errorMessage,
  } = useField<string>({ path });

  const embedInfo = useMemo<{
    type: 'video' | 'playlist';
    id: string;
  } | null>(() => {
    const url = value.trim();
    let m = url.match(/dailymotion\.com\/video\/([^_/?]+)/);
    if (m) {
      return { type: 'video', id: m[1]! };
    }
    m = url.match(/dailymotion\.com\/playlist\/([^_/?]+)/);
    if (m) {
      return { type: 'playlist', id: m[1]! };
    }
    m = url.match(/dai\.ly\/([^_/?]+)/);
    if (m) {
      return { type: 'video', id: m[1]! };
    }
    return null;
  }, [value]);

  return (
    <div style={{ marginBottom: '1.5rem' }}>
      <label
        htmlFor={path}
        style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}
      >
        Dailymotion URL
      </label>

      <TextInput
        path={path}
        value={value}
        placeholder="https://www.dailymotion.com/video/x7za5 or /playlist/x7v75l"
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setValue(e.target.value)
        }
        aria-invalid={showError}
      />

      {showError && <FieldError path={path} />}
      <FieldDescription path={path} description="Paste a Dailymotion video or playlist URL." />

      {embedInfo && (
        <div
          style={{
            position: 'relative',
            width: '100%',
            marginTop: '1rem',
            paddingBottom: '56.25%', // 16:9 aspect ratio
            height: 0,
          }}
        >
          <iframe
            src={`https://www.dailymotion.com/embed/${embedInfo.type}/${embedInfo.id}`}
            frameBorder="0"
            allow="autoplay; fullscreen"
            allowFullScreen
            title="Dailymotion embed"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
            }}
          />
        </div>
      )}
    </div>
  );
};

export default DailymotionEmbed;
