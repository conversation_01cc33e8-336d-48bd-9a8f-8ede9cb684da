'use client';

import React, { useState, useEffect, useMemo } from 'react';
import type { TextFieldClientComponent } from 'payload';
import {
  useField,
  TextInput,
  FieldError,
  FieldDescription,
} from '@payloadcms/ui';

const XEmbed: TextFieldClientComponent = ({ path }) => {
  const { value = '', setValue, showError } = useField<string>({ path });
  const [embedHtml, setEmbedHtml] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [loadError, setLoadError] = useState<boolean>(false);

  const cleanUrl = useMemo<string | null>(() => {
    try {
      const u = new URL(value.trim());
      const host = u.host.replace(/^www\./, '');
      if ((host === 'twitter.com' || host === 'x.com') && /\/status\/\d+/.test(u.pathname)) {
        return `${u.origin}${u.pathname}`;
      }
    } catch {}
    return null;
  }, [value]);

  useEffect(() => {
    if (!cleanUrl) {
      setEmbedHtml('');
      return;
    }
    setLoading(true);
    setLoadError(false);
    fetch(`/api/twitter?url=${encodeURIComponent(cleanUrl)}`)
      .then(r => r.json())
      .then(d => {
        if (d.html) {
          setEmbedHtml(d.html);
        } else {
          setEmbedHtml('');
          setLoadError(true);
        }
      })
      .catch(() => {
        setEmbedHtml('');
        setLoadError(true);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [cleanUrl]);

  useEffect(() => {
    if (!embedHtml) return;
    if ((window as any).twttr?.widgets) {
      (window as any).twttr.widgets.load();
    } else {
      const s = document.createElement('script');
      s.src = 'https://platform.twitter.com/widgets.js';
      s.async = true;
      document.body.appendChild(s);
    }
  }, [embedHtml]);

  return (
    <div style={{ marginBottom: '1rem' }}>
      <label htmlFor={path} style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>
        Tweet URL
      </label>
      <TextInput
        path={path}
        value={value}
        placeholder="https://x.com/username/status/1234567890123456789"
        onChange={(e: { target: { value: unknown; }; }) => setValue(e.target.value)}
        aria-invalid={showError}
      />
      {showError && <FieldError path={path} />}
      <FieldDescription path={path} />

      {loading && (
        <p style={{ marginTop: '1rem', color: '#888' }}>
          Loading preview…
        </p>
      )}

      {loadError && !loading && (
        <p style={{ marginTop: '1rem', color: 'red' }}>
          Failed to load embed.
        </p>
      )}

      {embedHtml && !loading && (
        <div
          style={{
            marginTop: '1rem',

            overflowY: 'auto',
            border: '1px solid #333',
            borderRadius: 4,
          }}
          dangerouslySetInnerHTML={{ __html: embedHtml }}
        />
      )}
    </div>
  );
};

export default XEmbed;
