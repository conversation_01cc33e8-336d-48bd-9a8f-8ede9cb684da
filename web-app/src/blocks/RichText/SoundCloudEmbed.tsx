'use client';

import React, { useMemo } from 'react';
import type { TextFieldClientComponent } from 'payload';
import {
  useField,
  TextInput,
  FieldError,
  FieldDescription,
} from '@payloadcms/ui';

const SoundCloudEmbed: TextFieldClientComponent = ({ path }) => {
  const {
    value = '',
    setValue,
    showError,
  } = useField<string>({ path });

  const embedUrl = useMemo(() => {
    try {
      const url = new URL(value.trim());
      if (url.host.endsWith('soundcloud.com')) {
        const clean = `${url.origin}${url.pathname}`;
        return encodeURIComponent(clean);
      }
    } catch (error) {
      console.error('Invalid SoundCloud URL:', error);
    }
    return null;
  }, [value]);

  const height = embedUrl && /\/sets\//i.test(value) ? 450 : 166;

  return (
    <div style={{ marginBottom: '1.5rem' }}>
      <label
        htmlFor={path}
        style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}
      >
        SoundCloud URL
      </label>

      <TextInput
        path={path}
        value={value}
        placeholder="https://soundcloud.com/artist/track-name"
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setValue(e.target.value)
        }
        aria-invalid={showError}
      />

      {showError && <FieldError path={path} />}
      <FieldDescription path={path} />

      {embedUrl && (
        <div
          style={{
            position: 'relative',
            width: '100%',
            height: `${height}px`,
            marginTop: '1rem',
          }}
        >
          <iframe
            title="SoundCloud Preview"
            width="100%"
            height={height}
            scrolling="no"
            frameBorder="no"
            allow="autoplay"
            src={
              'https://w.soundcloud.com/player/?' +
              `url=${embedUrl}` +
              '&color=%23ff5500&auto_play=false&hide_related=false' +
              '&show_comments=true&show_user=true&show_reposts=false'
            }
            style={{ position: 'absolute', inset: 0 }}
          />
        </div>
      )}
    </div>
  );
};

export default SoundCloudEmbed;
