'use client';

import React, { useMemo } from 'react';
import type { TextFieldClientComponent } from 'payload';
import { useField, TextInput } from '@payloadcms/ui';

const YoutubeEmbed: TextFieldClientComponent = ({ path }) => {
  const {
    value = '',
    setValue,
    showError,
    errorMessage,
  } = useField<string>({ path });

  const videoId = useMemo(() => {
    const m = value.match(
      /(?:youtu\.be\/|youtube\.com\/(?:watch\?v=|embed\/|v\/))([\w-]{11})/
    );
    return m ? m[1] : null;
  }, [value]);

  return (
    <div style={{ marginBottom: '1.5rem' }}>
      <label
        htmlFor={path}
        style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}
      >
        YouTube URL
      </label>

      <TextInput
        path={path}
        value={value}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setValue(e.target.value)
        }
        placeholder="https://youtu.be/QkHaRoapP0A"
        aria-invalid={showError}
      />

      {showError && (
        <p style={{ color: 'red', fontSize: '0.875rem', marginTop: '0.25rem' }}>
          {errorMessage}
        </p>
      )}

      <p style={{ fontSize: '0.875rem', color: '#666', marginTop: '0.25rem' }}>
        Paste any YouTube URL (e.g. https://youtu.be/dQw4w9WgXcQ)
      </p>

      {videoId && (
        <div
          style={{
            position: 'relative',
            paddingBottom: '80vh',
            height: 0,
          }}
        >
          <iframe
            src={`https://www.youtube.com/embed/${videoId}`}
            title="YouTube preview"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '80vh',
              border: 0,
            }}
          />
        </div>
      )}
    </div>
  );
};

export default YoutubeEmbed;
