'use client';

import React, { useMemo } from 'react';
import type { TextFieldClientComponent } from 'payload';
import {
  useField,
  TextInput,
  FieldError,
  FieldDescription,
} from '@payloadcms/ui';

const SpotifyEmbed: TextFieldClientComponent = ({ path }) => {
  const {
    value = '',
    setValue,
    showError,
    errorMessage,
  } = useField<string>({ path });

  // parse type and id
  const embed = useMemo(() => {
    const m = value.match(
      /open\.spotify\.com\/(track|album|playlist|episode)\/([A-Za-z0-9]+)/
    );
    return m ? { type: m[1], id: m[2] } : null;
  }, [value]);

  const height = embed
    ? embed.type === 'track' || embed.type === 'episode'
      ? 80
      : 380
    : 0;

  return (
    <div style={{ marginBottom: '1.5rem' }}>
      <label
        htmlFor={path}
        style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}
      >
        Spotify URL
      </label>

      <TextInput
        path={path}
        value={value}
        placeholder="https://open.spotify.com/track/4uLU6hMCjMI75M1A2tKUQC"
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setValue(e.target.value)
        }
        aria-invalid={showError}
      />

      {showError && <FieldError path={path} message={errorMessage} />}
      <FieldDescription
        path={path}
        description="Paste a Spotify link to a track, album, playlist, or episode."
      />

      {embed && (
        <div
          style={{
            position: 'relative',
            width: '100%',
            height: `${height}px`,
            marginTop: '1rem',
          }}
        >
          <iframe
            title="Spotify preview"
            src={`https://open.spotify.com/embed/${embed.type}/${embed.id}`}
            allow="encrypted-media"
            frameBorder={0}
            style={{
              position: 'absolute',
              inset: 0,
              width: '100%',
              height: '100%',
            }}
          />
        </div>
      )}
    </div>
  );
};

export default SpotifyEmbed;
