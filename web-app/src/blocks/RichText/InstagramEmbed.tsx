'use client';

import React, { useMemo, useEffect } from 'react';
import type { TextFieldClientComponent } from 'payload';
import {
  useField,
  TextInput,
  FieldError,
  FieldDescription,
} from '@payloadcms/ui';

const InstagramEmbed: TextFieldClientComponent = ({ path }) => {
  const { value = '', setValue, showError, errorMessage } = useField<string>({ path });

  const postUrl = useMemo<string | null>(() => {
    try {
      const u = new URL(value.trim());
      const host = u.host.replace(/^www\./, '');
      if (host === 'instagram.com' && /^\/p\/[^/]+\/?$/.test(u.pathname)) {
        return `${u.origin}${u.pathname.replace(/\/?$/, '/')}`;
      }
    } catch {
      // invalid URL
    }
    return null;
  }, [value]);

  // Load Instagram embed script & process
  useEffect(() => {
    if (!postUrl) return;
    if ((window as any).instgrm?.Embeds) {
      (window as any).instgrm.Embeds.process();
    } else {
      const s = document.createElement('script');
      s.src = 'https://www.instagram.com/embed.js';
      s.async = true;
      document.body.appendChild(s);
    }
  }, [postUrl]);

  return (
    <div style={{ marginBottom: '1.5rem' }}>
      <label htmlFor={path} style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>
        Instagram Post URL
      </label>

      <TextInput
        path={path}
        value={value}
        placeholder="https://www.instagram.com/p/XXXXXXXXXXX/"
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setValue(e.target.value)}
        aria-invalid={showError}
      />

      {showError && <FieldError path={path} message={errorMessage} />}
      <FieldDescription path={path} description="Paste an Instagram post URL to embed." />

      {postUrl && (
        <div
          style={{
            marginTop: '1rem',
            overflowY: 'auto',
            borderRadius: 4,
            padding: 8,
          }}
        >
          <blockquote
            className="instagram-media"
            data-instgrm-permalink={postUrl}
            data-instgrm-version="14"
            style={{ margin: 0 }}
          />
        </div>
      )}
    </div>
  );
};

export default InstagramEmbed;
