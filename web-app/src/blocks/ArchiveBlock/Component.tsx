import type { ArchiveBlock as ArchiveBlockProps } from '@/payload-types'

// import configPromise from '@payload-config'
// import { getPayload } from 'payload'
import React from 'react'
import RichText from '@/components/RichText'

// import { CollectionArchive } from '@/components/CollectionArchive'

export const ArchiveBlock: React.FC<
  ArchiveBlockProps & {
    id?: string
  }
> = async (props) => {
  const { id, introContent } = props

  return (
    <div className="my-16" id={`block-${id}`}>
      {introContent && (
        <div className="container mb-16">
          <RichText className="ml-0 max-w-[48rem]" data={introContent} enableGutter={false} />
        </div>
      )}
      {/* <CollectionArchive posts={posts} /> */}
    </div>
  )
}
