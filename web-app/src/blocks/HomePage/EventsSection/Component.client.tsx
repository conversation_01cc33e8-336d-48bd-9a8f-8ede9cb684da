'use client'

import { CenterColumnLayout } from '@/components/CenterColumnLayout'
import { motion } from 'framer-motion'
import { HomeEventsSection as HomeEventsSectionProps, Event } from '@/payload-types'
import { ContentSlider } from '@/components/ContentSlider'
import { EventCard } from '@/components/EventCard'

const HomeEventsClient = (props: HomeEventsSectionProps) => {
  const { title, events } = props
  const upcomingGrayAreaEvents = events.filter((event) => event)
  return (
    <CenterColumnLayout
      className="w-full bg-gray-1"
      as={motion.section}
      layout="position"
      layoutRoot
    >
      <ContentSlider
        title={title}
        items={upcomingGrayAreaEvents as Event[]}
        renderItem={({ item }: { item: Event }) => <EventCard event={item} />}
        fullWidthBackground="gray-1"
        link={{
          label: 'See all',
          href: '/events?grayAreaEvents=true',
        }}
      />
    </CenterColumnLayout>
  )
}

export default HomeEventsClient
