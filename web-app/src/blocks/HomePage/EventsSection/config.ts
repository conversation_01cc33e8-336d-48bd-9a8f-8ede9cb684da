import { Block } from 'payload'

export const HomeEventsSection: Block = {
  slug: 'homeEventsSection',
  interfaceName: 'homeEventsSection',
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      defaultValue: 'Upcoming Gray Area Events',
    },
    {
      name: 'events',
      type: 'relationship',
      relationTo: 'events',
      hasMany: true,
      required: true,
      defaultValue: async ({ req }) => {
        const events = await req.payload.find({
          collection: 'events',
          limit: 1000000,
        })
        return events.docs.map((event) => event)
      },
    },
  ],
}
