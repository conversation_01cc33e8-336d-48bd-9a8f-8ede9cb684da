import { Block } from 'payload'

export const EventsBlock: Block = {
  slug: 'eventsBlock',
  interfaceName: 'eventsBlock',
  fields: [
    {
      name: 'heading',
      type: 'text',
      required: true,
      defaultValue: 'Events',
    },
    {
      name: 'events',
      type: 'relationship',
      relationTo: 'events',
      hasMany: true,
      required: true,
      defaultValue: async ({ req }) => {
        const events = await req.payload.find({
          collection: 'events',
          limit: Infinity,
        })
        return events.docs.map((event) => event)
      },
    },
  ],
}
