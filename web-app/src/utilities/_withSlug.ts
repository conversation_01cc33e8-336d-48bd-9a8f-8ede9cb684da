// src/collections/_withSlug.ts
import type { CollectionConfig } from 'payload'
import { slugField } from '@/fields/slug'

export interface BaseWithSlug extends CollectionConfig {
  /** the field name to derive the slug from (e.g. 'title', 'name', etc.) */
  slugSource: string
}

/**
 * Appends a slug + slugLock checkbox to the end of your fields array
 */
export const withSlug = (conf: BaseWithSlug): CollectionConfig => ({
  ...conf,
  fields: [
    ...conf.fields,
    ...slugField(conf.slugSource, {
      slugOverrides: {
        required: true,
        label: 'Slug',
        admin: {
          description: 'Auto-generated. Uncheck to edit manually.',
          placeholder: 'Will be generated from ' + conf.slugSource,
        },
      },
    }),
  ],
})
