export const getGoogleMapsLink = (
    lat: number,
    lng: number,
    zoom: number = 15,
  ): string => {
    if (!Number.isFinite(lat) || !Number.isFinite(lng)) {
      throw new TypeError("Latitude and longitude must be finite numbers");
    }
    if (lat < -90 || lat > 90) {
      throw new RangeError("Latitude must be between -90 and 90 degrees");
    }
    if (lng < -180 || lng > 180) {
      throw new RangeError("Longitude must be between -180 and 180 degrees");
    }
    const latStr = lat.toString();
    const lngStr = lng.toString();
  
    return `https://www.google.com/maps/search/?api=1&query=${latStr},${lngStr}&zoom=${zoom}`;
  };

