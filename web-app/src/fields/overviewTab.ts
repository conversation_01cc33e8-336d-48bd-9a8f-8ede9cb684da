import type { Tab } from 'payload'

export const overviewTab: Tab = {
  name: 'overview',
  label: 'Overview',
  fields: [
    {
      name: 'overview',
      type: 'group',
      admin: {
        position: 'sidebar',
      },
      fields: [
        {
          name: 'generate_button',
          type: 'ui',         
          admin: {
            position: 'sidebar',
            components: {
              Field: '@/components/OverviewGenerateButton.tsx',
            },
          },
        },
        {
          name: "hero",
          type: "upload",
          relationTo: "media",
        },
        {
          name: "content",
          type: "richText",
        },
      ],
    },
  ],
}
