import type { Tab } from 'payload'
import { PLATFORM_OPTIONS } from './platformOptions'

export const marketingTab: Tab = {
  name: 'marketing',
  label: 'Marketing',
  fields: [
    {
      name: 'trackingLinks',
      type: 'array',
      fields: [
        {
          name: 'platform',
          type: 'select',
          options: PLATFORM_OPTIONS,
          defaultValue: 'dice',
        },
        {
          name: 'channel',
          type: 'text',
          required: true,
        },
        {
          name: 'dealsParams',
          type: 'select',
          options: [
            {
              label: 'Organic',
              value: 'organic',
            },
            {
              label: 'Paid',
              value: 'paid',
            },
          ],
        },
        {
          name: 'campaign',
          type: 'text',
          required: true,
        },
        {
          name: 'link',
          type: 'text',
          required: true,
        },
      ],
    },
  ],
}
