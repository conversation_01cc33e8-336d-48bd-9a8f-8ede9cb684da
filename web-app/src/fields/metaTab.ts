// src/fields/metaTab.ts
import {
  OverviewField,
  MetaTitleField,
  MetaImageField,
  MetaDescriptionField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'
import type { Tab } from 'payload'

export const metaTab: Tab = {
  name: 'meta',
  label: 'SEO',
  fields: [
    {
      name: 'meta',
      type: 'group',
      admin: {
        position: 'sidebar',
      },
      fields: [
        OverviewField({
          titlePath:       'meta.title',
          descriptionPath: 'meta.description',
          imagePath:       'meta.image',
        }),
        MetaTitleField({ hasGenerateFn: true }),
        MetaImageField({ relationTo: 'media' }),
        MetaDescriptionField({}),
        PreviewField({
          hasGenerateFn:    true,
          titlePath:        'meta.title',
          descriptionPath:  'meta.description',
        }),
      ],
    },
  ],
}
