import { Config } from 'payload'
import {
  BoldFeature,
  ItalicFeature,
  LinkFeature,
  ParagraphFeature,
  lexicalEditor,
  UnderlineFeature,
  BlocksFeature,
  BlockquoteFeature,
  UploadFeature,
} from '@payloadcms/richtext-lexical'

export const defaultLexical: Config['editor'] = lexicalEditor({
  features: () => {
    return [
      ParagraphFeature(),
      UnderlineFeature(),
      BoldFeature(),
      ItalicFeature(),
      BlockquoteFeature(),
      UploadFeature({
        maxDepth: 0,
      }),
      LinkFeature({
        enabledCollections: ['pages'],
        fields: ({ defaultFields }) => {
          const defaultFieldsWithoutUrl = defaultFields.filter((field) => {
            if ('name' in field && field.name === 'url') return false
            return true
          })

          return [
            ...defaultFieldsWithoutUrl,
            {
              name: 'url',
              type: 'text',
              admin: {
                condition: ({ linkType }) => linkType !== 'internal',
              },
              label: ({ t }) => t('fields:enterURL'),
              required: true,
            },
          ]
        },
      }),
      BlocksFeature({
        blocks: [
          {
            slug: 'Youtube',
            fields: [
              {
                admin: {
                  components: {
                    Field: '@/blocks/RichText/YoutubeEmbed.tsx',
                  },
                },
                name: 'youtube',
                type: 'text',
              },
            ],
          },
          {
            slug: 'Spotify',
            fields: [
              {
                admin: {
                  components: {
                    Field: '@/blocks/RichText/SpotifyEmbed.tsx',
                  },
                },
                name: 'spotify',
                type: 'text',
              },
            ],
          },
          {
            slug: 'SoundCloud',
            fields: [
              {
                admin: {
                  components: {
                    Field: '@/blocks/RichText/SoundCloudEmbed.tsx',
                  },
                },
                name: 'sound-cloud',
                type: 'text',
              },
            ],
          },
          {
            slug: 'twitter',
            fields: [
              {
                admin: {
                  components: {
                    Field: '@/blocks/RichText/TweetEmbed.tsx',
                  },
                },
                name: 'twitter',
                type: 'text',
              },
            ],
          },
          {
            slug: 'instagram',
            fields: [
              {
                admin: {
                  components: {
                    Field: '@/blocks/RichText/InstagramEmbed.tsx',
                  },
                },
                name: 'instagram',
                type: 'text',
              },
            ],
          },
          {
            slug: 'dailymotion',
            fields: [
              {
                admin: {
                  components: {
                    Field: '@/blocks/RichText/DailymotionEmbed.tsx',
                  },
                },
                name: 'dailymotion',
                type: 'text',
              },
            ],
          },
        ],
      }),
    ]
  },
})
