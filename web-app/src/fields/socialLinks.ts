import { Field } from "payload";

export const socialLinks: Field[] = [
	{
		name: "resource",
		type: "select",
		options: [
			{
				label: "Facebook",
				value: "Facebook",
			},
			{
				label: "Instagram",
				value: "Instagram",
			},
			{
				label: "TikTok",
				value: "TikTok",
			},
			{
				label: "Sound Cloud",
				value: "SoundCloud",
			},
			{
				label: "Discord",
				value: "Discord",
			},
			{
				label: "YouTube",
				value: "YouTube",
			},
			{
				label: "Twitter",
				value: "Twitter",
			},
			{
				label: "Twitch",
				value: "Twitch",
			},
			{
				label: "Pinterest",
				value: "Pinterest",
			},
			{
				label: "Spotify",
				value: "Spotify",
			},
			{
				label: "BeatPort",
				value: "BeatPort",
			},
			{
				label: "Website",
				value: "Website",
			},
			{
				label: "Dice",
				value: "Dice",
			},
			{
				label: "TVMaze",
				value: "TVMaze",
			},
			{
				label: "MusicBrainz",
				value: "MusicBrainz",
			},
			{
				label: "Tunefind",
				value: "Tunefind",
			},
			{
				label: "Line",
				value: "Line",
			},
			{
				label: "Genius",
				value: "Genius",
			},
			{
				label: "Pandora",
				value: "Pandora",
			},
			{
				label: "Shazam",
				value: "Shazam",
			},
			{
				label: "Tidal",
				value: "Tidal",
			},
			{
				label: "LastFm",
				value: "LastFm",
			},
			{
				label: "Deezer",
				value: "Deezer",
			},
			{
				label: "Songkick",
				value: "Songkick",
			},
			{
				label: "Bandsintown",
				value: "Bandsintown",
			},
			{
				label: "Discogs",
				value: "Discogs",
			},
			{
				label: "Itunes",
				value: "Itunes",
			},
			{
				label: "Amazon",
				value: "Amazon",
			},
		],
		required: true,
	},
	{
		name: "link",
		type: "text",
		required: true,
	},
];