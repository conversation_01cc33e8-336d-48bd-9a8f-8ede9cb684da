export function BeatportLogo({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.668 24c-3.857 0-6.935-3.039-6.935-6.974a6.98 6.98 0 011.812-4.714l-4.714 4.714-2.474-2.474 5.319-5.26c.72-.72 1.09-1.656 1.09-2.688V0h3.487v6.604c0 2.026-.72 3.74-2.123 5.143l-.156.156a6.945 6.945 0 014.694-1.812c3.955 0 6.975 3.136 6.975 6.935A6.943 6.943 0 0114.668 24zm0-10.714c-2.123 0-3.779 1.753-3.779 3.74 0 2.045 1.675 3.78 3.78 3.78a3.804 3.804 0 003.818-3.78c0-2.065-1.715-3.74-3.819-3.74Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function DiscordLogo({ className }: { className?: string }) {
  return (
    <svg
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      fill="currentColor"
    >
      <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z" />
    </svg>
  );
}

export function FacebookLogo({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 22 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M22 11.994C22 5.91818 17.0758 0.993958 11 0.993958C4.92422 0.993958 0 5.91818 0 11.994C0 17.4854 4.02187 22.0358 9.28125 22.8608V15.1736H6.48828V11.994H9.28125V9.57052C9.28125 6.81407 10.9227 5.29083 13.4363 5.29083C14.6395 5.29083 15.8984 5.50568 15.8984 5.50568V8.21271H14.5105C13.1441 8.21271 12.7188 9.06134 12.7188 9.93146V11.994H15.7695L15.2818 15.1736H12.7188V22.8608C17.9781 22.0358 22 17.4854 22 11.994Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function GrayAreaLogo({
  variant,
  className,
}: {
  variant?: "mark" | "stacked" | "horizontal";
  className?: string;
}) {
  switch (variant) {
    case "mark":
      return (
        <svg
          className={className || "w-32 text-white"}
          fill="currentColor"
          viewBox="0 0 790 541"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_1_62)">
            <path d="M789.9 270.1L519.9 0L395 124.9L270.1 0L0 270.1L270.1 540.2L395 415.2L519.9 540.1L789.9 270.1ZM270.1 505.7L34.4 270.1L270.1 34.4L377.8 142.1L326.5 193.4H325.8V194.1L289.3 230.6H288.6V231.3L249.8 270.1L377.8 398L270.1 505.7ZM461.6 314.2H328.4L323.8 309.6H466.2L461.6 314.2ZM433.7 342H356.3L351.7 337.4H438.4L433.7 342ZM405.8 369.9H384.1L379.5 365.3H410.5L405.8 369.9ZM401.2 374.5L395 380.7L388.8 374.5H401.2ZM415.1 360.6H374.8L370.2 356H419.8L415.1 360.6ZM424.4 351.3H365.5L360.9 346.7H429L424.4 351.3ZM443 332.7H347L342.4 328.1H447.7L443 332.7ZM452.3 323.4H337.7L333.1 318.8H457L452.3 323.4ZM470.8 304.9H319.1L314.5 300.3H475.5L470.8 304.9ZM480.1 295.6H309.8L305.2 291H484.8L480.1 295.6ZM489.4 286.3H300.5L295.9 281.7H494.1L489.4 286.3ZM498.7 277H291.2L286.6 272.4H503.3L498.7 277ZM286.6 267.7L291.2 263.1H498.7L503.3 267.7H286.6ZM309.8 244.5H480.1L484.7 249.1H305.2L309.8 244.5ZM300.5 253.8H489.4L494 258.4H295.9L300.5 253.8ZM475.5 239.9H314.5L319.1 235.3H470.8L475.5 239.9ZM323.7 230.6L328.3 226H461.5L466.1 230.6H323.7ZM347 207.4H443L447.6 212H342.3L347 207.4ZM337.7 216.7H452.3L456.9 221.3H333L337.7 216.7ZM438.3 202.7H351.6L356.2 198.1H433.6L438.3 202.7ZM360.9 193.4L365.5 188.8H424.4L429 193.4H360.9ZM384.1 170.2H405.8L410.4 174.8H379.4L384.1 170.2ZM374.8 179.5H415.1L419.7 184.1H370.1L374.8 179.5ZM388.8 165.6L395 159.4L401.2 165.6H388.8ZM463.5 346.7H464.4V345.8L491.3 318.9H492.2V318L540 270.2L501.5 231.7V230.8H500.6L464.4 194.6V193.7H463.5L412.2 142.4L519.9 34.4L755.5 270L519.9 505.7L412.2 398L463.5 346.7Z" />
          </g>
          <defs>
            <clipPath id="clip0_1_62">
              <rect width="789.9" height="540.1" fill="white" />
            </clipPath>
          </defs>
        </svg>
      );

    case "horizontal":
      return (
        <svg
          className={className || "w-32 text-white"}
          fill="currentColor"
          viewBox="0 0 960 290"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_1_63)">
            <path d="M423.8 144.9L278.9 0L211.9 67L144.9 0L0 144.9L144.9 289.8L211.9 222.8L278.9 289.8L423.8 144.9ZM144.9 271.3L18.5 144.9L144.9 18.5L202.7 76.3L175.2 103.8H174.8V104.2L155.2 123.8H154.8V124.2L134 144.9L202.6 213.5L144.9 271.3ZM247.6 168.5H176.2L173.7 166H250.1L247.6 168.5ZM232.7 183.5H191.2L188.7 181H235.2L232.7 183.5ZM217.7 198.4H206L203.5 195.9H220.1L217.7 198.4ZM215.2 200.9L211.9 204.2L208.6 200.9H215.2ZM222.7 193.4H201.1L198.6 190.9H225.2L222.7 193.4ZM227.7 188.5H196.1L193.6 186H230.2L227.7 188.5ZM237.6 178.5H186.1L183.6 176H240.1L237.6 178.5ZM242.6 173.5H181.1L178.6 171H245.1L242.6 173.5ZM252.6 163.6H171.2L168.7 161.1H255.1L252.6 163.6ZM257.6 158.6H166.2L163.7 156.1H260L257.6 158.6ZM262.5 153.6H161.2L158.7 151.1H265L262.5 153.6ZM267.5 148.6H156.2L153.7 146.1H270L267.5 148.6ZM153.8 143.6L156.3 141.1H267.6L270.1 143.6H153.8ZM166.2 131.2H257.6L260.1 133.7H163.8L166.2 131.2ZM161.2 136.2H262.5L265 138.7H158.7L161.2 136.2ZM255.1 128.7H168.7L171.2 126.2H252.6L255.1 128.7ZM173.7 123.7L176.2 121.2H247.6L250.1 123.7H173.7ZM186.1 111.2H237.6L240.1 113.7H183.6L186.1 111.2ZM181.2 116.2H242.7L245.2 118.7H178.7L181.2 116.2ZM235.2 108.8H188.7L191.2 106.3H232.7L235.2 108.8ZM193.6 103.8L196.1 101.3H227.7L230.2 103.8H193.6ZM206.1 91.3H217.8L220.3 93.8H203.7L206.1 91.3ZM201.1 96.3H222.7L225.2 98.8H198.6L201.1 96.3ZM208.6 88.8L211.9 85.5L215.2 88.8H208.6ZM248.6 186H249.1V185.5L263.5 171.1H264V170.6L289.7 144.9L269 124.2V123.7H268.5L249.1 104.3V103.8H248.6L221.1 76.3L278.9 18.5L405.3 144.9L278.9 271.3L221.1 213.5L248.6 186Z" />
            <path d="M527.7 256.9H477.7L468.8 279.8H445.4L492 158.5H513.4L560 279.8H536.5L527.7 256.9ZM502.7 186L485.6 235.6H519.9L502.7 186Z" />
            <path d="M925.9 256.9H875.9L867 279.8H843.6L890.2 158.5H911.6L958.2 279.8H934.7L925.9 256.9ZM900.9 186L883.8 235.6H918.1L900.9 186Z" />
            <path d="M694 279.8H666.8L631.4 239.2H609.4V279.8H586.5V158.3C605.7 158.3 625 158.5 644.2 158.5C672.8 158.7 687.9 177.7 687.9 198.7C687.9 215.3 680.3 232.1 657.2 236.8L693.9 278.2V279.8H694ZM609.4 179.8V218.6H644.2C658.8 218.6 665 208.9 665 199.2C665 189.5 658.6 179.8 644.2 179.8H609.4Z" />
            <path d="M814 279.8H723.2C723.2 239.4 723.2 198.9 723.2 158.5H814V180.7H745.9V208.6H811.6V229.9H745.9V257.3H814V279.8Z" />
            <path d="M540.1 35.6C532 27.8 519.8 23.5 509.2 23.5C483.2 23.5 467.4 43.3 467.4 68.2C467.4 88.1 479 108.8 509.2 108.8C518.7 108.8 527.1 106.7 536.6 99.1V77.5H505.6V57.1H557.6V108.2C545.6 121.9 530.6 130 509.2 130C463.6 130 445.1 100 445.1 68.1C445.1 34 466.4 2.39999 509.2 2.39999C525.5 2.39999 541.8 8.59999 554.3 20.9L540.1 35.6Z" />
            <path d="M694 127.3H666.8L631.4 86.7H609.4V127.3H586.5V5.79999C605.7 5.79999 625 5.99999 644.2 5.99999C672.9 6.09999 688 25.2 688 46.1C688 62.7 680.4 79.5 657.3 84.2L694 125.6V127.3ZM609.4 27.3V66.1H644.2C658.8 66.1 665 56.4 665 46.7C665 37 658.6 27.3 644.2 27.3H609.4Z" />
            <path d="M900.1 56.1L932.3 6.10001H959.8V7.10001L911.5 77.2V127.2H888.6V77.2L842.1 7.10001V6.10001H869.3L900.1 56.1Z" />
            <path d="M796.3 104.5H746.3L737.4 127.4H714L760.6 6H782L828.6 127.3H805L796.3 104.5ZM771.3 33.5L754.2 83.1H788.5L771.3 33.5Z" />
          </g>
          <defs>
            <clipPath id="clip0_1_63">
              <rect width="959.8" height="289.7" fill="white" />
            </clipPath>
          </defs>
        </svg>
      );

    case "stacked":
    default:
      return (
        <svg
          className={className || "w-32 text-white"}
          fill="currentColor"
          viewBox="0 0 509 372"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_2_2)">
            <path d="M466.2 144.9L321.3 0L254.3 67L187.3 0L42.4 144.9L187.3 289.8L254.3 222.8L321.3 289.8L466.2 144.9ZM187.3 271.3L60.9 144.9L187.3 18.5L245.1 76.3L217.6 103.8H217.2V104.2L197.6 123.8H197.2V124.2L176.4 145L245 213.6L187.3 271.3ZM290 168.5H218.6L216.1 166H292.5L290 168.5ZM275.1 183.5H233.6L231.1 181H277.6L275.1 183.5ZM260.2 198.4H248.5L246 195.9H262.6L260.2 198.4ZM257.7 200.9L254.4 204.2L251.1 200.9H257.7ZM265.1 193.4H243.5L241 191H267.6L265.1 193.4ZM270.1 188.5H238.5L236 186H272.6L270.1 188.5ZM280.1 178.5H228.6L226.1 176H282.6L280.1 178.5ZM285.1 173.5H223.6L221.1 171H287.6L285.1 173.5ZM295 163.6H213.6L211.1 161.1H297.5L295 163.6ZM300 158.6H208.6L206.1 156.1H302.4L300 158.6ZM305 153.6H203.7L201.2 151.1H307.5L305 153.6ZM310 148.6H198.7L196.2 146.1H312.5L310 148.6ZM196.2 143.6L198.7 141.1H310L312.5 143.6H196.2ZM208.6 131.2H300L302.5 133.7H206.2L208.6 131.2ZM203.7 136.2H305L307.5 138.7H201.2L203.7 136.2ZM297.5 128.7H211.1L213.6 126.2H295L297.5 128.7ZM216.1 123.7L218.6 121.2H290L292.5 123.7H216.1ZM228.6 111.2H280.1L282.6 113.7H226.1L228.6 111.2ZM223.6 116.2H285.1L287.6 118.7H221.1L223.6 116.2ZM277.6 108.8H231.1L233.6 106.3H275.1L277.6 108.8ZM236 103.8L238.5 101.3H270.1L272.6 103.8H236ZM248.5 91.3H260.2L262.7 93.8H246L248.5 91.3ZM243.5 96.3H265.1L267.6 98.8H241L243.5 96.3ZM251 88.8L254.3 85.5L257.6 88.8H251ZM291.1 186H291.6V185.5L306 171H306.5V170.5L332.2 144.8L311.5 124.1V123.6H311L291.6 104.2V103.7H291.1L263.6 76.2L321.4 18.4L447.8 144.8L321.3 271.3L263.5 213.5L291.1 186Z" />
            <path d="M308.8 359.2H285.6L281.5 369.8H270.6L292.2 313.5H302.1L323.7 369.8H312.8L308.8 359.2ZM297.2 326.3L289.2 349.3H305.1L297.2 326.3Z" />
            <path d="M493.7 359.2H470.5L466.4 369.8H455.5L477.1 313.5H487L508.6 369.8H497.7L493.7 359.2ZM482.1 326.3L474.1 349.3H490L482.1 326.3Z" />
            <path d="M387.4 369.8H374.8L358.3 351H348.1V369.8H337.5V313.4C346.4 313.4 355.4 313.5 364.3 313.5C377.6 313.6 384.6 322.4 384.6 332.2C384.6 339.9 381.1 347.7 370.4 349.9L387.5 369.1V369.8H387.4ZM348.1 323.4V341.4H364.3C371.1 341.4 374 336.9 374 332.4C374 327.9 371 323.4 364.3 323.4H348.1Z" />
            <path d="M441.7 369.8H399.5C399.5 351 399.5 332.2 399.5 313.5H441.7V323.8H410.1V336.8H440.6V346.7H410.1V359.4H441.7V369.8Z" />
            <path d="M44.1 327.2C40.3 323.6 34.7 321.6 29.8 321.6C17.7 321.6 10.4 330.8 10.4 342.4C10.4 351.7 15.8 361.2 29.8 361.2C34.2 361.2 38.1 360.2 42.5 356.7V346.7H28.1V337.2H52.2V360.9C46.6 367.3 39.6 371 29.7 371C8.6 371.1 0 357.1 0 342.3C0 326.4 9.9 311.8 29.8 311.8C37.4 311.8 44.9 314.7 50.7 320.4L44.1 327.2Z" />
            <path d="M115.6 369.8H103L86.6 351H76.3V369.8H65.7V313.4C74.6 313.4 83.6 313.5 92.5 313.5C105.8 313.6 112.8 322.4 112.8 332.2C112.8 339.9 109.3 347.7 98.6 349.9L115.7 369.1V369.8H115.6ZM76.3 323.3V341.3H92.5C99.3 341.3 102.2 336.8 102.2 332.3C102.2 327.8 99.2 323.3 92.5 323.3H76.3Z" />
            <path d="M210.6 336.7L225.6 313.5H238.4V314L216 346.6V369.8H205.4V346.6L183.7 314V313.5H196.3L210.6 336.7Z" />
            <path d="M163 359.2H139.8L135.7 369.8H124.8L146.4 313.5H156.3L177.9 369.8H167L163 359.2ZM151.4 326.3L143.4 349.3H159.3L151.4 326.3Z" />
          </g>
          <defs>
            <clipPath id="clip0_2_2">
              <rect width="508.7" height="371.1" fill="white" />
            </clipPath>
          </defs>
        </svg>
      );
  }
}

export function GrayAreaHousekeysLogo({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 776 603"
      className={className}
    >
      <path
        fill="currentColor"
        d="M491.101 147.451c-1.202 1.116-1.962 2.378-2.987 2.646-1.808.474-3.775.442-5.679.442-56.742.019-113.484.019-170.221 0-1.899 0-3.866.019-5.679-.451-1.02-.264-1.785-1.517-2.996-2.633 1.425-.455 2.185-.916 2.941-.916 60.559-.031 121.121-.031 181.68.005.765 0 1.525.451 2.941.907ZM490.573 141.204c-1.653.492-2.632.962-3.643 1.044-1.899.159-3.812.123-5.72.123-55.899.004-111.803.018-167.703-.032-2.919-.005-6.006.729-9.126-1.367.788-.82 1.366-1.968 2.168-2.15 1.83-.419 3.77-.497 5.665-.497 56.719-.023 113.438-.023 170.158 0 1.894 0 3.829.078 5.665.488.806.182 1.411 1.289 2.536 2.391ZM458.218 180.27c-1.772 1.59-2.423 2.674-3.243 2.815-2.127.365-4.322.424-6.494.424-34.036.023-68.072.023-102.108 0-2.168 0-4.363-.05-6.499-.378-1.357-.205-2.504-1.048-1.93-3.235 1.124-.155 2.395-.451 3.675-.492 23.53-.793 108.251-.296 116.599.866ZM484.043 155.569c-2.314 1.508-3.279 2.546-4.413 2.783-1.84.383-3.789.315-5.692.315-51.237.013-102.473.013-153.709-.005-1.899 0-3.871.068-5.674-.405-1.002-.265-1.722-1.586-2.842-2.707 6.958-1.029 162.407-1.129 172.33.019ZM483.651 133.569c-4.326.301-6.457.579-8.584.574-53.668-.077-107.336-.182-161.004-.328-.396 0-.788-.61-1.535-1.225.77-.715 1.421-1.85 2.232-1.977 2.399-.374 4.863-.469 7.304-.469 50.134-.023 100.264-.023 150.398 0 2.441 0 4.9.095 7.309.441.834.119 1.54 1.13 3.88 2.984ZM345.198 187.951c1.69-.151 3.283-.419 4.877-.419 31.322-.023 62.64-.028 93.962-.01 3.998 0 5.924.752 5.323 1.891-1.239 2.365-3.552 2.169-5.701 2.264-1.357.06-2.724.028-4.085.028-28.326 0-56.651.013-84.977-.023-2.436-.005-4.872-.269-7.3-.488-.869-.077-.86-.182-2.099-3.243ZM319.805 163.91c7.309-1.139 147.11-1.157 154.725-.022-1.23 1.084-2.009 2.337-2.988 2.519-2.113.392-4.33.296-6.507.296-45.207.009-90.414.009-135.616 0-2.177 0-4.395.118-6.508-.273-1.02-.187-1.84-1.44-3.106-2.52ZM320.138 124.28c.942-.738 1.671-1.763 2.536-1.9 2.132-.341 4.331-.36 6.499-.36 45.261-.018 90.518-.013 135.78-.004 2.177 0 4.376-.028 6.526.264.906.123 1.707 1.03 2.4 1.481-.287 1.981-1.558 1.735-2.496 1.826-1.352.137-2.723.073-4.085.073-46.623.005-93.246.014-139.869-.036-2.3-.005-4.819.738-7.291-1.344ZM328.631 117.283c.378-2.697 1.553-3.331 2.973-3.431 2.173-.146 4.354-.137 6.535-.137 39.241-.004 78.483-.009 117.724.005 2.445 0 4.909.027 7.327.319.875.105 1.653 1.02 2.295 1.449-.286 2.004-1.53 1.909-2.468 1.968-2.172.136-4.358.118-6.535.118-39.787.005-79.571.009-119.358-.009-2.683.005-5.365-.173-8.493-.282ZM328.48 172.716c1.289-.665 1.918-1.23 2.601-1.307 7.004-.784 129.108-.201 133.94.583.779 1.69-.228 2.56-1.626 2.774-2.135.324-4.326.433-6.494.438-39.787.022-79.58.022-119.367 0-2.168 0-4.358-.11-6.485-.47-.811-.136-1.471-1.116-2.569-2.018ZM336.327 108.267c1.799-1.363 2.523-2.338 3.384-2.474 2.131-.337 4.326-.383 6.493-.383 34.027-.018 68.059-.018 102.086 0 2.168 0 4.363.032 6.494.369.861.137 1.589 1.13 3.083 2.278-1.685.638-2.537 1.185-3.429 1.253-2.168.164-4.349.132-6.526.132-33.754.005-67.512.014-101.266-.014-2.951-.004-5.961.378-10.319-1.161ZM344.433 99.806c2.026-1.266 2.81-2.113 3.689-2.236 2.14-.292 4.33-.328 6.498-.328 28.558-.019 57.12-.019 85.678.004 2.163 0 4.353.073 6.48.424.842.141 1.539 1.139 2.942 2.255-1.735.592-2.646 1.148-3.575 1.18-3.803.132-7.614.132-11.421.132a51990.5 51990.5 0 0 1-79.967-.014c-2.938-.004-5.957.433-10.324-1.416ZM351.473 196.771c8.981-1.276 62.353-1.736 86.179-.98 1.284.041 2.564.369 4.886.72-1.744 1.403-2.618 2.643-3.707 2.87-2.108.442-4.33.406-6.507.41-23.476.023-46.956.019-70.431.005-1.909 0-3.853.068-5.707-.296-1.156-.232-2.167-1.212-4.713-2.729ZM353.668 90.923c.802-.579 1.544-1.522 2.414-1.658 2.136-.342 4.331-.41 6.503-.415 23.193-.027 46.386-.027 69.584 0 2.172.005 4.367.1 6.498.456.861.145 1.581 1.102 3.338 2.423-2.582.565-3.998 1.13-5.423 1.144-9.003.09-18.011.059-27.014.059-15.825.005-31.655.018-47.48-.014-2.673-.004-5.346-.214-8.019-.332-.137-.552-.269-1.107-.401-1.663ZM426.117 212.392c-1.986 2.069-2.528 3.126-3.311 3.349-1.539.437-3.206.542-4.822.547-13.904.036-27.807.054-41.71-.014-2.277-.009-4.982.624-6.357-2.601.61-.438 1.193-1.226 1.776-1.231 17.432-.063 34.86-.05 54.424-.05ZM361.834 204.406h72.129c-1.53 1.526-2.204 2.738-3.151 3.012-1.526.446-3.233.35-4.868.355-19.104.018-38.203.018-57.307 0-1.63 0-3.306.023-4.877-.324-.897-.2-1.63-1.134-2.437-1.735.169-.438.342-.875.511-1.308ZM361.401 82.75c1.111-.56 2.177-1.453 3.342-1.613 2.141-.291 4.34-.191 6.517-.191 17.419-.01 34.842-.01 52.261 0 2.177 0 4.381-.1 6.512.223 1.148.173 2.172 1.153 3.452 1.882-.847.724-1.193 1.28-1.58 1.307-8.985.706-64.516.314-70.504-1.608ZM416.298 221.454c-.637.984-.979 2.159-1.707 2.496-1.166.542-2.592.716-3.908.725-8.962.054-17.924.059-26.891-.005-1.316-.009-2.732-.218-3.907-.761-.724-.337-1.07-1.48-1.712-2.46 7.09-1.348 31.103-1.348 38.125.005ZM425.128 75.475c-1.225.414-1.967.879-2.709.884-16.863.054-33.727.068-50.59.04-.742 0-1.48-.5-2.709-.947.951-.948 1.553-2.041 2.4-2.282 1.53-.433 3.206-.47 4.818-.474 13.871-.032 27.747-.032 41.618 0 1.617.004 3.297.045 4.832.478.833.237 1.416 1.349 2.34 2.3ZM416.107 67.488c-7.814 1.258-31.335 1.157-37.898-.195.693-.93 1.103-1.969 1.854-2.365.902-.474 2.09-.528 3.156-.533 9.477-.036 18.953-.036 28.425 0 1.89.014 3.912.014 4.463 3.093ZM407.755 229.658c.392 1.768-.901 2.688-2.131 2.725a283.47 283.47 0 0 1-16.927-.018c-1.261-.041-2.509-.989-2.058-2.757 6.357-1.426 15.023-1.407 21.116.05ZM408.334 58.928c-6.813 1.567-14.741 1.595-22 .087 1.129-1.044 1.926-2.433 2.782-2.465a215.035 215.035 0 0 1 16.171-.06c.934.033 1.817 1.413 3.047 2.438ZM393.766 237.868c1.398-.487 2.072-.893 2.764-.92 1.27-.05 2.55.127 3.821.209.168 2.574-.993 4.064-3.042 3.804-1.157-.145-2.118-1.781-3.543-3.093ZM400.974 50.97c-2.026.464-2.818.856-3.538.756-.943-.132-1.867-.588-2.71-1.062-.15-.086-.013-1.047.246-1.43.788-1.162 1.908-2.005 3.307-1.217.801.447 1.32 1.4 2.695 2.953Z"
      />
      <path
        fill="currentColor"
        d="M397.258 419.946 291.921 314.573l64.794-64.819-104.559-104.6L397.258 0l145.098 145.154-104.554 104.6 64.794 64.819-105.338 105.373Zm-78.013-105.373 78.013 78.044 78.014-78.044-64.798-64.819 104.558-104.6-117.774-117.82-117.773 117.82 104.558 104.6-64.798 64.819ZM5.898 550.609H9.64v22.523h30.211v-22.523h3.743v51.452h-3.743v-25.762H9.642v25.762H5.897v-51.452ZM98.466 576.299c0-15.257 9.062-26.701 23.02-26.701s23.161 11.439 23.161 26.701c0 15.252-9.208 26.701-23.161 26.701-13.958 0-23.02-11.449-23.02-26.701Zm42.297 0c0-12.738-6.763-23.316-19.282-23.316-12.514 0-19.135 10.578-19.135 23.316s6.617 23.316 19.135 23.316c12.519 0 19.282-10.578 19.282-23.316ZM199.09 585.871v-35.257h3.738v34.974c0 10.073 5.251 13.963 14.6 13.963 8.849 0 14.6-3.886 14.6-13.963v-34.974h3.744v35.257c0 11.658-7.41 16.983-18.562 16.983-11.217 0-18.12-5.612-18.12-16.983ZM289.421 585.797h3.812c.36 8.706 6.617 13.891 16.185 13.891 7.842 0 13.812-4.246 13.812-11.513 0-7.412-3.958-9.284-16.185-11.585-9.782-1.795-15.752-4.82-15.752-13.385 0-8.059 6.904-13.603 17.045-13.603 10.429 0 16.472 5.9 17.192 14.319h-3.739c-.72-7.194-6.189-11.007-13.38-11.007-8.415 0-13.306 4.314-13.306 10.219 0 6.332 4.098 8.346 14.244 10.218 10.001 1.872 17.77 4.246 17.77 14.537 0 8.779-6.977 15.112-17.624 15.112-12.878 0-19.495-7.344-20.074-17.203ZM381.994 550.609h34.095v3.307h-30.357v20.005h27.911v3.239h-27.911v21.443h31.076v3.454h-34.814v-51.448ZM471.825 550.609h3.743v28.71l29.418-28.783h4.964l-21.941 21.23 22.948 30.295h-4.532l-21.075-27.708-9.782 9.572v18.132h-3.743v-51.448ZM563.887 550.609h34.095v3.307h-30.356v20.005h27.911v3.239h-27.911v21.443h31.076v3.454h-34.815v-51.448ZM668.177 579.178l-18.562-28.569h4.098l16.331 25.184h.141l16.33-25.184h4.031l-18.631 28.71v22.738h-3.738v-22.879ZM738.016 585.797h3.816c.36 8.706 6.617 13.891 16.185 13.891 7.837 0 13.812-4.246 13.812-11.513 0-7.412-3.958-9.284-16.185-11.585-9.782-1.795-15.757-4.82-15.757-13.385 0-8.059 6.904-13.603 17.05-13.603 10.429 0 16.472 5.9 17.192 14.319h-3.744c-.719-7.194-6.184-11.007-13.379-11.007-8.416 0-13.307 4.314-13.307 10.219 0 6.332 4.099 8.346 14.24 10.218 9.996 1.872 17.77 4.246 17.77 14.537 0 8.779-6.977 15.112-17.624 15.112-12.874 0-19.495-7.344-20.069-17.203ZM471.154 513.298h-35.398l-6.255 16.168H412.87l32.956-85.873h15.105l32.957 85.873h-16.631l-6.103-16.168Zm-17.699-50.182-12.206 35.082h24.259l-12.053-35.082ZM753.266 513.298h-35.397l-6.256 16.168h-16.631l32.957-85.873h15.105L776 529.466h-16.631l-6.103-16.168Zm-17.698-50.182-12.207 35.082h24.26l-12.053-35.082ZM591.078 529.467h-19.224l-25.175-28.676h-15.563v28.676h-16.173V443.44c13.579 0 27.311.153 40.89.153 20.293.152 30.973 13.575 30.973 28.523 0 11.745-5.34 23.642-21.666 26.997l26.091 29.286v1.068h-.153Zm-59.962-70.774v27.455h24.717c10.375 0 14.8-6.863 14.8-13.727s-4.577-13.728-14.8-13.728h-24.717ZM673.927 529.466H609.54v-85.873h64.387v15.71h-48.214v19.829h46.536v15.1h-46.536v19.371h48.214v15.863ZM67.286 464.489c-5.798-5.491-14.342-8.541-21.819-8.541-18.461 0-29.6 14.032-29.6 31.726 0 14.185 8.24 28.675 29.6 28.675 6.714 0 12.664-1.525 19.378-6.864v-15.252H42.874v-14.491h36.77v36.15c-8.544 9.761-19.224 15.405-34.329 15.405C13.121 531.449 0 510.095 0 487.521 0 463.269 15.105 441 45.468 441c11.595 0 23.038 4.423 31.888 13.117l-10.07 10.372ZM176.377 529.467h-19.224l-25.022-28.676h-15.716v28.676h-16.173V443.44c13.579 0 27.311.153 40.89.153 20.293.152 30.973 13.575 30.973 28.523 0 11.745-5.34 23.642-21.665 26.997l26.09 29.286v1.068h-.153Zm-59.962-70.926v27.455h24.717c10.376 0 14.8-6.864 14.8-13.728 0-6.863-4.577-13.727-14.8-13.727h-24.717ZM321.325 478.98l22.886-35.387h19.53v.763l-34.177 49.724v35.387h-16.173V494.08l-33.109-49.724v-.763h19.224l21.819 35.387ZM248.698 513.299h-35.397l-6.256 16.168h-16.63l32.956-85.874h15.105l32.956 85.874h-16.631l-6.103-16.168ZM231 463.117l-12.206 35.082h24.259L231 463.117Z"
      />
    </svg>
  );
}

export function InstagramLogo({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.45455 2C4.44208 2 2 4.44208 2 7.45455V16.5455C2 19.5579 4.44208 22 7.45455 22H16.5455C19.5579 22 22 19.5579 22 16.5455V7.45455C22 4.44208 19.5579 2 16.5455 2H7.45455ZM19.7273 5.63636C19.7273 6.38948 19.1168 7 18.3636 7C17.6105 7 17 6.38948 17 5.63636C17 4.88325 17.6105 4.27273 18.3636 4.27273C19.1168 4.27273 19.7273 4.88325 19.7273 5.63636ZM12 8.36364C9.99169 8.36364 8.36364 9.99169 8.36364 12C8.36364 14.0083 9.99169 15.6364 12 15.6364C14.0083 15.6364 15.6364 14.0083 15.6364 12C15.6364 9.99169 14.0083 8.36364 12 8.36364ZM6.54545 12C6.54545 8.98754 8.98754 6.54545 12 6.54545C15.0125 6.54545 17.4545 8.98754 17.4545 12C17.4545 15.0125 15.0125 17.4545 12 17.4545C8.98754 17.4545 6.54545 15.0125 6.54545 12Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function SoundCloudLogo({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.25 11.25C19.983 11.25 19.7205 11.2785 19.4685 11.3325C19.131 7.923 16.2465 5.25 12.75 5.25C12.336 5.25 12 5.586 12 6V18C12 18.414 12.336 18.75 12.75 18.75H20.25C22.3185 18.75 24 17.0685 24 15C24 12.933 22.3185 11.25 20.25 11.25ZM9.75 6.75C9.336 6.75 9 7.086 9 7.5V18C9 18.414 9.336 18.75 9.75 18.75C10.164 18.75 10.5 18.414 10.5 18V7.5C10.5 7.086 10.164 6.75 9.75 6.75ZM6 10.5C6 10.086 6.336 9.75 6.75 9.75C7.164 9.75 7.5 10.086 7.5 10.5V18C7.5 18.414 7.164 18.75 6.75 18.75C6.336 18.75 6 18.414 6 18V10.5ZM3.75 9.75C3.336 9.75 3 10.086 3 10.5V18C3 18.414 3.336 18.75 3.75 18.75C4.164 18.75 4.5 18.414 4.5 18V10.5C4.5 10.086 4.164 9.75 3.75 9.75ZM0 12.75C0 12.336 0.336 12 0.75 12C1.164 12 1.5 12.336 1.5 12.75V17.25C1.5 17.664 1.164 18 0.75 18C0.336 18 0 17.664 0 17.25V12.75Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function GoogleLogo({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="300"
      height="300"
      fill="currentColor"
      className={className}
      viewBox="0 0 16 16"
      id="IconChangeColor"
    >
      <path
        d="M15.545 6.558a9.42 9.42 0 0 1 .139 1.626c0 2.434-.87 4.492-2.384 5.885h.002C11.978 15.292 10.158 16 8 16A8 8 0 1 1 8 0a7.689 7.689 0 0 1 5.352 2.082l-2.284 2.284A4.347 4.347 0 0 0 8 3.166c-2.087 0-3.86 1.408-4.492 3.304a4.792 4.792 0 0 0 0 3.063h.003c.635 1.893 2.405 3.301 4.492 3.301 1.078 0 2.004-.276 2.722-.764h-.003a3.702 3.702 0 0 0 1.599-2.431H8v-3.08h7.545z"
        id="mainIconPathAttribute"
        fill="#ffffff"
      ></path>
    </svg>
  );
}

export function SpotifyLogo({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 22C17.072 22 22 17.072 22 11C22 4.928 17.072 0 11 0C4.928 0 0 4.928 0 11C0 17.072 4.928 22 11 22ZM15.466 16.1847V16.1856C14.7262 16.1856 12.3897 13.5933 5.82267 14.9389C5.64942 14.9838 5.423 15.0544 5.29467 15.0544C4.45592 15.0544 4.2955 13.7995 5.1975 13.6079C8.83025 12.8058 12.5427 12.8764 15.7089 14.7703C16.4642 15.2524 16.1434 16.1847 15.466 16.1847ZM16.6604 13.2761C16.5568 13.2486 16.5871 13.3393 16.1132 13.09C13.3402 11.4492 9.20792 10.7873 5.53025 11.7856C5.31758 11.8433 5.20208 11.9011 5.00225 11.9011C4.0205 11.9011 3.76017 10.4243 4.82992 10.1228C9.15292 8.90817 13.7903 9.61675 17.0188 11.5372C17.3782 11.7498 17.5203 12.0258 17.5203 12.4107C17.5157 12.8892 17.1435 13.2761 16.6604 13.2761ZM4.169 6.41483C8.31508 5.20117 14.5897 5.58433 18.491 7.86133C19.4911 8.43792 19.0978 9.89633 18.0345 9.89633L18.0336 9.89542C17.8026 9.89542 17.6605 9.83767 17.4607 9.72217C14.3046 7.8375 8.657 7.38558 5.00317 8.40583C4.84275 8.44983 4.64292 8.52042 4.43025 8.52042C3.8445 8.52042 3.39717 8.063 3.39717 7.47358C3.39717 6.87133 3.77025 6.53033 4.169 6.41483Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function TikTokLogo({ className }: { className?: string }) {
  return (
    <svg
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      fill="currentColor"
    >
      <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z" />
    </svg>
  );
}

export function TwitchLogo({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.1748 7.47926H12V12.9568H10.1748V7.47926Z"
        fill="currentColor"
      />
      <path
        d="M17.0207 7.47926H15.1946V12.9568H17.0207V7.47926Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.95703 5.65313V20.2604H6.97768V23H9.71899L12.4559 20.2595H16.5649L22.044 14.7837V2H3.3255L1.95703 5.65313ZM5.15159 3.82438H20.2179V13.8685L17.0216 17.064H11.9992L9.26225 19.8001V17.064H5.15159V3.82438Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function TwitterLogo({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M24 4.36769C23.1075 4.76923 22.1565 5.03538 21.165 5.16462C22.185 4.54 22.9635 3.55846 23.3295 2.37538C22.3785 2.95692 21.3285 3.36769 20.2095 3.59692C19.3065 2.61077 18.0195 2 16.6155 2C13.8915 2 11.6985 4.26769 11.6985 7.04769C11.6985 7.44769 11.7315 7.83231 11.8125 8.19846C7.722 7.99385 4.1025 5.98308 1.671 2.92C1.2465 3.67538 0.9975 4.54 0.9975 5.47077C0.9975 7.21846 1.875 8.76769 3.183 9.66462C2.3925 9.64923 1.617 9.41385 0.96 9.04308C0.96 9.05846 0.96 9.07846 0.96 9.09846C0.96 11.5508 2.6655 13.5877 4.902 14.0569C4.5015 14.1692 4.065 14.2231 3.612 14.2231C3.297 14.2231 2.979 14.2046 2.6805 14.1369C3.318 16.1354 5.127 17.6046 7.278 17.6523C5.604 18.9954 3.4785 19.8046 1.1775 19.8046C0.774 19.8046 0.387 19.7862 0 19.7354C2.1795 21.1769 4.7625 22 7.548 22C16.602 22 21.552 14.3077 21.552 7.64C21.552 7.41692 21.5445 7.20154 21.534 6.98769C22.5105 6.27692 23.331 5.38923 24 4.36769Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function YouTubeLogo({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M23.4979 6.62317C23.3612 6.11457 23.0931 5.65083 22.7207 5.27842C22.3483 4.90601 21.8846 4.63799 21.376 4.50122C19.5046 4 12 4 12 4C12 4 4.49537 4 2.62378 4.50146C2.11518 4.63824 1.65145 4.90625 1.27903 5.27867C0.906619 5.65108 0.638603 6.11481 0.501829 6.62341C-1.19209e-07 8.49475 0 12.3996 0 12.3996C0 12.3996 5.96046e-08 16.3045 0.501585 18.176C0.638359 18.6846 0.906376 19.1483 1.27879 19.5207C1.6512 19.8931 2.11493 20.1611 2.62354 20.2979C4.49512 20.7994 11.9998 20.7994 11.9998 20.7994C11.9998 20.7994 19.5044 20.7994 21.376 20.2979C21.8846 20.1611 22.3483 19.8931 22.7207 19.5207C23.0931 19.1483 23.3612 18.6846 23.4979 18.176C24 16.3045 24 12.3996 24 12.3996C24 12.3996 24 8.49475 23.4979 6.62317ZM9.88573 16.0163V8.8167L16.1206 12.4167L9.88573 16.0163Z"
        fill="currentColor"
      />
    </svg>
  );
}
