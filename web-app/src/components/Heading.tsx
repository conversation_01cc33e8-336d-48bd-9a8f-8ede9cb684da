import { cn } from '@/lib/utils'

export interface HeadingProps {
  title: string
  level: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  text?: string
  className?: string
  headingClassName?: string
}

// Conditional styles based on the heading level
const headingStyles = {
  h1: 'text-4xl sm:text-5xl',
  h2: 'text-2xl sm:text-3xl',
  h3: 'text-lg sm:text-2xl',
  h4: 'text-base sm:text-lg',
  h5: 'text-sm sm:text-base',
  h6: 'text-xs sm:text-sm',
}

export function Heading({ title, level, text, className, headingClassName }: HeadingProps) {
  const HeadingTag = level

  return (
    <div className={cn('mb-5 space-y-4 md:mb-8', className)}>
      <HeadingTag
        className={cn('font-semibold text-white', headingStyles[level], headingClassName)}
      >
        {title}
      </HeadingTag>
      {text ? <p className="!leading-relaxed text-gray-4 sm:text-lg">{text}</p> : null}
    </div>
  )
}
