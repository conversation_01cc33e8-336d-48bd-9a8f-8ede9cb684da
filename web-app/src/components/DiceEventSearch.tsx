'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import type { TextFieldClientComponent } from 'payload'
import {
  useField,
  useAllFormFields,
  TextInput,
  FieldLabel,
  FieldError,
  FieldDescription,
} from '@payloadcms/ui'
import type { Event } from '@/lib/dice/schema-types'

const AutocompleteField: TextFieldClientComponent = ({ path }) => {
  const { value = '', setValue, showError, errorMessage } = useField<string>({ path })
  const [, dispatchFields] = useAllFormFields()
  const [query, setQuery] = useState(value)
  const [suggestions, setSuggestions] = useState<Event[]>([])
  const [open, setOpen] = useState(false)
  const [isLoading, setLoading] = useState(false)
  const [isLoadingEvent, setLoadingEvent] = useState(false)
  const [fetchError, setFetchError] = useState<string | null>(null)
  const [activeIndex, setActiveIndex] = useState(-1)

  const suggestionsController = useRef<AbortController | null>(null)
  const eventController = useRef<AbortController | null>(null)
  const rootRef = useRef<HTMLDivElement>(null)
  const debounceTimer = useRef<number>(0)

  const fetchSuggestions = useCallback(async (q: string) => {
    suggestionsController.current?.abort()
    if (!q) {
      setSuggestions([])
      return
    }
    const ctrl = new AbortController()
    suggestionsController.current = ctrl
    setLoading(true)
    setFetchError(null)
    try {
      const res = await fetch(`/api/dice/search?q=${encodeURIComponent(q)}`, { signal: ctrl.signal })
      if (!res.ok) throw new Error(`Error ${res.status}`)
      const data = (await res.json()) as Event[]
      setSuggestions(data)
      setActiveIndex(-1)
    } catch (err) {
      if (!(err instanceof DOMException && err.name === 'AbortError')) setFetchError('Unable to load data')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    window.clearTimeout(debounceTimer.current)
    debounceTimer.current = window.setTimeout(() => fetchSuggestions(query), 350)
    return () => window.clearTimeout(debounceTimer.current)
  }, [query, fetchSuggestions])

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (rootRef.current && !rootRef.current.contains(e.target as Node)) setOpen(false)
    }
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setOpen(false)
    }
    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('keydown', handleEsc)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEsc)
    }
  }, [])

  const onSelect = useCallback(async (event: Event) => {
    eventController.current?.abort()
    const ctrl = new AbortController()
    eventController.current = ctrl
    setLoadingEvent(true)
    try {
      const res = await fetch(`/api/events/getEvent?id=${event.id}`, { signal: ctrl.signal })
      if (!res.ok) throw new Error(`Error ${res.status}`)
      const { socialLinks, previewImage, hero, description, startDatetime, endDatetime, venue } = await res.json()
      setValue(event.name)
      dispatchFields({ type: 'UPDATE', path: 'name', value: event.name })
      dispatchFields({ type: 'UPDATE', path: 'socialLinks', value: [] })
      socialLinks.forEach((link: any, idx: number) => {
        dispatchFields({
          type: 'ADD_ROW',
          path: 'socialLinks',
          rowIndex: idx,
          subFieldState: {
            resource: { initialValue: link.resource, value: link.resource, valid: true },
            link: { initialValue: link.link, value: link.link, valid: true },
          },
        })
      })
      dispatchFields({ type: 'UPDATE', path: 'startDate', value: startDatetime })
      dispatchFields({ type: 'UPDATE', path: 'endDate', value: endDatetime })
      dispatchFields({ type: 'UPDATE', path: 'previewImage', value: previewImage })
      dispatchFields({ type: 'UPDATE', path: 'Location.venue', value: venue });
      dispatchFields({ type: 'UPDATE', path: 'description', value: description, initialValue: description })
      dispatchFields({ type: 'UPDATE', path: 'overview.overview.hero', value: hero })
      setQuery(event.name || '')
      setOpen(false)
    } catch (err) {
      if (!(err instanceof DOMException && err.name === 'AbortError')) setFetchError('Unable to load event data')
    } finally {
      setLoadingEvent(false)
    }
  }, [dispatchFields, setValue])

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!open || suggestions.length === 0) return
    if (e.key === 'ArrowDown') {
      e.preventDefault()
      setActiveIndex(i => (i + 1) % suggestions.length)
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      setActiveIndex(i => (i - 1 + suggestions.length) % suggestions.length)
    } else if (e.key === 'Enter' && suggestions[activeIndex]) {
      e.preventDefault()
      onSelect(suggestions[activeIndex])
    }
  }

  return (
    <div ref={rootRef} style={{ position: 'relative', marginBottom: 16 }}>
      <FieldLabel htmlFor={path} label="Search" />
      <TextInput
        path={path}
        placeholder="Search Dice Events…"
        value={query}
        readOnly={isLoadingEvent}
        onChange={(e: { target: { value: React.SetStateAction<string> } }) => {
          setQuery(e.target.value)
          setOpen(true)
        }}
        onKeyDown={handleKeyDown}
        aria-invalid={showError}
        aria-autocomplete="list"
        aria-expanded={open}
        aria-controls={`${path}-listbox`}
      />
      {showError && <FieldError message={errorMessage} />}
      {!isLoadingEvent && <FieldDescription path={path} description="Start typing to find and select an event" />}
      {isLoadingEvent && <div style={{ padding: 8, color: '#666' }}>Loading event details…</div>}
      {open && !isLoadingEvent && (
        <div
          id={`${path}-listbox`}
          role="listbox"
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            marginTop: 4,
            maxHeight: 208,
            overflowY: 'auto',
            background: '#222',
            border: '1px solid #444',
            borderRadius: 4,
            zIndex: 10,
          }}
        >
          {isLoading && <div style={{ padding: 8, color: '#ccc' }}>Loading…</div>}
          {fetchError && <div style={{ padding: 8, color: 'tomato' }}>{fetchError}</div>}
          {!isLoading && !fetchError && suggestions.length === 0 && (
            <div style={{ padding: 8, color: '#999' }}>No results</div>
          )}
          {!isLoading &&
            !fetchError &&
            suggestions.map((item, idx) => (
              <div
                key={item.id}
                role="option"
                aria-selected={idx === activeIndex}
                onMouseDown={e => e.preventDefault()}
                onClick={() => onSelect(item)}
                style={{
                  padding: '8px 12px',
                  cursor: 'pointer',
                  borderBottom: '1px solid #333',
                  background: idx === activeIndex ? '#444' : 'transparent',
                }}
              >
                {item.name}
              </div>
            ))}
        </div>
      )}
    </div>
  )
}

export default AutocompleteField
