import { useCallback, useEffect, useState } from 'react'
import useEmblaCarousel from 'embla-carousel-react'
import { cn } from '@/lib/utils'
// import { Button, Heading, type ButtonProps } from '@/components'
import { ArrowLeftIcon, ArrowRightIcon, ArrowRightLongIcon } from '@/components/Icons'

import { AnimatePresence, motion } from 'framer-motion'
import { But<PERSON>, Size } from './Button'
import { Heading } from './Heading'

export interface ContentSliderProps<T extends Record<string, any>> {
  title?: string
  description?: string
  link?: {
    href: string
    label?: string
    icon?: React.FC<{ className?: string }>
    size?: Size
    asBottomButton?: boolean
    shallow?: boolean
  }
  items: T[]
  renderItem: React.FC<{ item: T }>
  showAsGrid?: {
    base?: 1 | 2 | 3 | 4 | 5 | 6
    sm?: 2 | 3 | 4 | 5 | 6
    md?: 2 | 3 | 4 | 5 | 6
    lg?: 2 | 3 | 4 | 5 | 6
    xl?: 2 | 3 | 4 | 5 | 6
  }
  fullWidthBackground?: 'gray-4' | 'gray-3' | 'gray-2' | 'gray-1'
  className?: string
}

export function ContentSlider<T extends Record<string, any>>({
  title,
  description,
  link,
  items,
  renderItem: SliderItem,
  showAsGrid,
  fullWidthBackground,
  className,
}: ContentSliderProps<T>) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'start',
    containScroll: 'trimSnaps',
    skipSnaps: true,
  })

  const [{ canScrollPrev, canScrollNext }, setCanScroll] = useState({
    canScrollPrev: false,
    canScrollNext: false,
  })

  const updateCanScroll = useCallback(() => {
    setCanScroll({
      canScrollPrev: emblaApi?.canScrollPrev() ?? false,
      canScrollNext: emblaApi?.canScrollNext() ?? false,
    })
  }, [emblaApi])

  useEffect(() => {
    if (emblaApi) emblaApi.scrollTo(0)
    // updateCanScroll();
  }, [emblaApi, items, updateCanScroll])

  useEffect(() => {
    updateCanScroll()

    if (emblaApi) {
      emblaApi.on('select', updateCanScroll)
      emblaApi.on('slidesChanged', updateCanScroll)
    }

    return () => {
      if (emblaApi) {
        emblaApi.off('select', updateCanScroll)
        emblaApi.off('slidesChanged', updateCanScroll)
      }
    }
  }, [emblaApi, updateCanScroll])

  if (!items.length) return null

  return (
    <div
      className={cn(
        fullWidthBackground && 'full-width overflow-hidden',
        fullWidthBackground === 'gray-1' && 'bg-gray-1',
        fullWidthBackground === 'gray-2' && 'bg-gray-2',
        fullWidthBackground === 'gray-3' && 'bg-gray-3',
        fullWidthBackground === 'gray-4' && 'bg-gray-4',
      )}
    >
      <AnimatePresence mode="popLayout" initial={false}>
        <div className={cn(fullWidthBackground ? 'py-6' : 'my-16', className)}>
          <div className="mb-5 flex flex-wrap items-center justify-between gap-x-8 gap-y-2 empty:hidden md:mb-8">
            {title ? <Heading level="h2" title={title} className="!mb-0" /> : null}
            {link && !link.asBottomButton ? (
              <Button
                type="link"
                href={link.href}
                icon={link.icon ?? ArrowRightIcon}
                size={link.size}
                shallow={link.shallow}
                variant="transparent"
                transparentColor="red"
                trailingIcon
              >
                {link.label ?? 'See all'}
              </Button>
            ) : null}
          </div>
          {description ? (
            <p className="mb-5 font-light text-gray-4 md:mb-7 md:text-lg">{description}</p>
          ) : null}

          {/* SLIDER */}

          <div
            className={cn(
              'relative -mx-4 w-screen md:w-auto',
              showAsGrid?.base && 'hidden',
              showAsGrid?.sm && 'sm:hidden',
              showAsGrid?.md && 'md:hidden',
              showAsGrid?.lg && 'lg:hidden',
              showAsGrid?.xl && 'xl:hidden',
            )}
          >
            <div
              className={cn(
                'absolute inset-y-0 left-0 z-10 hidden w-4 bg-gradient-to-r from-black lg:block',
                fullWidthBackground === 'gray-1' && 'from-gray-1',
                fullWidthBackground === 'gray-2' && 'from-gray-2',
                fullWidthBackground === 'gray-3' && 'from-gray-3',
                fullWidthBackground === 'gray-4' && 'from-gray-4',
              )}
            />
            {canScrollPrev && (
              <div className="absolute inset-y-0 -left-12 z-10 hidden items-center xl:flex">
                <Button
                  type="button"
                  icon={ArrowLeftIcon}
                  variant="opaque"
                  rounded
                  iconOnly
                  onClick={() => emblaApi?.scrollPrev()}
                />
              </div>
            )}
            <div
              className={cn(
                'absolute inset-y-0 right-0 z-10 hidden w-4 bg-gradient-to-l from-black lg:block',
                fullWidthBackground === 'gray-1' && 'from-gray-1',
                fullWidthBackground === 'gray-2' && 'from-gray-2',
                fullWidthBackground === 'gray-3' && 'from-gray-3',
                fullWidthBackground === 'gray-4' && 'from-gray-4',
              )}
            />
            {canScrollNext && (
              <div className="absolute inset-y-0 -right-12 z-10 hidden items-center xl:flex">
                <Button
                  type="button"
                  variant="opaque"
                  rounded
                  iconOnly
                  icon={ArrowRightIcon}
                  onClick={() => emblaApi?.scrollNext()}
                />
              </div>
            )}
            <div
              ref={emblaRef}
              className="embla__viewport relative overflow-hidden pl-4 md:pl-0 md:pr-4"
            >
              <div className="embla__container flex gap-4">
                {items.map((item, index) => (
                  <motion.div
                    key={`slider-${item.id}`}
                    layout="position"
                    className={cn(
                      'embla__slide slide group flex-shrink-0',
                      index === 0 && 'md:ml-4',
                    )}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <SliderItem item={item} />
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* GRID */}

          <div
            className={cn(
              'hidden gap-4',
              showAsGrid?.base && `grid grid-cols-${showAsGrid.base}`,
              showAsGrid?.sm && `sm:grid sm:grid-cols-${showAsGrid.sm}`,
              showAsGrid?.md && `md:grid md:grid-cols-${showAsGrid.md}`,
              showAsGrid?.lg && `lg:grid lg:grid-cols-${showAsGrid.lg}`,
              showAsGrid?.xl && `xl:grid xl:grid-cols-${showAsGrid.xl}`,
            )}
          >
            {items.map((item) => (
              <motion.div
                key={`grid-${item.id}`}
                layout="position"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <SliderItem item={item} />
              </motion.div>
            ))}
          </div>

          {link?.asBottomButton ? (
            <Button
              type="link"
              href={link.href}
              icon={link.icon ?? ArrowRightLongIcon}
              size={link.size}
              variant="transparent"
              transparentColor="red"
              shallow={link.shallow}
              trailingIcon
            >
              {link.label ?? 'See all'}
            </Button>
          ) : null}
        </div>
      </AnimatePresence>
    </div>
  )
}
