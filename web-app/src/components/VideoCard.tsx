import { Media as MediaType } from '@/payload-types'
import Link from 'next/link'
import { Media } from './Media'
import { VideoIcon } from './Icons'

export type VideoCardProps = {
  id: string | number
  url: string
  image: MediaType
  title: string
  topic?: string
}

export function VideoCard({ url, image, title, topic }: VideoCardProps) {
  return (
    <Link
      href={url}
      className="relative block w-full aspect-[2/1] group-[.slide]:w-60 group-[.slide]:sm:w-64"
    >
      <Media resource={image} className="w-full h-full" imgClassName="w-full h-full object-cover" />
      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-[rgba(53,55,57,0.75)]" />
      <VideoIcon className="absolute left-4 top-4 h-7 w-7 text-white" />
      <div className="absolute inset-x-0 bottom-0 px-4 pb-3">
        {topic ? <p className="truncate text-xs text-gray-4">{topic}</p> : null}
        <p className="truncate text-lg font-medium">{title}</p>
      </div>
    </Link>
  )
}
