'use client';

import React, { useState, useEffect, ChangeEvent } from 'react';
import type { TextFieldClientComponent } from 'payload';
import {
  useField,
  FieldLabel,
  FieldError,
  FieldDescription,
  TextInput,
} from '@payloadcms/ui';


const EncryptedSecretField: TextFieldClientComponent = (props: any) => {
  const { path, field } = props;
  const {
    value: storedCipher = '',
    setValue,
    showError,
    errorMessage,
  } = useField<string>({ path }) as any;

  const [input, setInput] = useState('');

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value)   
    setValue(e.target.value);     

  };

  return (
    <div style={{ marginBottom: 16 }}>
      <FieldLabel htmlFor={path} label={field?.label || 'Secret'} />
      <TextInput
        path={path}
        value={input}
        onChange={handleChange}
        placeholder="Enter secret…"
        aria-invalid={showError}
      />
      {showError && <FieldError message={errorMessage} />}
      <FieldDescription
        path={path}
      />
    </div>
  );
};

export default EncryptedSecretField;
