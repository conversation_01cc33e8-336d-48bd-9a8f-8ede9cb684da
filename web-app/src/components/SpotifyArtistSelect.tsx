'use client'

import React, { useState, useEffect, useRef, ChangeEvent } from 'react'
import type { TextFieldClientComponent } from 'payload'
import {
  useField,
  useAllFormFields,
  TextInput,
  FieldLabel,
  FieldError,
  FieldDescription,
} from '@payloadcms/ui'
import type { SpotifyArtist } from '@/lib/spotify/service'

const AutocompleteField: TextFieldClientComponent = ({ path }) => {
  const { value = '', setValue, showError, errorMessage } = useField<string>({ path })
  const [, dispatchFields] = useAllFormFields()

  const [query, setQuery] = useState(value)
  const [suggestions, setSuggestions] = useState<SpotifyArtist[]>([])
  const [open, setOpen] = useState(false)
  const [loadingSuggestions, setLoadingSuggestions] = useState(false)
  const [loadingSelect, setLoadingSelect] = useState(false)
  const rootRef = useRef<HTMLDivElement>(null)
  const debounceTimer = useRef<number>(0)

  const fetchSuggestions = async (q: string) => {
    if (!q) {
      setSuggestions([])
      return
    }
    setLoadingSuggestions(true)
    try {
      const res = await fetch(`/api/spotify/search?q=${encodeURIComponent(q)}`)
      if (res.ok) {
        setSuggestions(await res.json() as SpotifyArtist[])
      }
    } catch (e) {
      console.error('Autocomplete fetch error', e)
    } finally {
      setLoadingSuggestions(false)
    }
  }

  useEffect(() => {
    window.clearTimeout(debounceTimer.current)
    debounceTimer.current = window.setTimeout(() => {
      fetchSuggestions(query)
    }, 300)
    return () => window.clearTimeout(debounceTimer.current)
  }, [query])

  useEffect(() => {
    const onClick = (e: MouseEvent) => {
      if (rootRef.current && !rootRef.current.contains(e.target as Node)) {
        setOpen(false)
      }
    }
    document.addEventListener('mousedown', onClick)
    return () => document.removeEventListener('mousedown', onClick)
  }, [])

  const onSelect = async (artist: SpotifyArtist) => {
    setLoadingSelect(true)

    try {
      const res = await fetch(`/api/artists/getArtist?id=${artist.id}`)
      if (!res.ok) {
        throw new Error(`Error ${res.status}`)
      }
      const { previewImage, socialLinks, genres, overview, chartmetricId, country } = await res.json()

      setValue(artist.name)
      dispatchFields({ type: 'UPDATE', path: 'name', value: artist.name })
      dispatchFields({ type: 'UPDATE', path: 'spotifyId', value: artist.id })
      dispatchFields({ type: 'UPDATE', path: 'chartMetricExternalId', value: chartmetricId })
      dispatchFields({ type: 'UPDATE', path: 'country', value: country })

      dispatchFields({ type: 'UPDATE', path: 'genres', value: genres, initialValue: genres })
      dispatchFields({ type: 'UPDATE', path: 'previewImage', value: previewImage })
      dispatchFields({ type: 'UPDATE', path: 'previewImage', value: previewImage })
      dispatchFields({ type: 'UPDATE', path: 'overview.overview.content', value: overview, initialValue: overview })

      socialLinks.forEach((link: any, idx: number) => {
        dispatchFields({
          type: 'ADD_ROW',
          path: 'socialLinks',
          rowIndex: idx,
          subFieldState: {
            resource: { initialValue: link.resource, value: link.resource, valid: true },
            link: { initialValue: link.link, value: link.link, valid: true },
          },
        })
      })
      setQuery(artist.name)
      setOpen(false)
    } catch (e) {
      console.error(e)
    } finally {
      setLoadingSelect(false)
    }
  }

  return (
    <div ref={rootRef} style={{ position: 'relative', marginBottom: 16 }}>
      <FieldLabel htmlFor={path} label="Spotify Artist" />
      <TextInput
        path={path}
        placeholder={loadingSelect ? 'Loading artist…' : 'Search Spotify artists…'}
        value={query}
        readOnly={loadingSelect}
        onChange={(e: ChangeEvent<HTMLInputElement>) => {
          setQuery(e.target.value)
          setValue(e.target.value)
          setOpen(true)
        }}
        aria-invalid={showError}
      />

      {loadingSelect && (
        <div style={{
          position: 'absolute',
          inset: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'rgba(255,255,255,0.6)',
          borderRadius: 4,
          pointerEvents: 'none',
          zIndex: 20,
        }}>
          <div style={{
            width: 28,
            height: 28,
            border: '4px solid #f3f4f6',
            borderTop: '4px solid #3498db',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
          }} />
        </div>
      )}

      {showError && <FieldError message={errorMessage} />}
      <FieldDescription
        path={path}
        description="Start typing to find and select an artist"
      />

      {open && (
        <ul
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            maxHeight: 200,
            overflowY: 'auto',
            background: '#222',
            color: '#fff',
            border: '1px solid #444',
            borderRadius: 4,
            marginTop: 4,
            zIndex: 10,
            listStyle: 'none',
            padding: 0,
          }}
        >
          {loadingSuggestions && (
            <li style={{ padding: '8px 12px' }}>Loading…</li>
          )}
          {(!loadingSuggestions && !loadingSelect) &&
            suggestions.map(item => (
              <li
                key={item.id}
                onMouseDown={e => e.preventDefault()}
                onClick={() => onSelect(item)}
                style={{ padding: '8px 12px', cursor: 'pointer' }}
              >
                {item.name}
              </li>
            ))}
        </ul>
      )}

      <style jsx>{`
    @keyframes spin {
      0%   { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `}</style>
    </div>

  )
}

export default AutocompleteField
