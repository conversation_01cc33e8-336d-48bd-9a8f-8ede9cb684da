import Link from 'next/link'
import type { FC, ReactNode, MouseEventHandler } from 'react'
import { cn } from '@/lib/utils'

const sizeClasses = {
  xs: 'text-xs px-3 py-1.5',
  sm: 'text-sm px-4 py-2',
  md: 'text-base px-5 py-3',
  lg: 'text-lg px-5 py-3',
} as const

const iconSizeClasses = {
  xs: 'w-4 h-4',
  sm: 'w-5 h-5',
  md: 'w-[1.375rem] h-[1.375rem]',
  lg: 'w-6 h-6',
} as const

const variantClasses = {
  red: 'bg-red text-white enabled:hover:bg-red/80',
  gray: 'bg-gray-2 text-white enabled:hover:text-gray-4',
  opaque: 'bg-foreground/40 text-background enabled:hover:bg-foreground/50',
  transparent: 'bg-transparent',
} as const

const transparentColorClasses = {
  white: 'text-white enabled:hover:text-red',
  red: 'text-red enabled:hover:text-white',
  gray: 'text-gray-4 enabled:hover:text-white',
} as const

export type Variant = keyof typeof variantClasses
export type TransparentColor = keyof typeof transparentColorClasses
export type Size = keyof typeof sizeClasses

type SharedProps = {
  size?: Size
  disabled?: boolean
  icon?: FC<{ className?: string }>
  trailingIcon?: boolean
  rounded?: boolean
  iconOnly?: boolean
  fullWidth?: boolean
  className?: string
  analyticsId?: string
  ignoreContentEntity?: boolean
  children?: ReactNode
}

type ButtonAsButton = {
  type: 'button' | 'submit'
  onClick?: MouseEventHandler<HTMLButtonElement>
  variant: Exclude<Variant, 'transparent'>
} & SharedProps

type ButtonAsLink = {
  type: 'link'
  href: string
  shallow?: boolean
  target?: string
  onClick?: MouseEventHandler<HTMLAnchorElement>
  variant: 'transparent'
  transparentColor: TransparentColor
} & SharedProps

type ButtonProps = ButtonAsButton | ButtonAsLink

export function Button(props: ButtonProps) {
  const {
    className,
    icon: Icon,
    trailingIcon,
    rounded,
    iconOnly,
    fullWidth,
    disabled,
    size = 'md',
    // analyticsId,
    // ignoreContentEntity,
    children,
  } = props

  const isTransparent = props.variant === 'transparent'
  const baseClass = cn(
    'flex items-center font-medium transition-colors cursor-pointer',
    fullWidth && 'w-full',
    trailingIcon && 'flex-row-reverse',
    rounded && iconOnly && 'rounded-full',
    sizeClasses[size],
    iconOnly &&
      !children &&
      {
        xs: 'p-1.5',
        sm: 'p-2.5',
        md: 'p-3',
        lg: 'p-4',
      }[size],
    children &&
      {
        xs: 'gap-2',
        sm: 'gap-2',
        md: 'gap-3',
        lg: 'gap-3',
      }[size],
    isTransparent
      ? cn(
          variantClasses.transparent,
          transparentColorClasses[(props as ButtonAsLink).transparentColor],
        )
      : variantClasses[(props as ButtonAsButton).variant],
    isTransparent && 'p-0',
    disabled && 'opacity-50',
    className,
  )

  const iconClass = cn(iconSizeClasses[size])
  const contents = (
    <>
      {Icon && <Icon className={iconClass} />}
      {children && (typeof children === 'string' ? <span>{children}</span> : children)}
    </>
  )

  if (props.type === 'button' || props.type === 'submit') {
    return (
      <button type={props.type} onClick={props.onClick} className={baseClass} disabled={disabled}>
        {contents}
      </button>
    )
  }

  // At this point, we *know* props is ButtonAsLink
  const { href, shallow, target } = props as ButtonAsLink

  if (href.startsWith('/')) {
    return (
      <Link href={href} shallow={shallow} target={target} className={baseClass}>
        {contents}
      </Link>
    )
  }

  return (
    <a href={href} target="_blank" rel="noopener noreferrer" className={baseClass}>
      {contents}
    </a>
  )
}
