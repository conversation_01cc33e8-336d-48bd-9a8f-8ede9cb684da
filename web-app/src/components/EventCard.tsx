import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import duration from 'dayjs/plugin/duration'
import Link from 'next/link'
import React from 'react'
import { DiamondIcon, SignalIcon } from '@/components/Icons'
import { GrayAreaLogo } from '@/components/Logos'
import { Media } from '@/components/Media'
import { Event, Venue } from '@/payload-types'

// import { useAnalytics } from '~/hooks'
// import { type EventPartialDocument } from '~/server/services/sanity/validation'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(duration)

export type EventCardProps = {
  event: Event
  artistMentions?: {
    name: string
    url: string
  }[]
}

export function EventCard({ event, artistMentions = [] }: EventCardProps) {
  if (!event) return null

  const {
    Location,
    startDate,
    endDate,
    // isGrayAreaEvent,
    // isSoldOut,
    // isFestivalAfterparty,
    // isVirtualEvent,
    // isFestival,
    name,
    // externalLinks,
    // previewImage,
    slug,
  } = event
  const _startDate = dayjs(startDate)
  const _endDate = dayjs(endDate)

  const isMultidayEvent = endDate ? dayjs(endDate).diff(startDate, 'day') > 1 : false

  const venue = Location?.venue as Venue

  // const { track } = useAnalytics()

  return (
    <div
      className="relative w-full h-full group-[.slide]:w-[180px] group-[.slide]:sm:w-[270px]"
      // onClick={() => {
      //   track({
      //     event: 'product-clicked',
      //     product: {
      //       product_id: slug,
      //       sku: _id,
      //       name,
      //       url,
      //       virtual_event: isVirtualEvent,
      //       gray_area: isGrayAreaEvent,
      //       sold_out: isSoldOut,
      //       afterparty: isFestivalAfterparty,
      //       festival: isFestival,
      //       event_date: startDate?.toString(),
      //       start_date: startDate?.toString(),
      //       end_date: endDate?.toString(),
      //       genre: genres,
      //       lineup: artists.map(({ name, url, _id }) => ({
      //         name: name,
      //         url: url,
      //         _id: _id,
      //       })),
      //     },
      //   })
      // }}
    >
      <Link href={`events/${slug}`} className="aspect-square block w-full">
        {/* <Media
          resource={previewImage}
          className="w-full h-full"
          imgClassName="w-full h-full object-cover"
        /> */}
      </Link>
      <div className="flex flex-col justify-between py-3">
        <Link href={`events/${slug}`}>
          <p className="mb-0.5 font-medium !leading-tight text-white sm:text-lg">{name}</p>
        </Link>
        {/* {isGrayAreaEvent ? (
          <div className="mt-0.5 flex items-center gap-1.5">
            <GrayAreaLogo variant="mark" className="w-5 text-white" />
            <span className="text-xs text-gray-4">Gray Area event</span>
          </div>
        ) : null}
        {isVirtualEvent ? (
          <div className="mt-0.5 flex items-center gap-1.5">
            <SignalIcon className="h-5 w-5 text-white" />
            <span className="text-xs text-gray-4">Virtual event</span>
          </div>
        ) : null} */}
        <div className="mt-1.5 text-xs font-light text-gray-4 sm:text-sm">
          {venue ? (
            <Link href={'/'}>
              <p>{venue.name}</p>
              <p className="text-xs">
                {[
                  venue.name,
                  ...(venue.country === 'US' ? [venue.city ?? venue.country] : [venue.country]),
                ].join(', ')}
              </p>
            </Link>
          ) :  null}
          <div className="mt-1 space-x-1">
            {isMultidayEvent && _endDate ? (
              _startDate.format('MMM') === _endDate.format('MMM') ? (
                <span>
                  {_startDate.format('MMM D')} - {_endDate.format('D, YYYY')}
                </span>
              ) : (
                <span>
                  {_startDate.format('MMM D')} - {_endDate.format('MMM D, YYYY')}
                </span>
              )
            ) : (
              <span>{_startDate.format('MMM D, YYYY')}</span>
            )}
            {/* {isFestival || isMultidayEvent ? null : (
              <>
                <DiamondIcon className="inline h-1.5 w-1.5" />
                <span>{_startDate.format(_startDate.minute() === 0 ? 'h A' : 'h:mm A')}</span>
              </>
            )} */}
          </div>
        </div>
        {artistMentions.length > 0 ? (
          <p className="mt-2 text-xs text-gray-4">
            <div>
              {artistMentions.reduce<React.ReactElement[]>(
                (elements, { name, url }, index, mentions) => {
                  elements.push(
                    <Link
                      key={name}
                      href={url}
                      className="text-white underline opacity-90 transition-opacity hover:text-white hover:opacity-100"
                    >
                      {name}
                    </Link>,
                  )
                  if (mentions.length > 0 && index < mentions.length - 2)
                    elements.push(<span key={`${name}-separator`}>, </span>)
                  else if (mentions.length > 0 && index === mentions.length - 2)
                    elements.push(<span key={`${name}-separator`}> and </span>)

                  return elements
                },
                [],
              )}
              {` ${artistMentions.length === 1 ? 'is' : 'are'} on the lineup`}
            </div>
          </p>
        ) : null}
      </div>
    </div>
  )
}
