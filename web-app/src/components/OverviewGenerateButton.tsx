'use client'

import React, { useState } from 'react'
import { useAll<PERSON><PERSON><PERSON>ields, use<PERSON>orm<PERSON>ields, Button, TextInput, GroupField } from '@payloadcms/ui'
import { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'
import { compile } from 'handlebars'
import { overviewPrompts } from '@/constants/aiPrompts'
import { appendText, createLexicalState } from '@/lib/sync/lib'


export default function OverviewGenerateButton(props: any) {
  const [, dispatch] = useAllFormFields()
  const fields = useFormFields(([f]) => f)
  const slug = props.schemaPath.split('.')[0]
  const promptTemplate =
    overviewPrompts[slug] ??
    'Write a concise overview for {{name}}.'

  const [draft, setDraft] = useState<string | null>(null)
  const [sending, setSending] = useState(false)

  const editPrompt = () => {
    const context = Object.fromEntries(
      Object.entries(fields).map(([k, f]) => [k, f?.value]),
    )
    const compiled = compile(promptTemplate)(context)
    setDraft(compiled)
  }


  const send = async () => {
    if (!draft) return
    setSending(true)
    try {
      const res = await fetch('/api/generate-overview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: draft }),
      })
      const { text } = await res.json()

      const baseState = createLexicalState('')
      const withIntro = appendText(baseState, text)


      dispatch({
        type: 'UPDATE',
        path: 'overview.overview.content',
        value: withIntro,
        initialValue: withIntro,
      })
      setDraft(null)
    } finally {
      setSending(false)
    }
  }

  return (
<>
      <Button type="button" onClick={editPrompt} disabled={sending}>
        Generate Overview
      </Button>
      {draft !== null && (
        <div className="modal">
          {sending && (
            <div className="loader-overlay">
              <div className="loader" />
            </div>
          )}
          <div className="panel">
            <h3>Edit prompt before sending</h3>
            <textarea
              className="prompt-input"
              value={draft}
              onChange={e => setDraft(e.target.value)}
              autoFocus
            />
            <div className="actions">
              <Button className="btn" onClick={send} disabled={sending}>
                {sending ? 'Sending…' : 'Send'}
              </Button>
              <Button
                className="btn secondary"
                onClick={() => setDraft(null)}
                disabled={sending}
              >
                Cancel
              </Button>
            </div>
          </div>
          <style jsx>{`
            .modal {
              position: fixed;
              inset: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 1000;
            }
            .loader-overlay {
              position: absolute;
              inset: 0;
              background: rgba(255, 255, 255, 0.6);
              display: flex;
              align-items: center;
              justify-content: center;
            }
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
            .loader {
              border: 4px solid #f3f4f6;
              border-top: 4px solid #3498db;
              border-radius: 50%;
              width: 32px;
              height: 32px;
              animation: spin 1s linear infinite;
            }
            .panel {
              width: 90%;
              max-width: 520px;
              background: var(--theme-bg);
              border-radius: 6px;
              padding: 20px;
              font-family: Arial, sans-serif;
            }
            h3 {
              margin: 0 0 12px;
              font-size: 18px;
              font-weight: 600;
            }
            textarea {
              width: 100%;
              min-height: 160px;
              resize: vertical;
              padding: 8px;
              font-size: 14px;
              font-family: Menlo, monospace;
              border: 1px solid #d1d5db;
              border-radius: 4px;
              box-sizing: border-box;
            }
            .actions {
              margin-top: 16px;
              display: flex;
              gap: 8px;
              justify-content: flex-end;
            }
            .btn {
              padding: 6px 12px;
              font-size: 14px;
              border: 1px solid #d1d5db;
              border-radius: 4px;
              cursor: pointer;
              background: #f3f4f6;
              transition: background 0.15s ease;
            }
            .btn:hover:not([disabled]) {
              background: #e5e7eb;
            }
            .btn.secondary {
              background: #ffffff;
            }
            .btn[disabled] {
              cursor: not-allowed;
              opacity: 0.6;
            }
          `}</style>
        </div>
      )}
    </>
  )
}
