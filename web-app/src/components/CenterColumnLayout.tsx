import { twMerge } from 'tailwind-merge'

export type CenterColumnLayoutProps<T extends React.ElementType = 'div'> = {
  children: React.ReactNode
  className?: string
  as?: T
} & Omit<React.ComponentPropsWithRef<T>, 'children' | 'className' | 'as'>

export function CenterColumnLayout<T extends React.ElementType = 'div'>({
  children,
  className,
  as,
  ...props
}: CenterColumnLayoutProps<T>) {
  const TagName = as || 'div'

  return (
    <TagName
      className={twMerge(
        'px-4 sm:px-6 md:px-8 lg:px-10 [&>.full-width>div]:mx-auto [&>.full-width>div]:max-w-5xl [&>.full-width]:-mx-4 [&>.full-width]:px-4 [&>.full-width]:sm:-mx-6 [&>.full-width]:sm:px-6 [&>.full-width]:md:-mx-8 [&>.full-width]:md:px-8 [&>.full-width]:lg:-mx-10 [&>.full-width]:lg:px-10 [&>:not(.absolute,.full-width)]:mx-auto [&>:not(.absolute,.full-width)]:max-w-5xl',
        className,
      )}
      {...props}
    >
      {children}
    </TagName>
  )
}
