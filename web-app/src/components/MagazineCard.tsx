import Link from 'next/link'
// import { <PERSON><PERSON><PERSON>ine, GrayAreaLogo, Media } from '~/components'
import { Media } from '@/components/Media'
import { GrayAreaLogo } from '@/components/Logos'
// import { getArticleCategoryTitle, getArticleType } from '~/utils/content'
import { twMerge } from 'tailwind-merge'
import React from 'react'

type Mention = {
  _type: string
  name: string
  url: string
}
export type ArticleCardProps = any

export const articleCategories = [
  { title: 'Track of the Week', value: 'trackOfTheWeek' },
  { title: 'Set of the Week', value: 'setOfTheWeek' },
  { title: 'Playlist', value: 'playlist' },
  { title: 'This Week In Ibiza', value: 'thisWeekInIbiza' },
] as const

export const getArticleCategoryTitle = (category: any) => {
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  return articleCategories.find(({ value }) => value === category)!.title
}

export const reversePascalCase = (input: string) => {
  let output = ''

  for (const char of input) {
    if (output.length === 0) {
      output += char.toUpperCase()
      continue
    }

    if (char === char.toUpperCase()) {
      output += ' '
    }

    output += char
  }

  return output
}

export const getArticleType = (type: string) => reversePascalCase(type)

export type AuthorBylineProps = {
  author: any
  small?: boolean
  className?: string
}

export function AuthorByline({
  author: { name, url, photo },
  small,
  className,
}: AuthorBylineProps) {
  return (
    <Link href={url} className={twMerge('flex items-center space-x-2', className)}>
      {photo ? (
        <Media
          resource={photo}
          alt={`Photo of ${name}`}
          className={twMerge(
            'h-[18px] w-[18px] overflow-hidden rounded-full',
            !small && 'sm:h-[24px] sm:w-[24px]',
          )}
          imgClassName="w-full h-full object-cover"
        />
      ) : null}
      <p className={twMerge('text-gray-4', small ? 'text-sm' : 'text-base sm:text-lg')}>{name}</p>
    </Link>
  )
}

export function ArticleCard({
  article: { context, author, estimatedReadingTime },
  hideAuthor,
  hideType,
  hideReadTime,
  mentions,
}: ArticleCardProps) {
  const entity = 'entity' in context ? context.entity : null
  const image =
    context.image ||
    (entity?._type === 'event' && entity.imageSquare) ||
    (entity && 'photo' in entity && entity.photo) ||
    null
  const articleTypeHeading =
    context.type === 'magazine' && context.category
      ? getArticleCategoryTitle(context.category)
      : hideType
        ? null
        : getArticleType(context.type)

  return (
    <div className="relative block w-full group-[.slide]:w-[160px] group-[.slide]:sm:w-[212px]">
      <Link href={context.url}>
        {image ? (
          <Media
            resource={image}
            className="w-full h-full object-cover"
            imgClassName="w-full h-full object-cover"
          />
        ) : (
          <div className="aspect-h-1 aspect-w-1 flex max-w-full flex-shrink items-center justify-center bg-gray-2">
            <GrayAreaLogo variant="mark" className="mx-auto w-16 text-white" />
          </div>
        )}
      </Link>
      <div className="flex flex-col justify-between space-y-2 py-3">
        <Link href={context.url}>
          <div>
            {articleTypeHeading ? (
              <p className="mb-1 text-xs font-semibold text-white">{articleTypeHeading}</p>
            ) : null}
            <p className="text-sm text-white sm:text-base sm:leading-tight">
              <div>{'title' in context ? context.title : context.entity.name}</div>
            </p>
          </div>
        </Link>
        {/* {author && !hideAuthor ? <AuthorByline author={author} small /> : null} */}

        {hideReadTime ? null : (
          <p className="text-xs text-gray-4">{estimatedReadingTime} min read</p>
        )}

        {(mentions as Mention[]).length > 0 ? (
          <p className="mt-2 max-w-full text-xs text-gray-4">
            {(mentions as Mention[]).reduce<React.ReactElement[]>(
              (elements, { _type, name, url }, index, mentions) => {
                elements.push(
                  <Link
                    key={`${_type}-${name}`}
                    href={url}
                    className="text-white underline opacity-90 transition-opacity hover:text-white hover:opacity-100"
                  >
                    {name}
                  </Link>,
                )
                if (mentions.length > 0 && index < mentions.length - 2)
                  elements.push(<span key={`${_type}-${name}-separator`}>, </span>)
                else if (mentions.length > 0 && index === mentions.length - 2)
                  elements.push(<span key={`${_type}-${name}-separator`}> and </span>)

                return elements
              },
              [],
            )}
            {` ${mentions.length === 1 ? 'is' : 'are'} mentioned`}
          </p>
        ) : null}
      </div>
    </div>
  )
}
