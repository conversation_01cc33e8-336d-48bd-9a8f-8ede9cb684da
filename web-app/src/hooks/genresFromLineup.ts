import { Event, Genre } from '@/payload-types'
import { FieldHook } from 'payload'

const compareArrays = (a: number[], b: number[]): boolean => {
  a = a ?? [];
  b = b ?? [];
  if (a.length !== b.length) return false
  const sortedA = [...a].sort((x, y) => x - y)
  const sortedB = [...b].sort((x, y) => x - y)
  return sortedA.every((val, index) => val === sortedB[index])
}

export const genresFromLineup: FieldHook<Event> = async ({ data, originalDoc, req }) => {
  const { lineup, eventGenres } = data as Partial<Event>
  const genres: Genre[] = []

  if (lineup)
    for (const item of lineup) {
      const artistGenres = (
        await req.payload.find({
          collection: 'artists',
          where: {
            id: { equals: item.artist },
          },
          select: {
            genres: true,
          },
        })
      ).docs[0]?.genres

      if (artistGenres) genres.push(...(artistGenres as Genre[]))
    }

  const genresIds = [...new Set(genres.map(g => g.id))];

  if (!compareArrays(genresIds, eventGenres?.fromLineup as number[]))
    await req.payload.update({
      collection: 'events',
      where: {
        id: { equals: originalDoc!.id },
      },
      data: {
        eventGenres: {
          fromLineup: genresIds,
        },
      },
      req,
    })
}
