import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "promotions_code_locks" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"code" varchar NOT NULL,
  	"claimed_by_id" integer
  );
  
  ALTER TABLE "promo_codes" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "promo_codes" CASCADE;
  ALTER TABLE "payload_locked_documents_rels" DROP CONSTRAINT IF EXISTS "payload_locked_documents_rels_promo_codes_fk";
  
  DROP INDEX IF EXISTS "payload_locked_documents_rels_promo_codes_id_idx";
  DO $$ BEGIN
   ALTER TABLE "promotions_code_locks" ADD CONSTRAINT "promotions_code_locks_claimed_by_id_fan_users_id_fk" FOREIGN KEY ("claimed_by_id") REFERENCES "public"."fan_users"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "promotions_code_locks" ADD CONSTRAINT "promotions_code_locks_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."promotions"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "promotions_code_locks_order_idx" ON "promotions_code_locks" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "promotions_code_locks_parent_id_idx" ON "promotions_code_locks" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "promotions_code_locks_claimed_by_idx" ON "promotions_code_locks" USING btree ("claimed_by_id");
  ALTER TABLE "payload_locked_documents_rels" DROP COLUMN IF EXISTS "promo_codes_id";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "promo_codes" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"code" varchar NOT NULL,
  	"promotion_id" integer NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  ALTER TABLE "promotions_code_locks" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "promotions_code_locks" CASCADE;
  ALTER TABLE "payload_locked_documents_rels" ADD COLUMN "promo_codes_id" integer;
  DO $$ BEGIN
   ALTER TABLE "promo_codes" ADD CONSTRAINT "promo_codes_promotion_id_promotions_id_fk" FOREIGN KEY ("promotion_id") REFERENCES "public"."promotions"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "promo_codes_promotion_idx" ON "promo_codes" USING btree ("promotion_id");
  CREATE INDEX IF NOT EXISTS "promo_codes_updated_at_idx" ON "promo_codes" USING btree ("updated_at");
  CREATE INDEX IF NOT EXISTS "promo_codes_created_at_idx" ON "promo_codes" USING btree ("created_at");
  DO $$ BEGIN
   ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_promo_codes_fk" FOREIGN KEY ("promo_codes_id") REFERENCES "public"."promo_codes"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "payload_locked_documents_rels_promo_codes_id_idx" ON "payload_locked_documents_rels" USING btree ("promo_codes_id");`)
}
