import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "artists" ALTER COLUMN "spotify_id" SET NOT NULL;
  ALTER TABLE "articles" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "artists" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "event_brands" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "event_organizers" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "events" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "festival_profiles" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "festivals" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "residencies" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "ticket_types" ADD COLUMN "sync_lock" boolean DEFAULT false;
  ALTER TABLE "venues" ADD COLUMN "sync_lock" boolean DEFAULT false;`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "artists" ALTER COLUMN "spotify_id" DROP NOT NULL;
  ALTER TABLE "articles" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "artists" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "event_brands" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "event_organizers" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "events" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "festival_profiles" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "festivals" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "residencies" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "ticket_types" DROP COLUMN IF EXISTS "sync_lock";
  ALTER TABLE "venues" DROP COLUMN IF EXISTS "sync_lock";`)
}
