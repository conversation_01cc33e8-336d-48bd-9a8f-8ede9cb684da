import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "ticket_types_fee" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"applicable" boolean DEFAULT true,
  	"amount" numeric NOT NULL,
  	"computed" numeric NOT NULL,
  	"type" varchar NOT NULL,
  	"unit" varchar NOT NULL
  );
  
  ALTER TABLE "ticket_types" ADD COLUMN "total_fee" numeric;
  DO $$ BEGIN
   ALTER TABLE "ticket_types_fee" ADD CONSTRAINT "ticket_types_fee_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."ticket_types"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "ticket_types_fee_order_idx" ON "ticket_types_fee" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "ticket_types_fee_parent_id_idx" ON "ticket_types_fee" USING btree ("_parent_id");
  ALTER TABLE "ticket_types" DROP COLUMN IF EXISTS "fee";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   DROP TABLE "ticket_types_fee" CASCADE;
  ALTER TABLE "ticket_types" ADD COLUMN "fee" numeric;
  ALTER TABLE "ticket_types" DROP COLUMN IF EXISTS "total_fee";`)
}
