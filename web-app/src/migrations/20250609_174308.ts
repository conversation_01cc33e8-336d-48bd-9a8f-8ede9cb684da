import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "events_ticket_types" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"ticket_type_id" integer NOT NULL
  );
  
  ALTER TABLE "ticket_types" DROP CONSTRAINT "ticket_types_event_id_events_id_fk";
  
  DROP INDEX IF EXISTS "ticket_types_event_idx";
  DO $$ BEGIN
   ALTER TABLE "events_ticket_types" ADD CONSTRAINT "events_ticket_types_ticket_type_id_ticket_types_id_fk" FOREIGN KEY ("ticket_type_id") REFERENCES "public"."ticket_types"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "events_ticket_types" ADD CONSTRAINT "events_ticket_types_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."events"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "events_ticket_types_order_idx" ON "events_ticket_types" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "events_ticket_types_parent_id_idx" ON "events_ticket_types" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "events_ticket_types_ticket_type_idx" ON "events_ticket_types" USING btree ("ticket_type_id");
  ALTER TABLE "ticket_types" DROP COLUMN IF EXISTS "event_id";
  ALTER TABLE "public"."payload_jobs_log" ALTER COLUMN "task_slug" SET DATA TYPE text;
  DROP TYPE "public"."enum_payload_jobs_log_task_slug";
  CREATE TYPE "public"."enum_payload_jobs_log_task_slug" AS ENUM('inline', 'importDiceEvent', 'extractEventUrls', 'getSitemapUrls', 'qflowLogin', 'qflowFetchEvents', 'qflowSyncAttendees', 'fetchEventWithTicketTypes', 'upsertTicketTypes', 'fetchOrders', 'upsertTickets', 'getConcertSitemapUrls', 'collectConcertUrls', 'fetchHtmlAndSave', 'saveRawSanityData', 'importSanityData', 'handleSongkickEvent', 'extractKeysByBucket', 'extractLdJson', 'extractRetailerId', 'findOrCreateEvent', 'findOrCreateTicket', 'createOrder', 'importPlanetscaleUser', 'importPlanetscalePresaleRegistration', 'schedulePublish');
  ALTER TABLE "public"."payload_jobs_log" ALTER COLUMN "task_slug" SET DATA TYPE "public"."enum_payload_jobs_log_task_slug" USING "task_slug"::"public"."enum_payload_jobs_log_task_slug";
  ALTER TABLE "public"."payload_jobs" ALTER COLUMN "workflow_slug" SET DATA TYPE text;
  DROP TYPE "public"."enum_payload_jobs_workflow_slug";
  CREATE TYPE "public"."enum_payload_jobs_workflow_slug" AS ENUM('crawlSanity', 'crawlRAWorkflow', 'crawlSongkickWorkflow', 'crawlDicekWorkflow', 'importSanityDataWorkflow', 'syncDicePartnerEventsWorkflow', 'syncDiceOrdersWorkflow', 'syncDiceWorkflow', 'syncQFlowWorkflow', 'syncSongkickWorkflow', 'syncPlanetscaleUsersWorkflow', 'syncPlanetscalePresaleRegistrationsWorkflow');
  ALTER TABLE "public"."payload_jobs" ALTER COLUMN "workflow_slug" SET DATA TYPE "public"."enum_payload_jobs_workflow_slug" USING "workflow_slug"::"public"."enum_payload_jobs_workflow_slug";
  ALTER TABLE "public"."payload_jobs" ALTER COLUMN "task_slug" SET DATA TYPE text;
  DROP TYPE "public"."enum_payload_jobs_task_slug";
  CREATE TYPE "public"."enum_payload_jobs_task_slug" AS ENUM('inline', 'importDiceEvent', 'extractEventUrls', 'getSitemapUrls', 'qflowLogin', 'qflowFetchEvents', 'qflowSyncAttendees', 'fetchEventWithTicketTypes', 'upsertTicketTypes', 'fetchOrders', 'upsertTickets', 'getConcertSitemapUrls', 'collectConcertUrls', 'fetchHtmlAndSave', 'saveRawSanityData', 'importSanityData', 'handleSongkickEvent', 'extractKeysByBucket', 'extractLdJson', 'extractRetailerId', 'findOrCreateEvent', 'findOrCreateTicket', 'createOrder', 'importPlanetscaleUser', 'importPlanetscalePresaleRegistration', 'schedulePublish');
  ALTER TABLE "public"."payload_jobs" ALTER COLUMN "task_slug" SET DATA TYPE "public"."enum_payload_jobs_task_slug" USING "task_slug"::"public"."enum_payload_jobs_task_slug";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TYPE "public"."enum_payload_jobs_log_task_slug" ADD VALUE 'fetchEventIds' BEFORE 'fetchEventWithTicketTypes';
  ALTER TYPE "public"."enum_payload_jobs_workflow_slug" ADD VALUE 'syncDiceTicketTypesWorkflow' BEFORE 'syncDicePartnerEventsWorkflow';
  ALTER TYPE "public"."enum_payload_jobs_task_slug" ADD VALUE 'fetchEventIds' BEFORE 'fetchEventWithTicketTypes';
  ALTER TABLE "events_ticket_types" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "events_ticket_types" CASCADE;
  ALTER TABLE "ticket_types" ADD COLUMN "event_id" integer;
  DO $$ BEGIN
   ALTER TABLE "ticket_types" ADD CONSTRAINT "ticket_types_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "ticket_types_event_idx" ON "ticket_types" USING btree ("event_id");`)
}
