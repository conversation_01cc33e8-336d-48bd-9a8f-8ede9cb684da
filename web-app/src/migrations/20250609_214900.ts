import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_festival_profiles_origin" AS ENUM('sanity', 'dice', 'ra', 'songkick', 'eventbrite');
  ALTER TABLE "festival_profiles" ADD COLUMN "external_sanity_id" varchar;
  ALTER TABLE "festival_profiles" ADD COLUMN "origin" "enum_festival_profiles_origin";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "festival_profiles" DROP COLUMN IF EXISTS "external_sanity_id";
  ALTER TABLE "festival_profiles" DROP COLUMN IF EXISTS "origin";
  DROP TYPE "public"."enum_festival_profiles_origin";`)
}
