import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "hubs" ADD COLUMN "formatted" varchar NOT NULL;
  ALTER TABLE "venues" ADD COLUMN "hub_id" integer;
  DO $$ BEGIN
   ALTER TABLE "venues" ADD CONSTRAINT "venues_hub_id_hubs_id_fk" FOREIGN KEY ("hub_id") REFERENCES "public"."hubs"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "venues_hub_idx" ON "venues" USING btree ("hub_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "venues" DROP CONSTRAINT "venues_hub_id_hubs_id_fk";
  
  DROP INDEX IF EXISTS "venues_hub_idx";
  ALTER TABLE "hubs" DROP COLUMN IF EXISTS "formatted";
  ALTER TABLE "venues" DROP COLUMN IF EXISTS "hub_id";`)
}
