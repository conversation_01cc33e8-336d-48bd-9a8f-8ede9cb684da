{"id": "b797667e-5431-47a3-a884-279183c1300e", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.agencies": {"name": "agencies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "general_contac_info_email": {"name": "general_contac_info_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "general_contac_info_phone_number": {"name": "general_contac_info_phone_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"agencies_updated_at_idx": {"name": "agencies_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agencies_created_at_idx": {"name": "agencies_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agents": {"name": "agents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "general_contac_info_email": {"name": "general_contac_info_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "general_contac_info_phone_number": {"name": "general_contac_info_phone_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "agency_id": {"name": "agency_id", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"agents_agency_idx": {"name": "agents_agency_idx", "columns": [{"expression": "agency_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agents_updated_at_idx": {"name": "agents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agents_created_at_idx": {"name": "agents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"agents_agency_id_agencies_id_fk": {"name": "agents_agency_id_agencies_id_fk", "tableFrom": "agents", "tableTo": "agencies", "columnsFrom": ["agency_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles": {"name": "articles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "enum_articles_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "integer", "primaryKey": false, "notNull": true}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"articles_author_idx": {"name": "articles_author_idx", "columns": [{"expression": "author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_preview_image_idx": {"name": "articles_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_overview_overview_overview_overview_hero_idx": {"name": "articles_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_meta_meta_meta_meta_image_idx": {"name": "articles_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_slug_idx": {"name": "articles_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_updated_at_idx": {"name": "articles_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_created_at_idx": {"name": "articles_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_author_id_authors_id_fk": {"name": "articles_author_id_authors_id_fk", "tableFrom": "articles", "tableTo": "authors", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "articles_preview_image_id_media_id_fk": {"name": "articles_preview_image_id_media_id_fk", "tableFrom": "articles", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "articles_overview_overview_hero_id_media_id_fk": {"name": "articles_overview_overview_hero_id_media_id_fk", "tableFrom": "articles", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "articles_meta_meta_image_id_media_id_fk": {"name": "articles_meta_meta_image_id_media_id_fk", "tableFrom": "articles", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles_rels": {"name": "articles_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "events_id": {"name": "events_id", "type": "integer", "primaryKey": false, "notNull": false}, "artists_id": {"name": "artists_id", "type": "integer", "primaryKey": false, "notNull": false}, "event_brands_id": {"name": "event_brands_id", "type": "integer", "primaryKey": false, "notNull": false}, "festivals_id": {"name": "festivals_id", "type": "integer", "primaryKey": false, "notNull": false}, "residencies_id": {"name": "residencies_id", "type": "integer", "primaryKey": false, "notNull": false}, "venues_id": {"name": "venues_id", "type": "integer", "primaryKey": false, "notNull": false}, "authors_id": {"name": "authors_id", "type": "integer", "primaryKey": false, "notNull": false}, "ocho_episodes_id": {"name": "ocho_episodes_id", "type": "integer", "primaryKey": false, "notNull": false}, "hubs_id": {"name": "hubs_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"articles_rels_order_idx": {"name": "articles_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_parent_idx": {"name": "articles_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_path_idx": {"name": "articles_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_events_id_idx": {"name": "articles_rels_events_id_idx", "columns": [{"expression": "events_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_artists_id_idx": {"name": "articles_rels_artists_id_idx", "columns": [{"expression": "artists_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_event_brands_id_idx": {"name": "articles_rels_event_brands_id_idx", "columns": [{"expression": "event_brands_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_festivals_id_idx": {"name": "articles_rels_festivals_id_idx", "columns": [{"expression": "festivals_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_residencies_id_idx": {"name": "articles_rels_residencies_id_idx", "columns": [{"expression": "residencies_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_venues_id_idx": {"name": "articles_rels_venues_id_idx", "columns": [{"expression": "venues_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_authors_id_idx": {"name": "articles_rels_authors_id_idx", "columns": [{"expression": "authors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_ocho_episodes_id_idx": {"name": "articles_rels_ocho_episodes_id_idx", "columns": [{"expression": "ocho_episodes_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_hubs_id_idx": {"name": "articles_rels_hubs_id_idx", "columns": [{"expression": "hubs_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_rels_parent_fk": {"name": "articles_rels_parent_fk", "tableFrom": "articles_rels", "tableTo": "articles", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_events_fk": {"name": "articles_rels_events_fk", "tableFrom": "articles_rels", "tableTo": "events", "columnsFrom": ["events_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_artists_fk": {"name": "articles_rels_artists_fk", "tableFrom": "articles_rels", "tableTo": "artists", "columnsFrom": ["artists_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_event_brands_fk": {"name": "articles_rels_event_brands_fk", "tableFrom": "articles_rels", "tableTo": "event_brands", "columnsFrom": ["event_brands_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_festivals_fk": {"name": "articles_rels_festivals_fk", "tableFrom": "articles_rels", "tableTo": "festivals", "columnsFrom": ["festivals_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_residencies_fk": {"name": "articles_rels_residencies_fk", "tableFrom": "articles_rels", "tableTo": "residencies", "columnsFrom": ["residencies_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_venues_fk": {"name": "articles_rels_venues_fk", "tableFrom": "articles_rels", "tableTo": "venues", "columnsFrom": ["venues_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_authors_fk": {"name": "articles_rels_authors_fk", "tableFrom": "articles_rels", "tableTo": "authors", "columnsFrom": ["authors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_ocho_episodes_fk": {"name": "articles_rels_ocho_episodes_fk", "tableFrom": "articles_rels", "tableTo": "ocho_episodes", "columnsFrom": ["ocho_episodes_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_hubs_fk": {"name": "articles_rels_hubs_fk", "tableFrom": "articles_rels", "tableTo": "hubs", "columnsFrom": ["hubs_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.artist_deals_status_history": {"name": "artist_deals_status_history", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "status": {"name": "status", "type": "enum_artist_deals_status_history_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"artist_deals_status_history_order_idx": {"name": "artist_deals_status_history_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artist_deals_status_history_parent_id_idx": {"name": "artist_deals_status_history_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"artist_deals_status_history_parent_id_fk": {"name": "artist_deals_status_history_parent_id_fk", "tableFrom": "artist_deals_status_history", "tableTo": "artist_deals", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.artist_deals": {"name": "artist_deals", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "artist_id": {"name": "artist_id", "type": "integer", "primaryKey": false, "notNull": true}, "event_id": {"name": "event_id", "type": "integer", "primaryKey": false, "notNull": true}, "expenses": {"name": "expenses", "type": "numeric", "primaryKey": false, "notNull": true, "default": 0}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"artist_deals_artist_idx": {"name": "artist_deals_artist_idx", "columns": [{"expression": "artist_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artist_deals_event_idx": {"name": "artist_deals_event_idx", "columns": [{"expression": "event_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artist_deals_updated_at_idx": {"name": "artist_deals_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artist_deals_created_at_idx": {"name": "artist_deals_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"artist_deals_artist_id_artists_id_fk": {"name": "artist_deals_artist_id_artists_id_fk", "tableFrom": "artist_deals", "tableTo": "artists", "columnsFrom": ["artist_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artist_deals_event_id_events_id_fk": {"name": "artist_deals_event_id_events_id_fk", "tableFrom": "artist_deals", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.artist_deals_rels": {"name": "artist_deals_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "documents_id": {"name": "documents_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"artist_deals_rels_order_idx": {"name": "artist_deals_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artist_deals_rels_parent_idx": {"name": "artist_deals_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artist_deals_rels_path_idx": {"name": "artist_deals_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artist_deals_rels_documents_id_idx": {"name": "artist_deals_rels_documents_id_idx", "columns": [{"expression": "documents_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"artist_deals_rels_parent_fk": {"name": "artist_deals_rels_parent_fk", "tableFrom": "artist_deals_rels", "tableTo": "artist_deals", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "artist_deals_rels_documents_fk": {"name": "artist_deals_rels_documents_fk", "tableFrom": "artist_deals_rels", "tableTo": "documents", "columnsFrom": ["documents_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.artists_representation": {"name": "artists_representation", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "territory_id": {"name": "territory_id", "type": "integer", "primaryKey": false, "notNull": true}, "coverage": {"name": "coverage", "type": "enum_artists_representation_coverage", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'global'"}, "agency_id": {"name": "agency_id", "type": "integer", "primaryKey": false, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": false}, "management_company_id": {"name": "management_company_id", "type": "integer", "primaryKey": false, "notNull": false}, "manager_id": {"name": "manager_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"artists_representation_order_idx": {"name": "artists_representation_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_representation_parent_id_idx": {"name": "artists_representation_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_representation_territory_idx": {"name": "artists_representation_territory_idx", "columns": [{"expression": "territory_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_representation_agency_idx": {"name": "artists_representation_agency_idx", "columns": [{"expression": "agency_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_representation_agent_idx": {"name": "artists_representation_agent_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_representation_management_company_idx": {"name": "artists_representation_management_company_idx", "columns": [{"expression": "management_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_representation_manager_idx": {"name": "artists_representation_manager_idx", "columns": [{"expression": "manager_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"artists_representation_territory_id_countries_id_fk": {"name": "artists_representation_territory_id_countries_id_fk", "tableFrom": "artists_representation", "tableTo": "countries", "columnsFrom": ["territory_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artists_representation_agency_id_agencies_id_fk": {"name": "artists_representation_agency_id_agencies_id_fk", "tableFrom": "artists_representation", "tableTo": "agencies", "columnsFrom": ["agency_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artists_representation_agent_id_agents_id_fk": {"name": "artists_representation_agent_id_agents_id_fk", "tableFrom": "artists_representation", "tableTo": "agents", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artists_representation_management_company_id_managment_companies_id_fk": {"name": "artists_representation_management_company_id_managment_companies_id_fk", "tableFrom": "artists_representation", "tableTo": "managment_companies", "columnsFrom": ["management_company_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artists_representation_manager_id_managers_id_fk": {"name": "artists_representation_manager_id_managers_id_fk", "tableFrom": "artists_representation", "tableTo": "managers", "columnsFrom": ["manager_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artists_representation_parent_id_fk": {"name": "artists_representation_parent_id_fk", "tableFrom": "artists_representation", "tableTo": "artists", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.artists_social_links": {"name": "artists_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "resource": {"name": "resource", "type": "enum_artists_social_links_resource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"artists_social_links_order_idx": {"name": "artists_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_social_links_parent_id_idx": {"name": "artists_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"artists_social_links_parent_id_fk": {"name": "artists_social_links_parent_id_fk", "tableFrom": "artists_social_links", "tableTo": "artists", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.artists": {"name": "artists", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "spotify_id": {"name": "spotify_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "chart_metric_external_id": {"name": "chart_metric_external_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "external_sanity_id": {"name": "external_sanity_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"artists_country_idx": {"name": "artists_country_idx", "columns": [{"expression": "country_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_preview_image_idx": {"name": "artists_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_overview_overview_overview_overview_hero_idx": {"name": "artists_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_meta_meta_meta_meta_image_idx": {"name": "artists_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_slug_idx": {"name": "artists_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_updated_at_idx": {"name": "artists_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_created_at_idx": {"name": "artists_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"artists_country_id_countries_id_fk": {"name": "artists_country_id_countries_id_fk", "tableFrom": "artists", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artists_preview_image_id_media_id_fk": {"name": "artists_preview_image_id_media_id_fk", "tableFrom": "artists", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artists_overview_overview_hero_id_media_id_fk": {"name": "artists_overview_overview_hero_id_media_id_fk", "tableFrom": "artists", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "artists_meta_meta_image_id_media_id_fk": {"name": "artists_meta_meta_image_id_media_id_fk", "tableFrom": "artists", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.artists_rels": {"name": "artists_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "genres_id": {"name": "genres_id", "type": "integer", "primaryKey": false, "notNull": false}, "residencies_id": {"name": "residencies_id", "type": "integer", "primaryKey": false, "notNull": false}, "managment_companies_id": {"name": "managment_companies_id", "type": "integer", "primaryKey": false, "notNull": false}, "managers_id": {"name": "managers_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"artists_rels_order_idx": {"name": "artists_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_rels_parent_idx": {"name": "artists_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_rels_path_idx": {"name": "artists_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_rels_genres_id_idx": {"name": "artists_rels_genres_id_idx", "columns": [{"expression": "genres_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_rels_residencies_id_idx": {"name": "artists_rels_residencies_id_idx", "columns": [{"expression": "residencies_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_rels_managment_companies_id_idx": {"name": "artists_rels_managment_companies_id_idx", "columns": [{"expression": "managment_companies_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "artists_rels_managers_id_idx": {"name": "artists_rels_managers_id_idx", "columns": [{"expression": "managers_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"artists_rels_parent_fk": {"name": "artists_rels_parent_fk", "tableFrom": "artists_rels", "tableTo": "artists", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "artists_rels_genres_fk": {"name": "artists_rels_genres_fk", "tableFrom": "artists_rels", "tableTo": "genres", "columnsFrom": ["genres_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "artists_rels_residencies_fk": {"name": "artists_rels_residencies_fk", "tableFrom": "artists_rels", "tableTo": "residencies", "columnsFrom": ["residencies_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "artists_rels_managment_companies_fk": {"name": "artists_rels_managment_companies_fk", "tableFrom": "artists_rels", "tableTo": "managment_companies", "columnsFrom": ["managment_companies_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "artists_rels_managers_fk": {"name": "artists_rels_managers_fk", "tableFrom": "artists_rels", "tableTo": "managers", "columnsFrom": ["managers_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.authors_social_links": {"name": "authors_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "resource": {"name": "resource", "type": "enum_authors_social_links_resource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"authors_social_links_order_idx": {"name": "authors_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_social_links_parent_id_idx": {"name": "authors_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"authors_social_links_parent_id_fk": {"name": "authors_social_links_parent_id_fk", "tableFrom": "authors_social_links", "tableTo": "authors", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.authors": {"name": "authors", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"authors_country_idx": {"name": "authors_country_idx", "columns": [{"expression": "country_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_preview_image_idx": {"name": "authors_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_overview_overview_overview_overview_hero_idx": {"name": "authors_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_meta_meta_meta_meta_image_idx": {"name": "authors_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_slug_idx": {"name": "authors_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_updated_at_idx": {"name": "authors_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "authors_created_at_idx": {"name": "authors_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"authors_country_id_countries_id_fk": {"name": "authors_country_id_countries_id_fk", "tableFrom": "authors", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "authors_preview_image_id_media_id_fk": {"name": "authors_preview_image_id_media_id_fk", "tableFrom": "authors", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "authors_overview_overview_hero_id_media_id_fk": {"name": "authors_overview_overview_hero_id_media_id_fk", "tableFrom": "authors", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "authors_meta_meta_image_id_media_id_fk": {"name": "authors_meta_meta_image_id_media_id_fk", "tableFrom": "authors", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.countries": {"name": "countries", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "flag_id": {"name": "flag_id", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"countries_flag_idx": {"name": "countries_flag_idx", "columns": [{"expression": "flag_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "countries_updated_at_idx": {"name": "countries_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "countries_created_at_idx": {"name": "countries_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"countries_flag_id_media_id_fk": {"name": "countries_flag_id_media_id_fk", "tableFrom": "countries", "tableTo": "media", "columnsFrom": ["flag_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}}, "indexes": {"documents_updated_at_idx": {"name": "documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documents_created_at_idx": {"name": "documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documents_filename_idx": {"name": "documents_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.event_brands_social_links": {"name": "event_brands_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "resource": {"name": "resource", "type": "enum_event_brands_social_links_resource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"event_brands_social_links_order_idx": {"name": "event_brands_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_brands_social_links_parent_id_idx": {"name": "event_brands_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"event_brands_social_links_parent_id_fk": {"name": "event_brands_social_links_parent_id_fk", "tableFrom": "event_brands_social_links", "tableTo": "event_brands", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.event_brands": {"name": "event_brands", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"event_brands_country_idx": {"name": "event_brands_country_idx", "columns": [{"expression": "country_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_brands_preview_image_idx": {"name": "event_brands_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_brands_overview_overview_overview_overview_hero_idx": {"name": "event_brands_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_brands_meta_meta_meta_meta_image_idx": {"name": "event_brands_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_brands_slug_idx": {"name": "event_brands_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_brands_updated_at_idx": {"name": "event_brands_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_brands_created_at_idx": {"name": "event_brands_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"event_brands_country_id_countries_id_fk": {"name": "event_brands_country_id_countries_id_fk", "tableFrom": "event_brands", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "event_brands_preview_image_id_media_id_fk": {"name": "event_brands_preview_image_id_media_id_fk", "tableFrom": "event_brands", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "event_brands_overview_overview_hero_id_media_id_fk": {"name": "event_brands_overview_overview_hero_id_media_id_fk", "tableFrom": "event_brands", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "event_brands_meta_meta_image_id_media_id_fk": {"name": "event_brands_meta_meta_image_id_media_id_fk", "tableFrom": "event_brands", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.event_organizers": {"name": "event_organizers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "presented_by": {"name": "presented_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "dice_credentials_login": {"name": "dice_credentials_login", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "dice_credentials_password": {"name": "dice_credentials_password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "dice_credentials_dice_partner_api_token": {"name": "dice_credentials_dice_partner_api_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "resident_advisor_credentials_login": {"name": "resident_advisor_credentials_login", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "resident_advisor_credentials_password": {"name": "resident_advisor_credentials_password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "qflow_credentials_login": {"name": "qflow_credentials_login", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "qflow_credentials_password": {"name": "qflow_credentials_password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"event_organizers_overview_overview_overview_overview_hero_idx": {"name": "event_organizers_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_organizers_meta_meta_meta_meta_image_idx": {"name": "event_organizers_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_organizers_slug_idx": {"name": "event_organizers_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_organizers_updated_at_idx": {"name": "event_organizers_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "event_organizers_created_at_idx": {"name": "event_organizers_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"event_organizers_overview_overview_hero_id_media_id_fk": {"name": "event_organizers_overview_overview_hero_id_media_id_fk", "tableFrom": "event_organizers", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "event_organizers_meta_meta_image_id_media_id_fk": {"name": "event_organizers_meta_meta_image_id_media_id_fk", "tableFrom": "event_organizers", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events_external_platform_source_urls": {"name": "events_external_platform_source_urls", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "platform": {"name": "platform", "type": "enum_events_external_platform_source_urls_platform", "typeSchema": "public", "primaryKey": false, "notNull": true}, "source_url": {"name": "source_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"events_external_platform_source_urls_order_idx": {"name": "events_external_platform_source_urls_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_external_platform_source_urls_parent_id_idx": {"name": "events_external_platform_source_urls_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_external_platform_source_urls_parent_id_fk": {"name": "events_external_platform_source_urls_parent_id_fk", "tableFrom": "events_external_platform_source_urls", "tableTo": "events", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events_lineup": {"name": "events_lineup", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "artist_id": {"name": "artist_id", "type": "integer", "primaryKey": false, "notNull": true}, "tier": {"name": "tier", "type": "enum_events_lineup_tier", "typeSchema": "public", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"events_lineup_order_idx": {"name": "events_lineup_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_lineup_parent_id_idx": {"name": "events_lineup_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_lineup_artist_idx": {"name": "events_lineup_artist_idx", "columns": [{"expression": "artist_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_lineup_artist_id_artists_id_fk": {"name": "events_lineup_artist_id_artists_id_fk", "tableFrom": "events_lineup", "tableTo": "artists", "columnsFrom": ["artist_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_lineup_parent_id_fk": {"name": "events_lineup_parent_id_fk", "tableFrom": "events_lineup", "tableTo": "events", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events_social_links": {"name": "events_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "resource": {"name": "resource", "type": "enum_events_social_links_resource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"events_social_links_order_idx": {"name": "events_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_social_links_parent_id_idx": {"name": "events_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_social_links_parent_id_fk": {"name": "events_social_links_parent_id_fk", "tableFrom": "events_social_links", "tableTo": "events", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events_faqs": {"name": "events_faqs", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "question": {"name": "question", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "answer": {"name": "answer", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"events_faqs_order_idx": {"name": "events_faqs_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_faqs_parent_id_idx": {"name": "events_faqs_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_faqs_parent_id_fk": {"name": "events_faqs_parent_id_fk", "tableFrom": "events_faqs", "tableTo": "events", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events_marketing_tracking_links": {"name": "events_marketing_tracking_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "platform": {"name": "platform", "type": "enum_events_marketing_tracking_links_platform", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'dice'"}, "channel": {"name": "channel", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "deals_params": {"name": "deals_params", "type": "enum_events_marketing_tracking_links_deals_params", "typeSchema": "public", "primaryKey": false, "notNull": false}, "campaign": {"name": "campaign", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"events_marketing_tracking_links_order_idx": {"name": "events_marketing_tracking_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_marketing_tracking_links_parent_id_idx": {"name": "events_marketing_tracking_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_marketing_tracking_links_parent_id_fk": {"name": "events_marketing_tracking_links_parent_id_fk", "tableFrom": "events_marketing_tracking_links", "tableTo": "events", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "event_organizer_id": {"name": "event_organizer_id", "type": "integer", "primaryKey": false, "notNull": false}, "origin": {"name": "origin", "type": "enum_events_origin", "typeSchema": "public", "primaryKey": false, "notNull": false}, "external_sanity_id": {"name": "external_sanity_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "external_dice_id": {"name": "external_dice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "external_resident_advisor_id": {"name": "external_resident_advisor_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "external_event_brite_id": {"name": "external_event_brite_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "festival_id": {"name": "festival_id", "type": "integer", "primaryKey": false, "notNull": false}, "event_brand_id": {"name": "event_brand_id", "type": "integer", "primaryKey": false, "notNull": false}, "residency_id": {"name": "residency_id", "type": "integer", "primaryKey": false, "notNull": false}, "location_location_type": {"name": "location_location_type", "type": "enum_events_location_location_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'venue'"}, "location_venue_id": {"name": "location_venue_id", "type": "integer", "primaryKey": false, "notNull": false}, "location_hub_id": {"name": "location_hub_id", "type": "integer", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "enum_events_timezone", "typeSchema": "public", "primaryKey": false, "notNull": false}, "announcement_date": {"name": "announcement_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "sale_on_date": {"name": "sale_on_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "sale_off_date": {"name": "sale_off_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "min_age": {"name": "min_age", "type": "numeric", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"events_event_organizer_idx": {"name": "events_event_organizer_idx", "columns": [{"expression": "event_organizer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_festival_idx": {"name": "events_festival_idx", "columns": [{"expression": "festival_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_event_brand_idx": {"name": "events_event_brand_idx", "columns": [{"expression": "event_brand_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_residency_idx": {"name": "events_residency_idx", "columns": [{"expression": "residency_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_location_location_venue_idx": {"name": "events_location_location_venue_idx", "columns": [{"expression": "location_venue_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_location_location_hub_idx": {"name": "events_location_location_hub_idx", "columns": [{"expression": "location_hub_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_preview_image_idx": {"name": "events_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_overview_overview_overview_overview_hero_idx": {"name": "events_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_meta_meta_meta_meta_image_idx": {"name": "events_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_slug_idx": {"name": "events_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_updated_at_idx": {"name": "events_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_created_at_idx": {"name": "events_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_event_organizer_id_event_organizers_id_fk": {"name": "events_event_organizer_id_event_organizers_id_fk", "tableFrom": "events", "tableTo": "event_organizers", "columnsFrom": ["event_organizer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_festival_id_festivals_id_fk": {"name": "events_festival_id_festivals_id_fk", "tableFrom": "events", "tableTo": "festivals", "columnsFrom": ["festival_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_event_brand_id_event_brands_id_fk": {"name": "events_event_brand_id_event_brands_id_fk", "tableFrom": "events", "tableTo": "event_brands", "columnsFrom": ["event_brand_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_residency_id_residencies_id_fk": {"name": "events_residency_id_residencies_id_fk", "tableFrom": "events", "tableTo": "residencies", "columnsFrom": ["residency_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_location_venue_id_venues_id_fk": {"name": "events_location_venue_id_venues_id_fk", "tableFrom": "events", "tableTo": "venues", "columnsFrom": ["location_venue_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_location_hub_id_hubs_id_fk": {"name": "events_location_hub_id_hubs_id_fk", "tableFrom": "events", "tableTo": "hubs", "columnsFrom": ["location_hub_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_preview_image_id_media_id_fk": {"name": "events_preview_image_id_media_id_fk", "tableFrom": "events", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_overview_overview_hero_id_media_id_fk": {"name": "events_overview_overview_hero_id_media_id_fk", "tableFrom": "events", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_meta_meta_image_id_media_id_fk": {"name": "events_meta_meta_image_id_media_id_fk", "tableFrom": "events", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events_texts": {"name": "events_texts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"events_texts_order_parent_idx": {"name": "events_texts_order_parent_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_texts_parent_fk": {"name": "events_texts_parent_fk", "tableFrom": "events_texts", "tableTo": "events", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events_rels": {"name": "events_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "genres_id": {"name": "genres_id", "type": "integer", "primaryKey": false, "notNull": false}, "events_id": {"name": "events_id", "type": "integer", "primaryKey": false, "notNull": false}, "promotions_id": {"name": "promotions_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"events_rels_order_idx": {"name": "events_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_rels_parent_idx": {"name": "events_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_rels_path_idx": {"name": "events_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_rels_genres_id_idx": {"name": "events_rels_genres_id_idx", "columns": [{"expression": "genres_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_rels_events_id_idx": {"name": "events_rels_events_id_idx", "columns": [{"expression": "events_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_rels_promotions_id_idx": {"name": "events_rels_promotions_id_idx", "columns": [{"expression": "promotions_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_rels_parent_fk": {"name": "events_rels_parent_fk", "tableFrom": "events_rels", "tableTo": "events", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "events_rels_genres_fk": {"name": "events_rels_genres_fk", "tableFrom": "events_rels", "tableTo": "genres", "columnsFrom": ["genres_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "events_rels_events_fk": {"name": "events_rels_events_fk", "tableFrom": "events_rels", "tableTo": "events", "columnsFrom": ["events_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "events_rels_promotions_fk": {"name": "events_rels_promotions_fk", "tableFrom": "events_rels", "tableTo": "promotions", "columnsFrom": ["promotions_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fan_notification_listners": {"name": "fan_notification_listners", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "enum_fan_notification_listners_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'eventPresale'"}, "event_id": {"name": "event_id", "type": "integer", "primaryKey": false, "notNull": true}, "fan_id": {"name": "fan_id", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"fan_notification_listners_event_idx": {"name": "fan_notification_listners_event_idx", "columns": [{"expression": "event_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_notification_listners_fan_idx": {"name": "fan_notification_listners_fan_idx", "columns": [{"expression": "fan_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_notification_listners_updated_at_idx": {"name": "fan_notification_listners_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_notification_listners_created_at_idx": {"name": "fan_notification_listners_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"fan_notification_listners_event_id_events_id_fk": {"name": "fan_notification_listners_event_id_events_id_fk", "tableFrom": "fan_notification_listners", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "fan_notification_listners_fan_id_fan_users_id_fk": {"name": "fan_notification_listners_fan_id_fan_users_id_fk", "tableFrom": "fan_notification_listners", "tableTo": "fan_users", "columnsFrom": ["fan_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fan_users": {"name": "fan_users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "phone_number_country_code": {"name": "phone_number_country_code", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "eth_wallet": {"name": "eth_wallet", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "dice_id": {"name": "dice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"fan_users_updated_at_idx": {"name": "fan_users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_users_created_at_idx": {"name": "fan_users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fan_users_rels": {"name": "fan_users_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "event_brands_id": {"name": "event_brands_id", "type": "integer", "primaryKey": false, "notNull": false}, "genres_id": {"name": "genres_id", "type": "integer", "primaryKey": false, "notNull": false}, "artists_id": {"name": "artists_id", "type": "integer", "primaryKey": false, "notNull": false}, "authors_id": {"name": "authors_id", "type": "integer", "primaryKey": false, "notNull": false}, "orders_id": {"name": "orders_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"fan_users_rels_order_idx": {"name": "fan_users_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_users_rels_parent_idx": {"name": "fan_users_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_users_rels_path_idx": {"name": "fan_users_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_users_rels_event_brands_id_idx": {"name": "fan_users_rels_event_brands_id_idx", "columns": [{"expression": "event_brands_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_users_rels_genres_id_idx": {"name": "fan_users_rels_genres_id_idx", "columns": [{"expression": "genres_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_users_rels_artists_id_idx": {"name": "fan_users_rels_artists_id_idx", "columns": [{"expression": "artists_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_users_rels_authors_id_idx": {"name": "fan_users_rels_authors_id_idx", "columns": [{"expression": "authors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fan_users_rels_orders_id_idx": {"name": "fan_users_rels_orders_id_idx", "columns": [{"expression": "orders_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"fan_users_rels_parent_fk": {"name": "fan_users_rels_parent_fk", "tableFrom": "fan_users_rels", "tableTo": "fan_users", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "fan_users_rels_event_brands_fk": {"name": "fan_users_rels_event_brands_fk", "tableFrom": "fan_users_rels", "tableTo": "event_brands", "columnsFrom": ["event_brands_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "fan_users_rels_genres_fk": {"name": "fan_users_rels_genres_fk", "tableFrom": "fan_users_rels", "tableTo": "genres", "columnsFrom": ["genres_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "fan_users_rels_artists_fk": {"name": "fan_users_rels_artists_fk", "tableFrom": "fan_users_rels", "tableTo": "artists", "columnsFrom": ["artists_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "fan_users_rels_authors_fk": {"name": "fan_users_rels_authors_fk", "tableFrom": "fan_users_rels", "tableTo": "authors", "columnsFrom": ["authors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "fan_users_rels_orders_fk": {"name": "fan_users_rels_orders_fk", "tableFrom": "fan_users_rels", "tableTo": "orders", "columnsFrom": ["orders_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.festival_profiles_social_links": {"name": "festival_profiles_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "resource": {"name": "resource", "type": "enum_festival_profiles_social_links_resource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"festival_profiles_social_links_order_idx": {"name": "festival_profiles_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festival_profiles_social_links_parent_id_idx": {"name": "festival_profiles_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"festival_profiles_social_links_parent_id_fk": {"name": "festival_profiles_social_links_parent_id_fk", "tableFrom": "festival_profiles_social_links", "tableTo": "festival_profiles", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.festival_profiles": {"name": "festival_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"festival_profiles_preview_image_idx": {"name": "festival_profiles_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festival_profiles_overview_overview_overview_overview_hero_idx": {"name": "festival_profiles_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festival_profiles_meta_meta_meta_meta_image_idx": {"name": "festival_profiles_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festival_profiles_slug_idx": {"name": "festival_profiles_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festival_profiles_updated_at_idx": {"name": "festival_profiles_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festival_profiles_created_at_idx": {"name": "festival_profiles_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"festival_profiles_preview_image_id_media_id_fk": {"name": "festival_profiles_preview_image_id_media_id_fk", "tableFrom": "festival_profiles", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "festival_profiles_overview_overview_hero_id_media_id_fk": {"name": "festival_profiles_overview_overview_hero_id_media_id_fk", "tableFrom": "festival_profiles", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "festival_profiles_meta_meta_image_id_media_id_fk": {"name": "festival_profiles_meta_meta_image_id_media_id_fk", "tableFrom": "festival_profiles", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.festivals_social_links": {"name": "festivals_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "resource": {"name": "resource", "type": "enum_festivals_social_links_resource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"festivals_social_links_order_idx": {"name": "festivals_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festivals_social_links_parent_id_idx": {"name": "festivals_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"festivals_social_links_parent_id_fk": {"name": "festivals_social_links_parent_id_fk", "tableFrom": "festivals_social_links", "tableTo": "festivals", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.festivals": {"name": "festivals", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "festival_brand_id": {"name": "festival_brand_id", "type": "integer", "primaryKey": false, "notNull": false}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "external_sanity_id": {"name": "external_sanity_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "origin": {"name": "origin", "type": "enum_festivals_origin", "typeSchema": "public", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"festivals_festival_brand_idx": {"name": "festivals_festival_brand_idx", "columns": [{"expression": "festival_brand_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festivals_preview_image_idx": {"name": "festivals_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festivals_overview_overview_overview_overview_hero_idx": {"name": "festivals_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festivals_meta_meta_meta_meta_image_idx": {"name": "festivals_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festivals_slug_idx": {"name": "festivals_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festivals_updated_at_idx": {"name": "festivals_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "festivals_created_at_idx": {"name": "festivals_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"festivals_festival_brand_id_festival_profiles_id_fk": {"name": "festivals_festival_brand_id_festival_profiles_id_fk", "tableFrom": "festivals", "tableTo": "festival_profiles", "columnsFrom": ["festival_brand_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "festivals_preview_image_id_media_id_fk": {"name": "festivals_preview_image_id_media_id_fk", "tableFrom": "festivals", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "festivals_overview_overview_hero_id_media_id_fk": {"name": "festivals_overview_overview_hero_id_media_id_fk", "tableFrom": "festivals", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "festivals_meta_meta_image_id_media_id_fk": {"name": "festivals_meta_meta_image_id_media_id_fk", "tableFrom": "festivals", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.genres": {"name": "genres", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "enum_genres_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "jsonb", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "full_title": {"name": "full_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"genres_overview_overview_overview_overview_hero_idx": {"name": "genres_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "genres_meta_meta_meta_meta_image_idx": {"name": "genres_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "genres_slug_idx": {"name": "genres_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "genres_updated_at_idx": {"name": "genres_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "genres_created_at_idx": {"name": "genres_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"genres_overview_overview_hero_id_media_id_fk": {"name": "genres_overview_overview_hero_id_media_id_fk", "tableFrom": "genres", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "genres_meta_meta_image_id_media_id_fk": {"name": "genres_meta_meta_image_id_media_id_fk", "tableFrom": "genres", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hubs": {"name": "hubs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "enum_hubs_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'city'"}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"hubs_parent_idx": {"name": "hubs_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "hubs_overview_overview_overview_overview_hero_idx": {"name": "hubs_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "hubs_meta_meta_meta_meta_image_idx": {"name": "hubs_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "hubs_updated_at_idx": {"name": "hubs_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "hubs_created_at_idx": {"name": "hubs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"hubs_parent_id_hubs_id_fk": {"name": "hubs_parent_id_hubs_id_fk", "tableFrom": "hubs", "tableTo": "hubs", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "hubs_overview_overview_hero_id_media_id_fk": {"name": "hubs_overview_overview_hero_id_media_id_fk", "tableFrom": "hubs", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "hubs_meta_meta_image_id_media_id_fk": {"name": "hubs_meta_meta_image_id_media_id_fk", "tableFrom": "hubs", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.managers": {"name": "managers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "general_contac_info_email": {"name": "general_contac_info_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "general_contac_info_phone_number": {"name": "general_contac_info_phone_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "managment_company_id": {"name": "managment_company_id", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"managers_managment_company_idx": {"name": "managers_managment_company_idx", "columns": [{"expression": "managment_company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "managers_updated_at_idx": {"name": "managers_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "managers_created_at_idx": {"name": "managers_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"managers_managment_company_id_managment_companies_id_fk": {"name": "managers_managment_company_id_managment_companies_id_fk", "tableFrom": "managers", "tableTo": "managment_companies", "columnsFrom": ["managment_company_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.managment_companies": {"name": "managment_companies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "general_contac_info_email": {"name": "general_contac_info_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "general_contac_info_phone_number": {"name": "general_contac_info_phone_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"managment_companies_updated_at_idx": {"name": "managment_companies_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "managment_companies_created_at_idx": {"name": "managment_companies_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "caption": {"name": "caption", "type": "jsonb", "primaryKey": false, "notNull": false}, "prefix": {"name": "prefix", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'media'"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_url": {"name": "sizes_thumbnail_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_width": {"name": "sizes_thumbnail_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_height": {"name": "sizes_thumbnail_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_mime_type": {"name": "sizes_thumbnail_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filesize": {"name": "sizes_thumbnail_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filename": {"name": "sizes_thumbnail_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_url": {"name": "sizes_square_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_width": {"name": "sizes_square_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_height": {"name": "sizes_square_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_mime_type": {"name": "sizes_square_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_filesize": {"name": "sizes_square_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_filename": {"name": "sizes_square_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_url": {"name": "sizes_small_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_width": {"name": "sizes_small_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_height": {"name": "sizes_small_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_mime_type": {"name": "sizes_small_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_filesize": {"name": "sizes_small_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_filename": {"name": "sizes_small_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_url": {"name": "sizes_medium_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_width": {"name": "sizes_medium_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_height": {"name": "sizes_medium_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_mime_type": {"name": "sizes_medium_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_filesize": {"name": "sizes_medium_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_filename": {"name": "sizes_medium_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_url": {"name": "sizes_large_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_width": {"name": "sizes_large_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_height": {"name": "sizes_large_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_mime_type": {"name": "sizes_large_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_filesize": {"name": "sizes_large_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_filename": {"name": "sizes_large_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_url": {"name": "sizes_xlarge_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_width": {"name": "sizes_xlarge_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_height": {"name": "sizes_xlarge_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_mime_type": {"name": "sizes_xlarge_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_filesize": {"name": "sizes_xlarge_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_filename": {"name": "sizes_xlarge_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_url": {"name": "sizes_og_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_width": {"name": "sizes_og_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_height": {"name": "sizes_og_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_mime_type": {"name": "sizes_og_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_filesize": {"name": "sizes_og_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_filename": {"name": "sizes_og_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_filename_idx": {"name": "media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_thumbnail_sizes_thumbnail_filename_idx": {"name": "media_sizes_thumbnail_sizes_thumbnail_filename_idx", "columns": [{"expression": "sizes_thumbnail_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_square_sizes_square_filename_idx": {"name": "media_sizes_square_sizes_square_filename_idx", "columns": [{"expression": "sizes_square_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_small_sizes_small_filename_idx": {"name": "media_sizes_small_sizes_small_filename_idx", "columns": [{"expression": "sizes_small_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_medium_sizes_medium_filename_idx": {"name": "media_sizes_medium_sizes_medium_filename_idx", "columns": [{"expression": "sizes_medium_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_large_sizes_large_filename_idx": {"name": "media_sizes_large_sizes_large_filename_idx", "columns": [{"expression": "sizes_large_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_xlarge_sizes_xlarge_filename_idx": {"name": "media_sizes_xlarge_sizes_xlarge_filename_idx", "columns": [{"expression": "sizes_xlarge_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_og_sizes_og_filename_idx": {"name": "media_sizes_og_sizes_og_filename_idx", "columns": [{"expression": "sizes_og_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ocho_episodes": {"name": "ocho_episodes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "episode_number": {"name": "episode_number", "type": "numeric", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "jsonb", "primaryKey": false, "notNull": true}, "video_url": {"name": "video_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "podcast_url": {"name": "podcast_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"ocho_episodes_preview_image_idx": {"name": "ocho_episodes_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ocho_episodes_overview_overview_overview_overview_hero_idx": {"name": "ocho_episodes_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ocho_episodes_meta_meta_meta_meta_image_idx": {"name": "ocho_episodes_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ocho_episodes_slug_idx": {"name": "ocho_episodes_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ocho_episodes_updated_at_idx": {"name": "ocho_episodes_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ocho_episodes_created_at_idx": {"name": "ocho_episodes_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ocho_episodes_preview_image_id_media_id_fk": {"name": "ocho_episodes_preview_image_id_media_id_fk", "tableFrom": "ocho_episodes", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "ocho_episodes_overview_overview_hero_id_media_id_fk": {"name": "ocho_episodes_overview_overview_hero_id_media_id_fk", "tableFrom": "ocho_episodes", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "ocho_episodes_meta_meta_image_id_media_id_fk": {"name": "ocho_episodes_meta_meta_image_id_media_id_fk", "tableFrom": "ocho_episodes", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ocho_episodes_rels": {"name": "ocho_episodes_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "artists_id": {"name": "artists_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"ocho_episodes_rels_order_idx": {"name": "ocho_episodes_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ocho_episodes_rels_parent_idx": {"name": "ocho_episodes_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ocho_episodes_rels_path_idx": {"name": "ocho_episodes_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ocho_episodes_rels_artists_id_idx": {"name": "ocho_episodes_rels_artists_id_idx", "columns": [{"expression": "artists_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ocho_episodes_rels_parent_fk": {"name": "ocho_episodes_rels_parent_fk", "tableFrom": "ocho_episodes_rels", "tableTo": "ocho_episodes", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ocho_episodes_rels_artists_fk": {"name": "ocho_episodes_rels_artists_fk", "tableFrom": "ocho_episodes_rels", "tableTo": "artists", "columnsFrom": ["artists_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "external_dice_id": {"name": "external_dice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "origin": {"name": "origin", "type": "enum_orders_origin", "typeSchema": "public", "primaryKey": false, "notNull": false}, "external_resident_advisor_id": {"name": "external_resident_advisor_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "external_event_brite_id": {"name": "external_event_brite_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "event_id": {"name": "event_id", "type": "integer", "primaryKey": false, "notNull": false}, "fan_email": {"name": "fan_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"orders_event_idx": {"name": "orders_event_idx", "columns": [{"expression": "event_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_updated_at_idx": {"name": "orders_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_created_at_idx": {"name": "orders_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"orders_event_id_events_id_fk": {"name": "orders_event_id_events_id_fk", "tableFrom": "orders", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders_rels": {"name": "orders_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "tickets_id": {"name": "tickets_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"orders_rels_order_idx": {"name": "orders_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_rels_parent_idx": {"name": "orders_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_rels_path_idx": {"name": "orders_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_rels_tickets_id_idx": {"name": "orders_rels_tickets_id_idx", "columns": [{"expression": "tickets_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"orders_rels_parent_fk": {"name": "orders_rels_parent_fk", "tableFrom": "orders_rels", "tableTo": "orders", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_rels_tickets_fk": {"name": "orders_rels_tickets_fk", "tableFrom": "orders_rels", "tableTo": "tickets", "columnsFrom": ["tickets_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_cta_links": {"name": "pages_blocks_cta_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum_pages_blocks_cta_links_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum_pages_blocks_cta_links_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}}, "indexes": {"pages_blocks_cta_links_order_idx": {"name": "pages_blocks_cta_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_cta_links_parent_id_idx": {"name": "pages_blocks_cta_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_cta_links_parent_id_fk": {"name": "pages_blocks_cta_links_parent_id_fk", "tableFrom": "pages_blocks_cta_links", "tableTo": "pages_blocks_cta", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_cta": {"name": "pages_blocks_cta", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_cta_order_idx": {"name": "pages_blocks_cta_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_cta_parent_id_idx": {"name": "pages_blocks_cta_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_cta_path_idx": {"name": "pages_blocks_cta_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_cta_parent_id_fk": {"name": "pages_blocks_cta_parent_id_fk", "tableFrom": "pages_blocks_cta", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_content_columns": {"name": "pages_blocks_content_columns", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "size": {"name": "size", "type": "enum_pages_blocks_content_columns_size", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'oneThird'"}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "enable_link": {"name": "enable_link", "type": "boolean", "primaryKey": false, "notNull": false}, "link_type": {"name": "link_type", "type": "enum_pages_blocks_content_columns_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum_pages_blocks_content_columns_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}}, "indexes": {"pages_blocks_content_columns_order_idx": {"name": "pages_blocks_content_columns_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_content_columns_parent_id_idx": {"name": "pages_blocks_content_columns_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_content_columns_parent_id_fk": {"name": "pages_blocks_content_columns_parent_id_fk", "tableFrom": "pages_blocks_content_columns", "tableTo": "pages_blocks_content", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_content": {"name": "pages_blocks_content", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_content_order_idx": {"name": "pages_blocks_content_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_content_parent_id_idx": {"name": "pages_blocks_content_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_content_path_idx": {"name": "pages_blocks_content_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_content_parent_id_fk": {"name": "pages_blocks_content_parent_id_fk", "tableFrom": "pages_blocks_content", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_media_block": {"name": "pages_blocks_media_block", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_media_block_order_idx": {"name": "pages_blocks_media_block_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_media_block_parent_id_idx": {"name": "pages_blocks_media_block_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_media_block_path_idx": {"name": "pages_blocks_media_block_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_media_block_media_idx": {"name": "pages_blocks_media_block_media_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_media_block_media_id_media_id_fk": {"name": "pages_blocks_media_block_media_id_media_id_fk", "tableFrom": "pages_blocks_media_block", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "pages_blocks_media_block_parent_id_fk": {"name": "pages_blocks_media_block_parent_id_fk", "tableFrom": "pages_blocks_media_block", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_archive": {"name": "pages_blocks_archive", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "intro_content": {"name": "intro_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "populate_by": {"name": "populate_by", "type": "enum_pages_blocks_archive_populate_by", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'collection'"}, "relation_to": {"name": "relation_to", "type": "enum_pages_blocks_archive_relation_to", "typeSchema": "public", "primaryKey": false, "notNull": false}, "limit": {"name": "limit", "type": "numeric", "primaryKey": false, "notNull": false, "default": 10}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_archive_order_idx": {"name": "pages_blocks_archive_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_archive_parent_id_idx": {"name": "pages_blocks_archive_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_archive_path_idx": {"name": "pages_blocks_archive_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_archive_parent_id_fk": {"name": "pages_blocks_archive_parent_id_fk", "tableFrom": "pages_blocks_archive", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_events_block": {"name": "pages_blocks_events_block", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "heading": {"name": "heading", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'Events'"}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_events_block_order_idx": {"name": "pages_blocks_events_block_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_events_block_parent_id_idx": {"name": "pages_blocks_events_block_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_events_block_path_idx": {"name": "pages_blocks_events_block_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_events_block_parent_id_fk": {"name": "pages_blocks_events_block_parent_id_fk", "tableFrom": "pages_blocks_events_block", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_home_events_section": {"name": "pages_blocks_home_events_section", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'Upcoming Gray Area Events'"}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_home_events_section_order_idx": {"name": "pages_blocks_home_events_section_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_home_events_section_parent_id_idx": {"name": "pages_blocks_home_events_section_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_home_events_section_path_idx": {"name": "pages_blocks_home_events_section_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_home_events_section_parent_id_fk": {"name": "pages_blocks_home_events_section_parent_id_fk", "tableFrom": "pages_blocks_home_events_section", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages": {"name": "pages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_pages_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"pages_meta_meta_image_idx": {"name": "pages_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_slug_idx": {"name": "pages_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_updated_at_idx": {"name": "pages_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_created_at_idx": {"name": "pages_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages__status_idx": {"name": "pages__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_meta_image_id_media_id_fk": {"name": "pages_meta_image_id_media_id_fk", "tableFrom": "pages", "tableTo": "media", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_rels": {"name": "pages_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "events_id": {"name": "events_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"pages_rels_order_idx": {"name": "pages_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_parent_idx": {"name": "pages_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_path_idx": {"name": "pages_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_pages_id_idx": {"name": "pages_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_events_id_idx": {"name": "pages_rels_events_id_idx", "columns": [{"expression": "events_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_rels_parent_fk": {"name": "pages_rels_parent_fk", "tableFrom": "pages_rels", "tableTo": "pages", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pages_rels_pages_fk": {"name": "pages_rels_pages_fk", "tableFrom": "pages_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pages_rels_events_fk": {"name": "pages_rels_events_fk", "tableFrom": "pages_rels", "tableTo": "events", "columnsFrom": ["events_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_cta_links": {"name": "_pages_v_blocks_cta_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum__pages_v_blocks_cta_links_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum__pages_v_blocks_cta_links_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_cta_links_order_idx": {"name": "_pages_v_blocks_cta_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_cta_links_parent_id_idx": {"name": "_pages_v_blocks_cta_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_cta_links_parent_id_fk": {"name": "_pages_v_blocks_cta_links_parent_id_fk", "tableFrom": "_pages_v_blocks_cta_links", "tableTo": "_pages_v_blocks_cta", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_cta": {"name": "_pages_v_blocks_cta", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_cta_order_idx": {"name": "_pages_v_blocks_cta_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_cta_parent_id_idx": {"name": "_pages_v_blocks_cta_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_cta_path_idx": {"name": "_pages_v_blocks_cta_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_cta_parent_id_fk": {"name": "_pages_v_blocks_cta_parent_id_fk", "tableFrom": "_pages_v_blocks_cta", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_content_columns": {"name": "_pages_v_blocks_content_columns", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "size": {"name": "size", "type": "enum__pages_v_blocks_content_columns_size", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'oneThird'"}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "enable_link": {"name": "enable_link", "type": "boolean", "primaryKey": false, "notNull": false}, "link_type": {"name": "link_type", "type": "enum__pages_v_blocks_content_columns_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum__pages_v_blocks_content_columns_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_content_columns_order_idx": {"name": "_pages_v_blocks_content_columns_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_content_columns_parent_id_idx": {"name": "_pages_v_blocks_content_columns_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_content_columns_parent_id_fk": {"name": "_pages_v_blocks_content_columns_parent_id_fk", "tableFrom": "_pages_v_blocks_content_columns", "tableTo": "_pages_v_blocks_content", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_content": {"name": "_pages_v_blocks_content", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_content_order_idx": {"name": "_pages_v_blocks_content_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_content_parent_id_idx": {"name": "_pages_v_blocks_content_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_content_path_idx": {"name": "_pages_v_blocks_content_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_content_parent_id_fk": {"name": "_pages_v_blocks_content_parent_id_fk", "tableFrom": "_pages_v_blocks_content", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_media_block": {"name": "_pages_v_blocks_media_block", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_media_block_order_idx": {"name": "_pages_v_blocks_media_block_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_media_block_parent_id_idx": {"name": "_pages_v_blocks_media_block_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_media_block_path_idx": {"name": "_pages_v_blocks_media_block_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_media_block_media_idx": {"name": "_pages_v_blocks_media_block_media_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_media_block_media_id_media_id_fk": {"name": "_pages_v_blocks_media_block_media_id_media_id_fk", "tableFrom": "_pages_v_blocks_media_block", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_blocks_media_block_parent_id_fk": {"name": "_pages_v_blocks_media_block_parent_id_fk", "tableFrom": "_pages_v_blocks_media_block", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_archive": {"name": "_pages_v_blocks_archive", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "intro_content": {"name": "intro_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "populate_by": {"name": "populate_by", "type": "enum__pages_v_blocks_archive_populate_by", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'collection'"}, "relation_to": {"name": "relation_to", "type": "enum__pages_v_blocks_archive_relation_to", "typeSchema": "public", "primaryKey": false, "notNull": false}, "limit": {"name": "limit", "type": "numeric", "primaryKey": false, "notNull": false, "default": 10}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_archive_order_idx": {"name": "_pages_v_blocks_archive_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_archive_parent_id_idx": {"name": "_pages_v_blocks_archive_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_archive_path_idx": {"name": "_pages_v_blocks_archive_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_archive_parent_id_fk": {"name": "_pages_v_blocks_archive_parent_id_fk", "tableFrom": "_pages_v_blocks_archive", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_events_block": {"name": "_pages_v_blocks_events_block", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "heading": {"name": "heading", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'Events'"}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_events_block_order_idx": {"name": "_pages_v_blocks_events_block_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_events_block_parent_id_idx": {"name": "_pages_v_blocks_events_block_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_events_block_path_idx": {"name": "_pages_v_blocks_events_block_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_events_block_parent_id_fk": {"name": "_pages_v_blocks_events_block_parent_id_fk", "tableFrom": "_pages_v_blocks_events_block", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_home_events_section": {"name": "_pages_v_blocks_home_events_section", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'Upcoming Gray Area Events'"}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_home_events_section_order_idx": {"name": "_pages_v_blocks_home_events_section_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_home_events_section_parent_id_idx": {"name": "_pages_v_blocks_home_events_section_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_home_events_section_path_idx": {"name": "_pages_v_blocks_home_events_section_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_home_events_section_parent_id_fk": {"name": "_pages_v_blocks_home_events_section_parent_id_fk", "tableFrom": "_pages_v_blocks_home_events_section", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v": {"name": "_pages_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__pages_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}, "autosave": {"name": "autosave", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_parent_idx": {"name": "_pages_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_meta_version_meta_image_idx": {"name": "_pages_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_slug_idx": {"name": "_pages_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_updated_at_idx": {"name": "_pages_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_created_at_idx": {"name": "_pages_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version__status_idx": {"name": "_pages_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_created_at_idx": {"name": "_pages_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_updated_at_idx": {"name": "_pages_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_latest_idx": {"name": "_pages_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_autosave_idx": {"name": "_pages_v_autosave_idx", "columns": [{"expression": "autosave", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_parent_id_pages_id_fk": {"name": "_pages_v_parent_id_pages_id_fk", "tableFrom": "_pages_v", "tableTo": "pages", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_version_meta_image_id_media_id_fk": {"name": "_pages_v_version_meta_image_id_media_id_fk", "tableFrom": "_pages_v", "tableTo": "media", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_rels": {"name": "_pages_v_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "events_id": {"name": "events_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_rels_order_idx": {"name": "_pages_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_parent_idx": {"name": "_pages_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_path_idx": {"name": "_pages_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_pages_id_idx": {"name": "_pages_v_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_events_id_idx": {"name": "_pages_v_rels_events_id_idx", "columns": [{"expression": "events_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_rels_parent_fk": {"name": "_pages_v_rels_parent_fk", "tableFrom": "_pages_v_rels", "tableTo": "_pages_v", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_pages_v_rels_pages_fk": {"name": "_pages_v_rels_pages_fk", "tableFrom": "_pages_v_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_pages_v_rels_events_fk": {"name": "_pages_v_rels_events_fk", "tableFrom": "_pages_v_rels", "tableTo": "events", "columnsFrom": ["events_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promotions_code_locks": {"name": "promotions_code_locks", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "claimed_by_id": {"name": "claimed_by_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"promotions_code_locks_order_idx": {"name": "promotions_code_locks_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promotions_code_locks_parent_id_idx": {"name": "promotions_code_locks_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promotions_code_locks_claimed_by_idx": {"name": "promotions_code_locks_claimed_by_idx", "columns": [{"expression": "claimed_by_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"promotions_code_locks_claimed_by_id_fan_users_id_fk": {"name": "promotions_code_locks_claimed_by_id_fan_users_id_fk", "tableFrom": "promotions_code_locks", "tableTo": "fan_users", "columnsFrom": ["claimed_by_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "promotions_code_locks_parent_id_fk": {"name": "promotions_code_locks_parent_id_fk", "tableFrom": "promotions_code_locks", "tableTo": "promotions", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promotions": {"name": "promotions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "max_redemptions": {"name": "max_redemptions", "type": "numeric", "primaryKey": false, "notNull": true, "default": 0}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"promotions_updated_at_idx": {"name": "promotions_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promotions_created_at_idx": {"name": "promotions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.residencies_social_links": {"name": "residencies_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "resource": {"name": "resource", "type": "enum_residencies_social_links_resource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"residencies_social_links_order_idx": {"name": "residencies_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "residencies_social_links_parent_id_idx": {"name": "residencies_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"residencies_social_links_parent_id_fk": {"name": "residencies_social_links_parent_id_fk", "tableFrom": "residencies_social_links", "tableTo": "residencies", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.residencies": {"name": "residencies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "event_brand_id": {"name": "event_brand_id", "type": "integer", "primaryKey": false, "notNull": false}, "location_venue_id": {"name": "location_venue_id", "type": "integer", "primaryKey": false, "notNull": false}, "location_geo_location_location_name": {"name": "location_geo_location_location_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "location_geo_location_link": {"name": "location_geo_location_link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"residencies_event_brand_idx": {"name": "residencies_event_brand_idx", "columns": [{"expression": "event_brand_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "residencies_location_location_venue_idx": {"name": "residencies_location_location_venue_idx", "columns": [{"expression": "location_venue_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "residencies_preview_image_idx": {"name": "residencies_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "residencies_overview_overview_overview_overview_hero_idx": {"name": "residencies_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "residencies_meta_meta_meta_meta_image_idx": {"name": "residencies_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "residencies_slug_idx": {"name": "residencies_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "residencies_updated_at_idx": {"name": "residencies_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "residencies_created_at_idx": {"name": "residencies_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"residencies_event_brand_id_event_brands_id_fk": {"name": "residencies_event_brand_id_event_brands_id_fk", "tableFrom": "residencies", "tableTo": "event_brands", "columnsFrom": ["event_brand_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "residencies_location_venue_id_venues_id_fk": {"name": "residencies_location_venue_id_venues_id_fk", "tableFrom": "residencies", "tableTo": "venues", "columnsFrom": ["location_venue_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "residencies_preview_image_id_media_id_fk": {"name": "residencies_preview_image_id_media_id_fk", "tableFrom": "residencies", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "residencies_overview_overview_hero_id_media_id_fk": {"name": "residencies_overview_overview_hero_id_media_id_fk", "tableFrom": "residencies", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "residencies_meta_meta_image_id_media_id_fk": {"name": "residencies_meta_meta_image_id_media_id_fk", "tableFrom": "residencies", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tickets": {"name": "tickets", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "external_dice_id": {"name": "external_dice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "admitted_date": {"name": "admitted_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "total": {"name": "total", "type": "numeric", "primaryKey": false, "notNull": false}, "ticket_type_id": {"name": "ticket_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tickets_ticket_type_idx": {"name": "tickets_ticket_type_idx", "columns": [{"expression": "ticket_type_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tickets_updated_at_idx": {"name": "tickets_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tickets_created_at_idx": {"name": "tickets_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tickets_ticket_type_id_ticket_types_id_fk": {"name": "tickets_ticket_type_id_ticket_types_id_fk", "tableFrom": "tickets", "tableTo": "ticket_types", "columnsFrom": ["ticket_type_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ticket_types": {"name": "ticket_types", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "origin": {"name": "origin", "type": "enum_ticket_types_origin", "typeSchema": "public", "primaryKey": false, "notNull": false}, "external_dice_id_ticket_type_id": {"name": "external_dice_id_ticket_type_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "external_dice_id_price_tier_id": {"name": "external_dice_id_price_tier_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "external_resident_advisor_id": {"name": "external_resident_advisor_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "external_event_brite_id": {"name": "external_event_brite_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "ticket_type_name": {"name": "ticket_type_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "allocation": {"name": "allocation", "type": "numeric", "primaryKey": false, "notNull": true}, "face_value": {"name": "face_value", "type": "numeric", "primaryKey": false, "notNull": true}, "fee": {"name": "fee", "type": "numeric", "primaryKey": false, "notNull": false}, "total": {"name": "total", "type": "numeric", "primaryKey": false, "notNull": false}, "event_id": {"name": "event_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"ticket_types_event_idx": {"name": "ticket_types_event_idx", "columns": [{"expression": "event_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ticket_types_updated_at_idx": {"name": "ticket_types_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ticket_types_created_at_idx": {"name": "ticket_types_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ticket_types_event_id_events_id_fk": {"name": "ticket_types_event_id_events_id_fk", "tableFrom": "ticket_types", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_roles": {"name": "users_roles", "schema": "", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_users_roles", "typeSchema": "public", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"users_roles_order_idx": {"name": "users_roles_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_roles_parent_idx": {"name": "users_roles_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_roles_parent_fk": {"name": "users_roles_parent_fk", "tableFrom": "users_roles", "tableTo": "users", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_tenants_roles": {"name": "users_tenants_roles", "schema": "", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_users_tenants_roles", "typeSchema": "public", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"users_tenants_roles_order_idx": {"name": "users_tenants_roles_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_tenants_roles_parent_idx": {"name": "users_tenants_roles_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_tenants_roles_parent_fk": {"name": "users_tenants_roles_parent_fk", "tableFrom": "users_tenants_roles", "tableTo": "users_tenants", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_tenants": {"name": "users_tenants", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {"users_tenants_order_idx": {"name": "users_tenants_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_tenants_parent_id_idx": {"name": "users_tenants_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_tenants_tenant_idx": {"name": "users_tenants_tenant_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_tenants_tenant_id_event_organizers_id_fk": {"name": "users_tenants_tenant_id_event_organizers_id_fk", "tableFrom": "users_tenants", "tableTo": "event_organizers", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "users_tenants_parent_id_fk": {"name": "users_tenants_parent_id_fk", "tableFrom": "users_tenants", "tableTo": "users", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.venues_capacities": {"name": "venues_capacities", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "capacity": {"name": "capacity", "type": "numeric", "primaryKey": false, "notNull": true}}, "indexes": {"venues_capacities_order_idx": {"name": "venues_capacities_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_capacities_parent_id_idx": {"name": "venues_capacities_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"venues_capacities_parent_id_fk": {"name": "venues_capacities_parent_id_fk", "tableFrom": "venues_capacities", "tableTo": "venues", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.venues_internal_contacts": {"name": "venues_internal_contacts", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "enum_venues_internal_contacts_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "email_address": {"name": "email_address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"venues_internal_contacts_order_idx": {"name": "venues_internal_contacts_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_internal_contacts_parent_id_idx": {"name": "venues_internal_contacts_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"venues_internal_contacts_parent_id_fk": {"name": "venues_internal_contacts_parent_id_fk", "tableFrom": "venues_internal_contacts", "tableTo": "venues", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.venues_social_links": {"name": "venues_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "resource": {"name": "resource", "type": "enum_venues_social_links_resource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "link": {"name": "link", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"venues_social_links_order_idx": {"name": "venues_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_social_links_parent_id_idx": {"name": "venues_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"venues_social_links_parent_id_fk": {"name": "venues_social_links_parent_id_fk", "tableFrom": "venues_social_links", "tableTo": "venues", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.venues": {"name": "venues", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "coordinates": {"name": "coordinates", "type": "geometry(Point)", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "jsonb", "primaryKey": false, "notNull": false}, "preview_image_id": {"name": "preview_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "house_rules_id": {"name": "house_rules_id", "type": "integer", "primaryKey": false, "notNull": false}, "invoicing_info": {"name": "invoicing_info", "type": "jsonb", "primaryKey": false, "notNull": false}, "closest_airport": {"name": "closest_airport", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "origin": {"name": "origin", "type": "enum_venues_origin", "typeSchema": "public", "primaryKey": false, "notNull": false}, "external_sanity_id": {"name": "external_sanity_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "overview_overview_hero_id": {"name": "overview_overview_hero_id", "type": "integer", "primaryKey": false, "notNull": false}, "overview_overview_content": {"name": "overview_overview_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_meta_title": {"name": "meta_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_meta_image_id": {"name": "meta_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_meta_description": {"name": "meta_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"venues_preview_image_idx": {"name": "venues_preview_image_idx", "columns": [{"expression": "preview_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_house_rules_idx": {"name": "venues_house_rules_idx", "columns": [{"expression": "house_rules_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_overview_overview_overview_overview_hero_idx": {"name": "venues_overview_overview_overview_overview_hero_idx", "columns": [{"expression": "overview_overview_hero_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_meta_meta_meta_meta_image_idx": {"name": "venues_meta_meta_meta_meta_image_idx", "columns": [{"expression": "meta_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_slug_idx": {"name": "venues_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_updated_at_idx": {"name": "venues_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_created_at_idx": {"name": "venues_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"venues_preview_image_id_media_id_fk": {"name": "venues_preview_image_id_media_id_fk", "tableFrom": "venues", "tableTo": "media", "columnsFrom": ["preview_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "venues_house_rules_id_documents_id_fk": {"name": "venues_house_rules_id_documents_id_fk", "tableFrom": "venues", "tableTo": "documents", "columnsFrom": ["house_rules_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "venues_overview_overview_hero_id_media_id_fk": {"name": "venues_overview_overview_hero_id_media_id_fk", "tableFrom": "venues", "tableTo": "media", "columnsFrom": ["overview_overview_hero_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "venues_meta_meta_image_id_media_id_fk": {"name": "venues_meta_meta_image_id_media_id_fk", "tableFrom": "venues", "tableTo": "media", "columnsFrom": ["meta_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.venues_texts": {"name": "venues_texts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"venues_texts_order_parent_idx": {"name": "venues_texts_order_parent_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"venues_texts_parent_fk": {"name": "venues_texts_parent_fk", "tableFrom": "venues_texts", "tableTo": "venues", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.venues_rels": {"name": "venues_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "documents_id": {"name": "documents_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"venues_rels_order_idx": {"name": "venues_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_rels_parent_idx": {"name": "venues_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_rels_path_idx": {"name": "venues_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_rels_media_id_idx": {"name": "venues_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "venues_rels_documents_id_idx": {"name": "venues_rels_documents_id_idx", "columns": [{"expression": "documents_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"venues_rels_parent_fk": {"name": "venues_rels_parent_fk", "tableFrom": "venues_rels", "tableTo": "venues", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "venues_rels_media_fk": {"name": "venues_rels_media_fk", "tableFrom": "venues_rels", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "venues_rels_documents_fk": {"name": "venues_rels_documents_fk", "tableFrom": "venues_rels", "tableTo": "documents", "columnsFrom": ["documents_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.redirects": {"name": "redirects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "from": {"name": "from", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "to_type": {"name": "to_type", "type": "enum_redirects_to_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "to_url": {"name": "to_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"redirects_from_idx": {"name": "redirects_from_idx", "columns": [{"expression": "from", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_updated_at_idx": {"name": "redirects_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_created_at_idx": {"name": "redirects_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.redirects_rels": {"name": "redirects_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"redirects_rels_order_idx": {"name": "redirects_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_rels_parent_idx": {"name": "redirects_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_rels_path_idx": {"name": "redirects_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_rels_pages_id_idx": {"name": "redirects_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"redirects_rels_parent_fk": {"name": "redirects_rels_parent_fk", "tableFrom": "redirects_rels", "tableTo": "redirects", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "redirects_rels_pages_fk": {"name": "redirects_rels_pages_fk", "tableFrom": "redirects_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search_categories": {"name": "search_categories", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "relation_to": {"name": "relation_to", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"search_categories_order_idx": {"name": "search_categories_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_categories_parent_id_idx": {"name": "search_categories_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"search_categories_parent_id_fk": {"name": "search_categories_parent_id_fk", "tableFrom": "search_categories", "tableTo": "search", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search": {"name": "search", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "numeric", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"search_slug_idx": {"name": "search_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_meta_meta_image_idx": {"name": "search_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_updated_at_idx": {"name": "search_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_created_at_idx": {"name": "search_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"search_meta_image_id_media_id_fk": {"name": "search_meta_image_id_media_id_fk", "tableFrom": "search", "tableTo": "media", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search_rels": {"name": "search_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "articles_id": {"name": "articles_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"search_rels_order_idx": {"name": "search_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_rels_parent_idx": {"name": "search_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_rels_path_idx": {"name": "search_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_rels_articles_id_idx": {"name": "search_rels_articles_id_idx", "columns": [{"expression": "articles_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"search_rels_parent_fk": {"name": "search_rels_parent_fk", "tableFrom": "search_rels", "tableTo": "search", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "search_rels_articles_fk": {"name": "search_rels_articles_fk", "tableFrom": "search_rels", "tableTo": "articles", "columnsFrom": ["articles_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_jobs_log": {"name": "payload_jobs_log", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "executed_at": {"name": "executed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "completed_at": {"name": "completed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "task_slug": {"name": "task_slug", "type": "enum_payload_jobs_log_task_slug", "typeSchema": "public", "primaryKey": false, "notNull": true}, "task_i_d": {"name": "task_i_d", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "input": {"name": "input", "type": "jsonb", "primaryKey": false, "notNull": false}, "output": {"name": "output", "type": "jsonb", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "enum_payload_jobs_log_state", "typeSchema": "public", "primaryKey": false, "notNull": true}, "error": {"name": "error", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"payload_jobs_log_order_idx": {"name": "payload_jobs_log_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_log_parent_id_idx": {"name": "payload_jobs_log_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_jobs_log_parent_id_fk": {"name": "payload_jobs_log_parent_id_fk", "tableFrom": "payload_jobs_log", "tableTo": "payload_jobs", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_jobs": {"name": "payload_jobs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "input": {"name": "input", "type": "jsonb", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "total_tried": {"name": "total_tried", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "has_error": {"name": "has_error", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "error": {"name": "error", "type": "jsonb", "primaryKey": false, "notNull": false}, "workflow_slug": {"name": "workflow_slug", "type": "enum_payload_jobs_workflow_slug", "typeSchema": "public", "primaryKey": false, "notNull": false}, "task_slug": {"name": "task_slug", "type": "enum_payload_jobs_task_slug", "typeSchema": "public", "primaryKey": false, "notNull": false}, "queue": {"name": "queue", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'default'"}, "wait_until": {"name": "wait_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "processing": {"name": "processing", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_jobs_completed_at_idx": {"name": "payload_jobs_completed_at_idx", "columns": [{"expression": "completed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_total_tried_idx": {"name": "payload_jobs_total_tried_idx", "columns": [{"expression": "total_tried", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_has_error_idx": {"name": "payload_jobs_has_error_idx", "columns": [{"expression": "has_error", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_workflow_slug_idx": {"name": "payload_jobs_workflow_slug_idx", "columns": [{"expression": "workflow_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_task_slug_idx": {"name": "payload_jobs_task_slug_idx", "columns": [{"expression": "task_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_queue_idx": {"name": "payload_jobs_queue_idx", "columns": [{"expression": "queue", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_wait_until_idx": {"name": "payload_jobs_wait_until_idx", "columns": [{"expression": "wait_until", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_processing_idx": {"name": "payload_jobs_processing_idx", "columns": [{"expression": "processing", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_updated_at_idx": {"name": "payload_jobs_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_created_at_idx": {"name": "payload_jobs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents": {"name": "payload_locked_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "agencies_id": {"name": "agencies_id", "type": "integer", "primaryKey": false, "notNull": false}, "agents_id": {"name": "agents_id", "type": "integer", "primaryKey": false, "notNull": false}, "articles_id": {"name": "articles_id", "type": "integer", "primaryKey": false, "notNull": false}, "artist_deals_id": {"name": "artist_deals_id", "type": "integer", "primaryKey": false, "notNull": false}, "artists_id": {"name": "artists_id", "type": "integer", "primaryKey": false, "notNull": false}, "authors_id": {"name": "authors_id", "type": "integer", "primaryKey": false, "notNull": false}, "countries_id": {"name": "countries_id", "type": "integer", "primaryKey": false, "notNull": false}, "documents_id": {"name": "documents_id", "type": "integer", "primaryKey": false, "notNull": false}, "event_brands_id": {"name": "event_brands_id", "type": "integer", "primaryKey": false, "notNull": false}, "event_organizers_id": {"name": "event_organizers_id", "type": "integer", "primaryKey": false, "notNull": false}, "events_id": {"name": "events_id", "type": "integer", "primaryKey": false, "notNull": false}, "fan_notification_listners_id": {"name": "fan_notification_listners_id", "type": "integer", "primaryKey": false, "notNull": false}, "fan_users_id": {"name": "fan_users_id", "type": "integer", "primaryKey": false, "notNull": false}, "festival_profiles_id": {"name": "festival_profiles_id", "type": "integer", "primaryKey": false, "notNull": false}, "festivals_id": {"name": "festivals_id", "type": "integer", "primaryKey": false, "notNull": false}, "genres_id": {"name": "genres_id", "type": "integer", "primaryKey": false, "notNull": false}, "hubs_id": {"name": "hubs_id", "type": "integer", "primaryKey": false, "notNull": false}, "managers_id": {"name": "managers_id", "type": "integer", "primaryKey": false, "notNull": false}, "managment_companies_id": {"name": "managment_companies_id", "type": "integer", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "ocho_episodes_id": {"name": "ocho_episodes_id", "type": "integer", "primaryKey": false, "notNull": false}, "orders_id": {"name": "orders_id", "type": "integer", "primaryKey": false, "notNull": false}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "promotions_id": {"name": "promotions_id", "type": "integer", "primaryKey": false, "notNull": false}, "residencies_id": {"name": "residencies_id", "type": "integer", "primaryKey": false, "notNull": false}, "tickets_id": {"name": "tickets_id", "type": "integer", "primaryKey": false, "notNull": false}, "ticket_types_id": {"name": "ticket_types_id", "type": "integer", "primaryKey": false, "notNull": false}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "venues_id": {"name": "venues_id", "type": "integer", "primaryKey": false, "notNull": false}, "redirects_id": {"name": "redirects_id", "type": "integer", "primaryKey": false, "notNull": false}, "search_id": {"name": "search_id", "type": "integer", "primaryKey": false, "notNull": false}, "payload_jobs_id": {"name": "payload_jobs_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_agencies_id_idx": {"name": "payload_locked_documents_rels_agencies_id_idx", "columns": [{"expression": "agencies_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_agents_id_idx": {"name": "payload_locked_documents_rels_agents_id_idx", "columns": [{"expression": "agents_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_articles_id_idx": {"name": "payload_locked_documents_rels_articles_id_idx", "columns": [{"expression": "articles_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_artist_deals_id_idx": {"name": "payload_locked_documents_rels_artist_deals_id_idx", "columns": [{"expression": "artist_deals_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_artists_id_idx": {"name": "payload_locked_documents_rels_artists_id_idx", "columns": [{"expression": "artists_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_authors_id_idx": {"name": "payload_locked_documents_rels_authors_id_idx", "columns": [{"expression": "authors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_countries_id_idx": {"name": "payload_locked_documents_rels_countries_id_idx", "columns": [{"expression": "countries_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_documents_id_idx": {"name": "payload_locked_documents_rels_documents_id_idx", "columns": [{"expression": "documents_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_event_brands_id_idx": {"name": "payload_locked_documents_rels_event_brands_id_idx", "columns": [{"expression": "event_brands_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_event_organizers_id_idx": {"name": "payload_locked_documents_rels_event_organizers_id_idx", "columns": [{"expression": "event_organizers_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_events_id_idx": {"name": "payload_locked_documents_rels_events_id_idx", "columns": [{"expression": "events_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_fan_notification_listners_id_idx": {"name": "payload_locked_documents_rels_fan_notification_listners_id_idx", "columns": [{"expression": "fan_notification_listners_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_fan_users_id_idx": {"name": "payload_locked_documents_rels_fan_users_id_idx", "columns": [{"expression": "fan_users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_festival_profiles_id_idx": {"name": "payload_locked_documents_rels_festival_profiles_id_idx", "columns": [{"expression": "festival_profiles_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_festivals_id_idx": {"name": "payload_locked_documents_rels_festivals_id_idx", "columns": [{"expression": "festivals_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_genres_id_idx": {"name": "payload_locked_documents_rels_genres_id_idx", "columns": [{"expression": "genres_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_hubs_id_idx": {"name": "payload_locked_documents_rels_hubs_id_idx", "columns": [{"expression": "hubs_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_managers_id_idx": {"name": "payload_locked_documents_rels_managers_id_idx", "columns": [{"expression": "managers_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_managment_companies_id_idx": {"name": "payload_locked_documents_rels_managment_companies_id_idx", "columns": [{"expression": "managment_companies_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_ocho_episodes_id_idx": {"name": "payload_locked_documents_rels_ocho_episodes_id_idx", "columns": [{"expression": "ocho_episodes_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_orders_id_idx": {"name": "payload_locked_documents_rels_orders_id_idx", "columns": [{"expression": "orders_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_pages_id_idx": {"name": "payload_locked_documents_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_promotions_id_idx": {"name": "payload_locked_documents_rels_promotions_id_idx", "columns": [{"expression": "promotions_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_residencies_id_idx": {"name": "payload_locked_documents_rels_residencies_id_idx", "columns": [{"expression": "residencies_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_tickets_id_idx": {"name": "payload_locked_documents_rels_tickets_id_idx", "columns": [{"expression": "tickets_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_ticket_types_id_idx": {"name": "payload_locked_documents_rels_ticket_types_id_idx", "columns": [{"expression": "ticket_types_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_venues_id_idx": {"name": "payload_locked_documents_rels_venues_id_idx", "columns": [{"expression": "venues_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_redirects_id_idx": {"name": "payload_locked_documents_rels_redirects_id_idx", "columns": [{"expression": "redirects_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_search_id_idx": {"name": "payload_locked_documents_rels_search_id_idx", "columns": [{"expression": "search_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_payload_jobs_id_idx": {"name": "payload_locked_documents_rels_payload_jobs_id_idx", "columns": [{"expression": "payload_jobs_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_agencies_fk": {"name": "payload_locked_documents_rels_agencies_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "agencies", "columnsFrom": ["agencies_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_agents_fk": {"name": "payload_locked_documents_rels_agents_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "agents", "columnsFrom": ["agents_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_articles_fk": {"name": "payload_locked_documents_rels_articles_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "articles", "columnsFrom": ["articles_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_artist_deals_fk": {"name": "payload_locked_documents_rels_artist_deals_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "artist_deals", "columnsFrom": ["artist_deals_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_artists_fk": {"name": "payload_locked_documents_rels_artists_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "artists", "columnsFrom": ["artists_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_authors_fk": {"name": "payload_locked_documents_rels_authors_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "authors", "columnsFrom": ["authors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_countries_fk": {"name": "payload_locked_documents_rels_countries_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "countries", "columnsFrom": ["countries_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_documents_fk": {"name": "payload_locked_documents_rels_documents_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "documents", "columnsFrom": ["documents_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_event_brands_fk": {"name": "payload_locked_documents_rels_event_brands_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "event_brands", "columnsFrom": ["event_brands_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_event_organizers_fk": {"name": "payload_locked_documents_rels_event_organizers_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "event_organizers", "columnsFrom": ["event_organizers_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_events_fk": {"name": "payload_locked_documents_rels_events_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "events", "columnsFrom": ["events_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_fan_notification_listners_fk": {"name": "payload_locked_documents_rels_fan_notification_listners_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "fan_notification_listners", "columnsFrom": ["fan_notification_listners_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_fan_users_fk": {"name": "payload_locked_documents_rels_fan_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "fan_users", "columnsFrom": ["fan_users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_festival_profiles_fk": {"name": "payload_locked_documents_rels_festival_profiles_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "festival_profiles", "columnsFrom": ["festival_profiles_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_festivals_fk": {"name": "payload_locked_documents_rels_festivals_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "festivals", "columnsFrom": ["festivals_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_genres_fk": {"name": "payload_locked_documents_rels_genres_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "genres", "columnsFrom": ["genres_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_hubs_fk": {"name": "payload_locked_documents_rels_hubs_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "hubs", "columnsFrom": ["hubs_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_managers_fk": {"name": "payload_locked_documents_rels_managers_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "managers", "columnsFrom": ["managers_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_managment_companies_fk": {"name": "payload_locked_documents_rels_managment_companies_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "managment_companies", "columnsFrom": ["managment_companies_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_ocho_episodes_fk": {"name": "payload_locked_documents_rels_ocho_episodes_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "ocho_episodes", "columnsFrom": ["ocho_episodes_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_orders_fk": {"name": "payload_locked_documents_rels_orders_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "orders", "columnsFrom": ["orders_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_pages_fk": {"name": "payload_locked_documents_rels_pages_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_promotions_fk": {"name": "payload_locked_documents_rels_promotions_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "promotions", "columnsFrom": ["promotions_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_residencies_fk": {"name": "payload_locked_documents_rels_residencies_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "residencies", "columnsFrom": ["residencies_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_tickets_fk": {"name": "payload_locked_documents_rels_tickets_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "tickets", "columnsFrom": ["tickets_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_ticket_types_fk": {"name": "payload_locked_documents_rels_ticket_types_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "ticket_types", "columnsFrom": ["ticket_types_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_venues_fk": {"name": "payload_locked_documents_rels_venues_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "venues", "columnsFrom": ["venues_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_redirects_fk": {"name": "payload_locked_documents_rels_redirects_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "redirects", "columnsFrom": ["redirects_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_search_fk": {"name": "payload_locked_documents_rels_search_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "search", "columnsFrom": ["search_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_payload_jobs_fk": {"name": "payload_locked_documents_rels_payload_jobs_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_jobs", "columnsFrom": ["payload_jobs_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences": {"name": "payload_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_migrations": {"name": "payload_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.enum_articles_type": {"name": "enum_articles_type", "schema": "public", "values": ["Academy", "Magazine"]}, "public.enum_artist_deals_status_history_status": {"name": "enum_artist_deals_status_history_status", "schema": "public", "values": ["offerSent", "counterOffer", "declined", "accepted", "canceled"]}, "public.enum_artists_representation_coverage": {"name": "enum_artists_representation_coverage", "schema": "public", "values": ["global", "continent", "country"]}, "public.enum_artists_social_links_resource": {"name": "enum_artists_social_links_resource", "schema": "public", "values": ["Facebook", "Instagram", "TikTok", "SoundCloud", "Discord", "YouTube", "Twitter", "Twitch", "Pinterest", "Spotify", "BeatPort", "Website", "<PERSON><PERSON>", "TVMaze", "MusicBrainz", "Tunefind", "Line", "<PERSON><PERSON>", "Pandora", "<PERSON><PERSON><PERSON>", "Tidal", "LastFm", "<PERSON><PERSON>", "Songkick", "Bandsintown", "Discogs", "Itunes", "Amazon"]}, "public.enum_authors_social_links_resource": {"name": "enum_authors_social_links_resource", "schema": "public", "values": ["Facebook", "Instagram", "TikTok", "SoundCloud", "Discord", "YouTube", "Twitter", "Twitch", "Pinterest", "Spotify", "BeatPort", "Website", "<PERSON><PERSON>", "TVMaze", "MusicBrainz", "Tunefind", "Line", "<PERSON><PERSON>", "Pandora", "<PERSON><PERSON><PERSON>", "Tidal", "LastFm", "<PERSON><PERSON>", "Songkick", "Bandsintown", "Discogs", "Itunes", "Amazon"]}, "public.enum_event_brands_social_links_resource": {"name": "enum_event_brands_social_links_resource", "schema": "public", "values": ["Facebook", "Instagram", "TikTok", "SoundCloud", "Discord", "YouTube", "Twitter", "Twitch", "Pinterest", "Spotify", "BeatPort", "Website", "<PERSON><PERSON>", "TVMaze", "MusicBrainz", "Tunefind", "Line", "<PERSON><PERSON>", "Pandora", "<PERSON><PERSON><PERSON>", "Tidal", "LastFm", "<PERSON><PERSON>", "Songkick", "Bandsintown", "Discogs", "Itunes", "Amazon"]}, "public.enum_events_external_platform_source_urls_platform": {"name": "enum_events_external_platform_source_urls_platform", "schema": "public", "values": ["sanity", "dice", "ra", "songkick", "eventbrite"]}, "public.enum_events_lineup_tier": {"name": "enum_events_lineup_tier", "schema": "public", "values": ["Headliner", "SpecialGuest", "SupportI", "SupportII", "Resident", "Local", "JrLocal"]}, "public.enum_events_social_links_resource": {"name": "enum_events_social_links_resource", "schema": "public", "values": ["Facebook", "Instagram", "TikTok", "SoundCloud", "Discord", "YouTube", "Twitter", "Twitch", "Pinterest", "Spotify", "BeatPort", "Website", "<PERSON><PERSON>", "TVMaze", "MusicBrainz", "Tunefind", "Line", "<PERSON><PERSON>", "Pandora", "<PERSON><PERSON><PERSON>", "Tidal", "LastFm", "<PERSON><PERSON>", "Songkick", "Bandsintown", "Discogs", "Itunes", "Amazon"]}, "public.enum_events_marketing_tracking_links_platform": {"name": "enum_events_marketing_tracking_links_platform", "schema": "public", "values": ["sanity", "dice", "ra", "songkick", "eventbrite"]}, "public.enum_events_marketing_tracking_links_deals_params": {"name": "enum_events_marketing_tracking_links_deals_params", "schema": "public", "values": ["organic", "paid"]}, "public.enum_events_origin": {"name": "enum_events_origin", "schema": "public", "values": ["sanity", "dice", "ra", "songkick", "eventbrite"]}, "public.enum_events_location_location_type": {"name": "enum_events_location_location_type", "schema": "public", "values": ["venue", "hub"]}, "public.enum_events_timezone": {"name": "enum_events_timezone", "schema": "public", "values": ["Africa/Abidjan", "Africa/Accra", "Africa/Addis_Ababa", "Africa/Algiers", "Africa/Asmera", "Africa/Bamako", "Africa/Bangui", "Africa/Banjul", "Africa/Bissau", "Africa/Blantyre", "Africa/Brazzaville", "Africa/Bujumbura", "Africa/Cairo", "Africa/Casablanca", "Africa/Ceuta", "Africa/Conakry", "Africa/Dakar", "Africa/Dar_es_Salaam", "Africa/Djibouti", "Africa/Douala", "Africa/El_Aaiun", "Africa/Freetown", "Africa/Gaborone", "Africa/Harare", "Africa/Johannesburg", "Africa/Juba", "Africa/Kampala", "Africa/Khartoum", "Africa/Kigali", "Africa/Kinshasa", "Africa/Lagos", "Africa/Libreville", "Africa/Lome", "Africa/Luanda", "Africa/Lubumbashi", "Africa/Lusaka", "Africa/Malabo", "Africa/Maputo", "Africa/Maseru", "Africa/Mbabane", "Africa/Mogadishu", "Africa/Monrovia", "Africa/Nairobi", "Africa/Ndjamena", "Africa/Niamey", "Africa/Nouakchott", "Africa/Ouagadougou", "Africa/Porto-Novo", "Africa/Sao_Tome", "Africa/Tripoli", "Africa/Tunis", "Africa/Windhoek", "America/Adak", "America/Anchorage", "America/Anguilla", "America/Antigua", "America/Araguaina", "America/Argentina/La_Rioja", "America/Argentina/Rio_Gallegos", "America/Argentina/Salta", "America/Argentina/San_Juan", "America/Argentina/San_Luis", "America/Argentina/Tucuman", "America/Argentina/Ushuaia", "America/Aruba", "America/Asuncion", "America/Bahia", "America/Bahia_Banderas", "America/Barbados", "America/Belem", "America/Belize", "America/Blanc-Sablon", "America/Boa_Vista", "America/Bogota", "America/Boise", "America/Buenos_Aires", "America/Cambridge_Bay", "America/Campo_Grande", "America/Cancun", "America/Caracas", "America/Catamarca", "America/Cayenne", "America/Cayman", "America/Chicago", "America/Chihuahua", "America/Ciudad_Juarez", "America/Coral_Harbour", "America/Cordoba", "America/Costa_Rica", "America/Creston", "America/Cuiaba", "America/Curacao", "America/Danmarkshavn", "America/Dawson", "America/Dawson_Creek", "America/Denver", "America/Detroit", "America/Dominica", "America/Edmonton", "America/Eirunepe", "America/El_Salvador", "America/Fort_Nelson", "America/Fortaleza", "America/Glace_Bay", "America/Godthab", "America/Goose_Bay", "America/Grand_Turk", "America/Grenada", "America/Guadeloupe", "America/Guatemala", "America/Guayaquil", "America/Guyana", "America/Halifax", "America/Havana", "America/Hermosillo", "America/Indiana/Knox", "America/Indiana/Marengo", "America/Indiana/Petersburg", "America/Indiana/Tell_City", "America/Indiana/Vevay", "America/Indiana/Vincennes", "America/Indiana/Winamac", "America/Indianapolis", "America/Inuvik", "America/Iqaluit", "America/Jamaica", "America/Jujuy", "America/Juneau", "America/Kentucky/Monticello", "America/Kralendijk", "America/La_Paz", "America/Lima", "America/Los_Angeles", "America/Louisville", "America/Lower_Princes", "America/Maceio", "America/Managua", "America/Manaus", "America/Marigot", "America/Martinique", "America/Matamoros", "America/Mazatlan", "America/Mendoza", "America/Menominee", "America/Merida", "America/Metlakatla", "America/Mexico_City", "America/Miquelon", "America/Moncton", "America/Monterrey", "America/Montevideo", "America/Montserrat", "America/Nassau", "America/New_York", "America/Nome", "America/Noronha", "America/North_Dakota/Beulah", "America/North_Dakota/Center", "America/North_Dakota/New_Salem", "America/Ojinaga", "America/Panama", "America/Paramaribo", "America/Phoenix", "America/Port-au-Prince", "America/Port_of_Spain", "America/Porto_Velho", "America/Puerto_Rico", "America/Punta_Arenas", "America/Rankin_Inlet", "America/Recife", "America/Regina", "America/Resolute", "America/Rio_Branco", "America/Santarem", "America/Santiago", "America/Santo_Domingo", "America/Sao_Paulo", "America/Scoresbysund", "America/Sitka", "America/St_Barthelemy", "America/St_Johns", "America/St_Kitts", "America/St_Lucia", "America/St_Thomas", "America/St_Vincent", "America/Swift_Current", "America/Tegucigalpa", "America/Thule", "America/Tijuana", "America/Toronto", "America/Tortola", "America/Vancouver", "America/Whitehorse", "America/Winnipeg", "America/Yakutat", "Antarctica/Casey", "Antarctica/Davis", "Antarctica/DumontDUrville", "Antarctica/Macquarie", "Antarctica/Mawson", "Antarctica/McMurdo", "Antarctica/Palmer", "Antarctica/Rothera", "Antarctica/Syowa", "Antarctica/Troll", "Antarctica/Vostok", "Arctic/Longyearbyen", "Asia/Aden", "Asia/Almaty", "Asia/Amman", "Asia/Anadyr", "Asia/Aqtau", "Asia/Aqtobe", "Asia/Ashgabat", "Asia/Atyrau", "Asia/Baghdad", "Asia/Bahrain", "Asia/Baku", "Asia/Bangkok", "Asia/Barnaul", "Asia/Beirut", "Asia/Bishkek", "Asia/Brunei", "Asia/Calcutta", "Asia/Chita", "Asia/Colombo", "Asia/Damascus", "Asia/Dhaka", "Asia/Dili", "Asia/Dubai", "Asia/Dushanbe", "Asia/Famagusta", "Asia/Gaza", "Asia/Hebron", "Asia/Hong_Kong", "Asia/Hovd", "Asia/Irkutsk", "Asia/Jakarta", "Asia/Jayapura", "Asia/Jerusalem", "Asia/Kabul", "Asia/Kamchatka", "Asia/Karachi", "Asia/Katmandu", "Asia/Khandyga", "Asia/Krasnoyarsk", "Asia/Kuala_Lumpur", "Asia/Kuching", "Asia/Kuwait", "Asia/Macau", "Asia/Magadan", "Asia/Makassar", "Asia/Manila", "Asia/Muscat", "Asia/Nicosia", "Asia/Novokuznetsk", "Asia/Novosibirsk", "Asia/Omsk", "Asia/Oral", "Asia/Phnom_Penh", "Asia/Pontianak", "Asia/Pyongyang", "Asia/Qatar", "Asia/Qostanay", "Asia/Qyzylorda", "Asia/Rangoon", "Asia/Riyadh", "Asia/Saigon", "Asia/Sakhalin", "Asia/Samarkand", "Asia/Seoul", "Asia/Shanghai", "Asia/Singapore", "Asia/Srednekolymsk", "Asia/Taipei", "Asia/Tashkent", "Asia/Tbilisi", "Asia/Tehran", "Asia/Thimphu", "Asia/Tokyo", "Asia/Tomsk", "Asia/Ulaanbaatar", "Asia/Urumqi", "Asia/Ust-Nera", "Asia/Vientiane", "Asia/Vladivostok", "Asia/Yakutsk", "Asia/Yekaterinburg", "Asia/Yerevan", "Atlantic/Azores", "Atlantic/Bermuda", "Atlantic/Canary", "Atlantic/Cape_Verde", "Atlantic/Faeroe", "Atlantic/Madeira", "Atlantic/Reykjavik", "Atlantic/South_Georgia", "Atlantic/St_Helena", "Atlantic/Stanley", "Australia/Adelaide", "Australia/Brisbane", "Australia/Broken_Hill", "Australia/Darwin", "Australia/Eucla", "Australia/Hobart", "Australia/Lindeman", "Australia/Lord_Howe", "Australia/Melbourne", "Australia/Perth", "Australia/Sydney", "Europe/Amsterdam", "Europe/Andorra", "Europe/Astrakhan", "Europe/Athens", "Europe/Belgrade", "Europe/Berlin", "Europe/Bratislava", "Europe/Brussels", "Europe/Bucharest", "Europe/Budapest", "Europe/Busingen", "Europe/Chisinau", "Europe/Copenhagen", "Europe/Dublin", "Europe/Gibraltar", "Europe/Guernsey", "Europe/Helsinki", "Europe/Isle_of_Man", "Europe/Istanbul", "Europe/Jersey", "Europe/Kaliningrad", "Europe/Kiev", "Europe/Kirov", "Europe/Lisbon", "Europe/Ljubljana", "Europe/London", "Europe/Luxembourg", "Europe/Madrid", "Europe/Malta", "Europe/Mariehamn", "Europe/Minsk", "Europe/Monaco", "Europe/Moscow", "Europe/Oslo", "Europe/Paris", "Europe/Podgorica", "Europe/Prague", "Europe/Riga", "Europe/Rome", "Europe/Samara", "Europe/San_Marino", "Europe/Sarajevo", "Europe/Saratov", "Europe/Simferopol", "Europe/Skopje", "Europe/Sofia", "Europe/Stockholm", "Europe/Tallinn", "Europe/Tirane", "Europe/Ulyanovsk", "Europe/Vaduz", "Europe/Vatican", "Europe/Vienna", "Europe/Vilnius", "Europe/Volgograd", "Europe/Warsaw", "Europe/Zagreb", "Europe/Zurich", "Indian/Antananarivo", "Indian/Chagos", "Indian/Christmas", "Indian/Cocos", "Indian/Comoro", "Indian/Kerguelen", "Indian/Mahe", "Indian/Maldives", "Indian/Mauritius", "Indian/Mayotte", "Indian/Reunion", "Pacific/Apia", "Pacific/Auckland", "Pacific/Bougainville", "Pacific/Chatham", "Pacific/Easter", "Pacific/Efate", "Pacific/Enderbury", "Pacific/Fakaofo", "Pacific/Fiji", "Pacific/Funafuti", "Pacific/Galapagos", "Pacific/Gambier", "Pacific/Guadalcanal", "Pacific/Guam", "Pacific/Honolulu", "Pacific/Kiritimati", "Pacific/Kosrae", "Pacific/Kwajalein", "Pacific/Majuro", "Pacific/Marquesas", "Pacific/Midway", "Pacific/Nauru", "Pacific/Niue", "Pacific/Norfolk", "Pacific/Noumea", "Pacific/Pago_Pago", "Pacific/Palau", "Pacific/Pitcairn", "Pacific/Ponape", "Pacific/Port_Moresby", "Pacific/Rarotonga", "Pacific/Saipan", "Pacific/Tahiti", "Pacific/Tarawa", "Pacific/Tongatapu", "Pacific/Truk", "Pacific/Wake", "Pacific/Wallis"]}, "public.enum_fan_notification_listners_type": {"name": "enum_fan_notification_listners_type", "schema": "public", "values": ["eventPresale"]}, "public.enum_festival_profiles_social_links_resource": {"name": "enum_festival_profiles_social_links_resource", "schema": "public", "values": ["Facebook", "Instagram", "TikTok", "SoundCloud", "Discord", "YouTube", "Twitter", "Twitch", "Pinterest", "Spotify", "BeatPort", "Website", "<PERSON><PERSON>", "TVMaze", "MusicBrainz", "Tunefind", "Line", "<PERSON><PERSON>", "Pandora", "<PERSON><PERSON><PERSON>", "Tidal", "LastFm", "<PERSON><PERSON>", "Songkick", "Bandsintown", "Discogs", "Itunes", "Amazon"]}, "public.enum_festivals_social_links_resource": {"name": "enum_festivals_social_links_resource", "schema": "public", "values": ["Facebook", "Instagram", "TikTok", "SoundCloud", "Discord", "YouTube", "Twitter", "Twitch", "Pinterest", "Spotify", "BeatPort", "Website", "<PERSON><PERSON>", "TVMaze", "MusicBrainz", "Tunefind", "Line", "<PERSON><PERSON>", "Pandora", "<PERSON><PERSON><PERSON>", "Tidal", "LastFm", "<PERSON><PERSON>", "Songkick", "Bandsintown", "Discogs", "Itunes", "Amazon"]}, "public.enum_festivals_origin": {"name": "enum_festivals_origin", "schema": "public", "values": ["sanity", "dice", "ra", "songkick", "eventbrite"]}, "public.enum_genres_type": {"name": "enum_genres_type", "schema": "public", "values": ["Spotify", "GrayArea", "<PERSON><PERSON>", "ResidentAdvisor", "ChartMetric"]}, "public.enum_hubs_type": {"name": "enum_hubs_type", "schema": "public", "values": ["country", "region", "city"]}, "public.enum_orders_origin": {"name": "enum_orders_origin", "schema": "public", "values": ["sanity", "dice", "ra", "songkick", "eventbrite"]}, "public.enum_pages_blocks_cta_links_link_type": {"name": "enum_pages_blocks_cta_links_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_pages_blocks_cta_links_link_appearance": {"name": "enum_pages_blocks_cta_links_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum_pages_blocks_content_columns_size": {"name": "enum_pages_blocks_content_columns_size", "schema": "public", "values": ["oneThird", "half", "twoThirds", "full"]}, "public.enum_pages_blocks_content_columns_link_type": {"name": "enum_pages_blocks_content_columns_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_pages_blocks_content_columns_link_appearance": {"name": "enum_pages_blocks_content_columns_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum_pages_blocks_archive_populate_by": {"name": "enum_pages_blocks_archive_populate_by", "schema": "public", "values": ["collection", "selection"]}, "public.enum_pages_blocks_archive_relation_to": {"name": "enum_pages_blocks_archive_relation_to", "schema": "public", "values": ["pages"]}, "public.enum_pages_status": {"name": "enum_pages_status", "schema": "public", "values": ["draft", "published"]}, "public.enum__pages_v_blocks_cta_links_link_type": {"name": "enum__pages_v_blocks_cta_links_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum__pages_v_blocks_cta_links_link_appearance": {"name": "enum__pages_v_blocks_cta_links_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum__pages_v_blocks_content_columns_size": {"name": "enum__pages_v_blocks_content_columns_size", "schema": "public", "values": ["oneThird", "half", "twoThirds", "full"]}, "public.enum__pages_v_blocks_content_columns_link_type": {"name": "enum__pages_v_blocks_content_columns_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum__pages_v_blocks_content_columns_link_appearance": {"name": "enum__pages_v_blocks_content_columns_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum__pages_v_blocks_archive_populate_by": {"name": "enum__pages_v_blocks_archive_populate_by", "schema": "public", "values": ["collection", "selection"]}, "public.enum__pages_v_blocks_archive_relation_to": {"name": "enum__pages_v_blocks_archive_relation_to", "schema": "public", "values": ["pages"]}, "public.enum__pages_v_version_status": {"name": "enum__pages_v_version_status", "schema": "public", "values": ["draft", "published"]}, "public.enum_residencies_social_links_resource": {"name": "enum_residencies_social_links_resource", "schema": "public", "values": ["Facebook", "Instagram", "TikTok", "SoundCloud", "Discord", "YouTube", "Twitter", "Twitch", "Pinterest", "Spotify", "BeatPort", "Website", "<PERSON><PERSON>", "TVMaze", "MusicBrainz", "Tunefind", "Line", "<PERSON><PERSON>", "Pandora", "<PERSON><PERSON><PERSON>", "Tidal", "LastFm", "<PERSON><PERSON>", "Songkick", "Bandsintown", "Discogs", "Itunes", "Amazon"]}, "public.enum_ticket_types_origin": {"name": "enum_ticket_types_origin", "schema": "public", "values": ["sanity", "dice", "ra", "songkick", "eventbrite"]}, "public.enum_users_roles": {"name": "enum_users_roles", "schema": "public", "values": ["super-admin", "user", "read-only"]}, "public.enum_users_tenants_roles": {"name": "enum_users_tenants_roles", "schema": "public", "values": ["tenant-admin", "tenant-viewer"]}, "public.enum_venues_internal_contacts_type": {"name": "enum_venues_internal_contacts_type", "schema": "public", "values": ["Management", "Marketing", "Advancing", "Production", "Ticketing", "Finance"]}, "public.enum_venues_social_links_resource": {"name": "enum_venues_social_links_resource", "schema": "public", "values": ["Facebook", "Instagram", "TikTok", "SoundCloud", "Discord", "YouTube", "Twitter", "Twitch", "Pinterest", "Spotify", "BeatPort", "Website", "<PERSON><PERSON>", "TVMaze", "MusicBrainz", "Tunefind", "Line", "<PERSON><PERSON>", "Pandora", "<PERSON><PERSON><PERSON>", "Tidal", "LastFm", "<PERSON><PERSON>", "Songkick", "Bandsintown", "Discogs", "Itunes", "Amazon"]}, "public.enum_venues_origin": {"name": "enum_venues_origin", "schema": "public", "values": ["sanity", "dice", "ra", "songkick", "eventbrite"]}, "public.enum_redirects_to_type": {"name": "enum_redirects_to_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_payload_jobs_log_task_slug": {"name": "enum_payload_jobs_log_task_slug", "schema": "public", "values": ["inline", "importDiceEvent", "extractEventUrls", "getSitemapUrls", "qflowLogin", "qflowFetchEvents", "qflowSyncAttendees", "fetchEventIds", "fetchEventWithTicketTypes", "upsertTicketTypes", "fetchOrders", "upsertTickets", "getConcertSitemapUrls", "collectConcertUrls", "fetchHtmlAndSave", "saveRawSanityData", "importSanityData", "handleSongkickEvent", "extractKeysByBucket", "extractLdJson", "extractRetailerId", "findOrCreateEvent", "findOrCreateTicket", "createOrder", "importPlanetscaleUser", "importPlanetscalePresaleRegistration", "schedulePublish"]}, "public.enum_payload_jobs_log_state": {"name": "enum_payload_jobs_log_state", "schema": "public", "values": ["failed", "succeeded"]}, "public.enum_payload_jobs_workflow_slug": {"name": "enum_payload_jobs_workflow_slug", "schema": "public", "values": ["crawlSanity", "crawlRAWorkflow", "crawlSongkickWorkflow", "crawlDicekWorkflow", "importSanityDataWorkflow", "syncDiceTicketTypesWorkflow", "syncDicePartnerEventsWorkflow", "syncDiceOrdersWorkflow", "syncDiceWorkflow", "syncQFlowWorkflow", "syncSongkickWorkflow", "syncPlanetscaleUsersWorkflow", "syncPlanetscalePresaleRegistrationsWorkflow"]}, "public.enum_payload_jobs_task_slug": {"name": "enum_payload_jobs_task_slug", "schema": "public", "values": ["inline", "importDiceEvent", "extractEventUrls", "getSitemapUrls", "qflowLogin", "qflowFetchEvents", "qflowSyncAttendees", "fetchEventIds", "fetchEventWithTicketTypes", "upsertTicketTypes", "fetchOrders", "upsertTickets", "getConcertSitemapUrls", "collectConcertUrls", "fetchHtmlAndSave", "saveRawSanityData", "importSanityData", "handleSongkickEvent", "extractKeysByBucket", "extractLdJson", "extractRetailerId", "findOrCreateEvent", "findOrCreateTicket", "createOrder", "importPlanetscaleUser", "importPlanetscalePresaleRegistration", "schedulePublish"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}