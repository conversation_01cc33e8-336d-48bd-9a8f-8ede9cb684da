import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TYPE "public"."enum_genres_type" ADD VALUE 'ChartMetric';`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "public"."genres" ALTER COLUMN "type" SET DATA TYPE text;
  DROP TYPE "public"."enum_genres_type";
  CREATE TYPE "public"."enum_genres_type" AS ENUM('Spotify', 'Gray<PERSON>rea', 'Dice', 'ResidentAdvisor');
  ALTER TABLE "public"."genres" ALTER COLUMN "type" SET DATA TYPE "public"."enum_genres_type" USING "type"::"public"."enum_genres_type";`)
}
