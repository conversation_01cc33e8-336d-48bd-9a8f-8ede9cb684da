import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_artists_representation_coverage" AS ENUM('global', 'continent', 'country');
  CREATE TABLE IF NOT EXISTS "artists_representation" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"territory_id" integer NOT NULL,
  	"coverage" "enum_artists_representation_coverage" DEFAULT 'global',
  	"agency_id" integer NOT NULL,
  	"agent_id" integer,
  	"management_company_id" integer,
  	"manager_id" integer
  );
  
  ALTER TABLE "artists_rels" DROP CONSTRAINT "artists_rels_agencies_fk";
  
  ALTER TABLE "artists_rels" DROP CONSTRAINT "artists_rels_agents_fk";
  
  DROP INDEX IF EXISTS "artists_rels_agencies_id_idx";
  DROP INDEX IF EXISTS "artists_rels_agents_id_idx";
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_territory_id_countries_id_fk" FOREIGN KEY ("territory_id") REFERENCES "public"."countries"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_agency_id_agencies_id_fk" FOREIGN KEY ("agency_id") REFERENCES "public"."agencies"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_agent_id_agents_id_fk" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_management_company_id_managment_companies_id_fk" FOREIGN KEY ("management_company_id") REFERENCES "public"."managment_companies"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_manager_id_managers_id_fk" FOREIGN KEY ("manager_id") REFERENCES "public"."managers"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."artists"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "artists_representation_order_idx" ON "artists_representation" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "artists_representation_parent_id_idx" ON "artists_representation" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_territory_idx" ON "artists_representation" USING btree ("territory_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_agency_idx" ON "artists_representation" USING btree ("agency_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_agent_idx" ON "artists_representation" USING btree ("agent_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_management_company_idx" ON "artists_representation" USING btree ("management_company_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_manager_idx" ON "artists_representation" USING btree ("manager_id");
  ALTER TABLE "artists_rels" DROP COLUMN IF EXISTS "agencies_id";
  ALTER TABLE "artists_rels" DROP COLUMN IF EXISTS "agents_id";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "artists_representation" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "artists_representation" CASCADE;
  ALTER TABLE "artists_rels" ADD COLUMN "agencies_id" integer;
  ALTER TABLE "artists_rels" ADD COLUMN "agents_id" integer;
  DO $$ BEGIN
   ALTER TABLE "artists_rels" ADD CONSTRAINT "artists_rels_agencies_fk" FOREIGN KEY ("agencies_id") REFERENCES "public"."agencies"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_rels" ADD CONSTRAINT "artists_rels_agents_fk" FOREIGN KEY ("agents_id") REFERENCES "public"."agents"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "artists_rels_agencies_id_idx" ON "artists_rels" USING btree ("agencies_id");
  CREATE INDEX IF NOT EXISTS "artists_rels_agents_id_idx" ON "artists_rels" USING btree ("agents_id");
  DROP TYPE "public"."enum_artists_representation_coverage";`)
}
