import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "orders" ADD COLUMN "fan_id" integer;
  DO $$ BEGIN
   ALTER TABLE "orders" ADD CONSTRAINT "orders_fan_id_fan_users_id_fk" FOREIGN KEY ("fan_id") REFERENCES "public"."fan_users"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "orders_fan_idx" ON "orders" USING btree ("fan_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "orders" DROP CONSTRAINT "orders_fan_id_fan_users_id_fk";
  
  DROP INDEX IF EXISTS "orders_fan_idx";
  ALTER TABLE "orders" DROP COLUMN IF EXISTS "fan_id";`)
}
