import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "promotions" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar NOT NULL,
  	"start_date" timestamp(3) with time zone NOT NULL,
  	"end_date" timestamp(3) with time zone NOT NULL,
  	"max_redemptions" numeric DEFAULT 0 NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  ALTER TABLE "events_rels" ADD COLUMN "promotions_id" integer;
  ALTER TABLE "promo_codes" ADD COLUMN "code" varchar NOT NULL;
  ALTER TABLE "promo_codes" ADD COLUMN "promotion_id" integer NOT NULL;
  ALTER TABLE "payload_locked_documents_rels" ADD COLUMN "promotions_id" integer;
  CREATE INDEX IF NOT EXISTS "promotions_updated_at_idx" ON "promotions" USING btree ("updated_at");
  CREATE INDEX IF NOT EXISTS "promotions_created_at_idx" ON "promotions" USING btree ("created_at");
  DO $$ BEGIN
   ALTER TABLE "events_rels" ADD CONSTRAINT "events_rels_promotions_fk" FOREIGN KEY ("promotions_id") REFERENCES "public"."promotions"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "promo_codes" ADD CONSTRAINT "promo_codes_promotion_id_promotions_id_fk" FOREIGN KEY ("promotion_id") REFERENCES "public"."promotions"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_promotions_fk" FOREIGN KEY ("promotions_id") REFERENCES "public"."promotions"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "events_rels_promotions_id_idx" ON "events_rels" USING btree ("promotions_id");
  CREATE INDEX IF NOT EXISTS "promo_codes_promotion_idx" ON "promo_codes" USING btree ("promotion_id");
  CREATE INDEX IF NOT EXISTS "payload_locked_documents_rels_promotions_id_idx" ON "payload_locked_documents_rels" USING btree ("promotions_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "promotions" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "promotions" CASCADE;
  ALTER TABLE "events_rels" DROP CONSTRAINT "events_rels_promotions_fk";
  
  ALTER TABLE "promo_codes" DROP CONSTRAINT "promo_codes_promotion_id_promotions_id_fk";
  
  ALTER TABLE "payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_promotions_fk";
  
  DROP INDEX IF EXISTS "events_rels_promotions_id_idx";
  DROP INDEX IF EXISTS "promo_codes_promotion_idx";
  DROP INDEX IF EXISTS "payload_locked_documents_rels_promotions_id_idx";
  ALTER TABLE "events_rels" DROP COLUMN IF EXISTS "promotions_id";
  ALTER TABLE "promo_codes" DROP COLUMN IF EXISTS "code";
  ALTER TABLE "promo_codes" DROP COLUMN IF EXISTS "promotion_id";
  ALTER TABLE "payload_locked_documents_rels" DROP COLUMN IF EXISTS "promotions_id";`)
}
