import { MigrateDownArgs, MigrateUpArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "agents" ADD COLUMN IF NOT EXISTS "automated_reporting_enabled" boolean DEFAULT false;
  ALTER TABLE "managers" ADD COLUMN IF NOT EXISTS "automated_reporting_enabled" boolean DEFAULT false;
  ALTER TABLE "ticket_types" ADD COLUMN IF NOT EXISTS "internal_presentation_only" boolean DEFAULT false;`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "agents" DROP COLUMN IF EXISTS "automated_reporting_enabled";
  ALTER TABLE "managers" DROP COLUMN IF EXISTS "automated_reporting_enabled";
  ALTER TABLE "ticket_types" DROP COLUMN IF EXISTS "internal_presentation_only";`)
}
