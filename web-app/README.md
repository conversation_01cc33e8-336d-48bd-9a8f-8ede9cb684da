# GreyArea.co Web App

#### Install
```bash
pnpm i
```

## Production Mode
#### Run Nextjs Prod Mode
Cannot run in production mode if the dev mode already connected to the postgres DB. need to delete the postgres data by ```docker compose down -v```
```bash
docker compose up --build  
```


## Dev Mode

##### Start Storage Services
```bash
docker compose up postgres minio createbuckets
```
##### Run Nextjs Dev Mode
```bash
pnpm run dev
```