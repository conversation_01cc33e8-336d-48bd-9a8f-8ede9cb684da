import { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  schema: [
    {
      'https://partners-endpoint.dice.fm/graphql': {
        headers: {
          Authorization: `Bearer ${process.env.DICE_PARTNER_API_TOKEN}`,
        },
      },
    },
  ],
  documents: ['src/lib/dice/operations/**/*.graphql'],
  generates: {
    'src/lib/dice/schema-types.ts': {
      plugins: ['typescript'],
    },
    'src/lib/dice/graphql-types.ts': {
      plugins: [
        'typescript',
        'typescript-operations',
        'typescript-graphql-request',
      ],
      config: {
        rawRequest: false,
      },
    },
  },
};

export default config;
