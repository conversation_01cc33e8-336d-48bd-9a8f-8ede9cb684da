import { CodegenConfig } from '@graphql-codegen/cli';
    
const config: CodegenConfig = {
  schema: 'src/lib/dice-p/schema.graphql',
  documents: ['src/lib/dice-p/operations/**/*.graphql'],
  generates: {
    'src/lib/dice-p/schema-types.ts': {
      plugins: ['typescript', 'typescript-operations', 'typescript-graphql-request'],
    },
  },
  config: {
    namingConvention:{
        enumValues: 'keep'
    }
  }
};

export default config;
